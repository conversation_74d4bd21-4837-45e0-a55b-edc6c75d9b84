// Test product data structure
const axios = require('axios');

async function testProductData() {
  try {
    console.log('🧪 Testing Product Data Structure...\n');
    
    const response = await axios.get('http://localhost:3001/api/products');
    const products = response.data.data.products;
    
    console.log('📊 Sample Product Data:');
    console.log('Total products:', products.length);
    
    if (products.length > 0) {
      const sampleProduct = products[0];
      console.log('\n📦 Sample Product Structure:');
      console.log('ID:', sampleProduct.id);
      console.log('Name:', sampleProduct.name);
      console.log('Price:', sampleProduct.price, '(Type:', typeof sampleProduct.price, ')');
      console.log('Unit:', sampleProduct.unit);
      console.log('Stock:', sampleProduct.stock);
      console.log('Category:', sampleProduct.Category?.name);
      console.log('Farmer:', sampleProduct.Farmer?.farmName);
      
      console.log('\n💰 Price Formatting Test:');
      console.log('Raw price:', sampleProduct.price);
      console.log('parseFloat(price):', parseFloat(sampleProduct.price));
      console.log('Formatted price:', `${parseFloat(sampleProduct.price || 0).toLocaleString()} FCFA`);
    }
    
    console.log('\n✅ Product data structure verified!');
    
  } catch (error) {
    console.error('❌ Error testing product data:', error.message);
  }
}

testProductData();
