// Test cart and wishlist functionality
const axios = require('axios');

async function testCartAndWishlist() {
  console.log('🧪 Testing Cart and Wishlist Functionality...\n');
  
  try {
    // First, login to get a token
    console.log('🔐 Logging in as customer...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'Test123!'
    });
    
    const token = loginResponse.data.data.accessToken;
    console.log('✅ Login successful\n');
    
    // Get products to test with
    console.log('📦 Getting products...');
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products;
    console.log(`✅ Found ${products.length} products\n`);
    
    if (products.length === 0) {
      console.log('❌ No products found to test with');
      return;
    }
    
    const testProduct = products[0];
    console.log(`🎯 Testing with product: ${testProduct.name} (ID: ${testProduct.id})\n`);
    
    // Test Add to Cart
    console.log('🛒 Testing Add to Cart...');
    try {
      const cartResponse = await axios.post('http://localhost:3001/api/cart/add', {
        productId: testProduct.id,
        quantity: 2
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ Add to cart successful:', cartResponse.data);
    } catch (cartError) {
      console.log('❌ Add to cart failed:', cartError.response?.data || cartError.message);
    }
    
    // Test Get Cart
    console.log('\n🛒 Testing Get Cart...');
    try {
      const getCartResponse = await axios.get('http://localhost:3001/api/cart', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ Get cart successful:', getCartResponse.data);
    } catch (getCartError) {
      console.log('❌ Get cart failed:', getCartError.response?.data || getCartError.message);
    }
    
    // Test Add to Wishlist
    console.log('\n❤️ Testing Add to Wishlist...');
    try {
      const wishlistResponse = await axios.post('http://localhost:3001/api/wishlist/add', {
        productId: testProduct.id
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ Add to wishlist successful:', wishlistResponse.data);
    } catch (wishlistError) {
      console.log('❌ Add to wishlist failed:', wishlistError.response?.data || wishlistError.message);
    }
    
    // Test Get Wishlist
    console.log('\n❤️ Testing Get Wishlist...');
    try {
      const getWishlistResponse = await axios.get('http://localhost:3001/api/wishlist', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ Get wishlist successful:', getWishlistResponse.data);
    } catch (getWishlistError) {
      console.log('❌ Get wishlist failed:', getWishlistError.response?.data || getWishlistError.message);
    }
    
    console.log('\n🎉 Cart and Wishlist tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testCartAndWishlist();
