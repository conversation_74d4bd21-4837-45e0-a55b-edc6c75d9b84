const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const { Sequelize } = require('sequelize');

// Initialize Sequelize
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: path.join(__dirname, '../database.sqlite'),
  logging: false
});

const insertCategories = async () => {
  try {
    console.log('🌱 Connecting to database...');
    await sequelize.authenticate();
    console.log('✅ Database connected');

    const categories = [
      { name: 'Cereals & Grains', description: 'Maize, rice, millet, sorghum, wheat, fonio, teff' },
      { name: 'Root Crops & Tubers', description: 'Cassava, yam, sweet potato, Irish potato, cocoyam, plantain' },
      { name: 'Major Cash Crops', description: 'Cocoa, coffee, cotton, rubber, palm oil, sugarcane, tobacco, tea' },
      { name: 'Tropical Fruits', description: 'Banana, pineapple, mango, papaya, avocado, citrus fruits, guava' },
      { name: 'Indigenous/Wild Fruits', description: 'Bush mango, African cherry, velvet tamarind, African pear' },
      { name: 'Leafy Greens', description: 'Spinach, lettuce, cabbage, bitter leaf, water leaf, pumpkin leaves' },
      { name: 'Fruit Vegetables', description: 'Tomato, pepper, eggplant, okra, cucumber, zucchini, pumpkin' },
      { name: 'Other Vegetables', description: 'Onion, garlic, carrot, beetroot, green beans, celery' },
      { name: 'Legumes & Pulses', description: 'Groundnuts, cowpea, beans, soybeans, bambara groundnut' },
      { name: 'Spices & Seasonings', description: 'Ginger, turmeric, pepper, njangsa seeds, eru seeds' },
      { name: 'Herbs & Aromatics', description: 'Basil, lemongrass, mint, rosemary, thyme, oregano' },
      { name: 'Tree Crops & Nuts', description: 'Oil palm, coconut, kola nut, African walnut, cashew, shea nut' },
      { name: 'Livestock - Ruminants', description: 'Cattle, goat, sheep, buffalo' },
      { name: 'Livestock - Monogastrics', description: 'Pig, poultry, duck, guinea fowl, rabbit, grasscutter' },
      { name: 'Aquaculture & Fisheries', description: 'Tilapia, catfish, carp, marine fish, shrimp, oysters' },
      { name: 'Medicinal & Aromatic Plants', description: 'Moringa, aloe vera, neem, artemisia, hibiscus' },
      { name: 'Fiber Crops', description: 'Cotton, sisal, jute, kenaf, bamboo, raffia palm' },
      { name: 'Beverage Crops', description: 'Coffee, tea, cocoa, kola nut, hibiscus, ginger drinks' },
      { name: 'Oil Crops', description: 'Oil palm, groundnut, soybean, sesame, sunflower, castor bean' },
      { name: 'Mushrooms & Fungi', description: 'Oyster mushrooms, shiitake, wild edible mushrooms' },
      { name: 'Beekeeping Products', description: 'Honey, beeswax, propolis, royal jelly' },
      { name: 'Forestry Products', description: 'Rubber trees, teak, eucalyptus, fruit tree timber' },
      { name: 'Flowers & Ornamentals', description: 'Cut flowers, ornamental plants, landscaping plants' }
    ];

    // Check existing categories
    const [existingCategories] = await sequelize.query('SELECT name FROM Categories');
    const existingNames = existingCategories.map(cat => cat.name);
    
    console.log(`📊 Found ${existingNames.length} existing categories:`, existingNames);

    // Filter new categories
    const categoriesToAdd = categories.filter(cat => !existingNames.includes(cat.name));
    
    console.log(`➕ Adding ${categoriesToAdd.length} new categories...`);

    if (categoriesToAdd.length > 0) {
      for (const category of categoriesToAdd) {
        const id = require('crypto').randomUUID();
        const now = new Date().toISOString();
        
        await sequelize.query(`
          INSERT INTO Categories (id, name, description, isActive, createdAt, updatedAt)
          VALUES (?, ?, ?, ?, ?, ?)
        `, {
          replacements: [id, category.name, category.description, true, now, now]
        });
        
        console.log(`✅ Added: ${category.name}`);
      }
    } else {
      console.log('ℹ️ All categories already exist');
    }

    // Get final count
    const [finalCategories] = await sequelize.query('SELECT COUNT(*) as count FROM Categories');
    console.log(`🎯 Total categories in database: ${finalCategories[0].count}`);

    await sequelize.close();
    console.log('✅ Categories insertion completed!');

  } catch (error) {
    console.error('❌ Error inserting categories:', error);
    process.exit(1);
  }
};

insertCategories();
