const vendorService = require('../../business/services/vendorService');
const User = require('../../data/models/user');

exports.registerVendor = async (req, res, next) => {
  try {
    const vendor = await vendorService.registerVendor(req.user.id, req.body);
    res.status(201).json(vendor);
  } catch (error) {
    next(error);
  }
};

exports.getVendorProfile = async (req, res, next) => {
  try {
    const profile = await vendorService.getVendorProfile(req.user.id);
    res.json(profile);
  } catch (error) {
    next(error);
  }
};

exports.updateVendorProfile = async (req, res, next) => {
  try {
    const updatedProfile = await vendorService.updateVendorProfile(req.user.id, req.body);
    res.json(updatedProfile);
  } catch (error) {
    next(error);
  }
};

exports.createProductListing = async (req, res, next) => {
  try {
    const listing = await vendorService.createProductListing(req.user.id, req.body);
    res.status(201).json(listing);
  } catch (error) {
    next(error);
  }
};

exports.updateProductListing = async (req, res, next) => {
  try {
    const updatedListing = await vendorService.updateProductListing(req.user.id, req.params.id, req.body);
    res.json(updatedListing);
  } catch (error) {
    next(error);
  }
};

exports.deleteProductListing = async (req, res, next) => {
  try {
    await vendorService.deleteProductListing(req.user.id, req.params.id);
    res.status(204).end();
  } catch (error) {
    next(error);
  }
};

exports.getVendorListings = async (req, res, next) => {
  try {
    const listings = await vendorService.getVendorListings(req.user.id);
    res.json(listings);
  } catch (error) {
    next(error);
  }
};

// New function for uploading product image
exports.uploadProductImage = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const productId = req.params.id;
    const imageUrl = `/uploads/${req.file.filename}`;

    const updatedProduct = await vendorService.uploadProductImage(req.user.id, productId, imageUrl);

    res.json({ message: 'Image uploaded successfully', product: updatedProduct });
  } catch (error) {
    next(error);
  }
};

// New function for getting vendor dashboard
exports.getDashboard = async (req, res, next) => {
  try {
    const dashboard = await vendorService.getVendorDashboard(req.user.id);
    res.json(dashboard);
  } catch (error) {
    next(error);
  }
};

// New function for creating a product
exports.createProduct = async (req, res, next) => {
  try {
    const product = await vendorService.createProduct(req.user.id, req.body);
    res.status(201).json(product);
  } catch (error) {
    next(error);
  }
};

// New function for deleting vendor account
exports.deleteAccount = async (req, res, next) => {
  try {
    await vendorService.deleteVendorAccount(req.user.id);
    res.json({ message: 'Vendor account deleted successfully' });
  } catch (error) {
    next(error);
  }
};

// New function for deleting vendor account
exports.listVendor = async (req, res, next) => {
  try {
    const user = await User.findAll({
      where: { role: "vendor" },
 
    });


    res.json(user);
  } catch (error) {
    next(error);
  }
};