// Import required modules
const nodemailer = require('nodemailer');
const config = require('../config/config');

// Create a transporter using SMTP
const transporter = nodemailer.createTransport({
  host: config.emailHost,
  port: config.emailPort,
  secure: config.emailSecure,
  auth: {
    user: config.emailUser,
    pass: config.emailPass
  }
});

// Define the EmailService class
class EmailService {
  // Method to send an email
  async sendEmail(to, subject, text, html) {
    try {
      // Define email options
      const mailOptions = {
        from: config.emailFrom,
        to,
        subject,
        text,
        html
      };

      // Send the email
      const info = await transporter.sendMail(mailOptions);
      console.log('Email sent: ', info.messageId);
      return info;
    } catch (error) {
      console.error('Error sending email: ', error);
      throw error;
    }
  }

  // Method to send a welcome email
  async sendWelcomeEmail(user) {
    const subject = 'Welcome to our platform!';
    const text = `Hi ${user.name}, welcome to our platform. We're glad to have you on board!`;
    const html = `<h1>Welcome, ${user.name}!</h1><p>We're glad to have you on board!</p>`;

    return this.sendEmail(user.email, subject, text, html);
  }

  // Method to send an order confirmation email
  async sendOrderConfirmationEmail(user, order) {
    const subject = `Order Confirmation - Order #${order.id}`;
    const text = `Hi ${user.name}, your order #${order.id} has been confirmed. Total: $${order.total}`;
    const html = `<h1>Order Confirmed</h1><p>Hi ${user.name}, your order #${order.id} has been confirmed.</p><p>Total: $${order.total}</p>`;

    return this.sendEmail(user.email, subject, text, html);
  }
}

// Export the EmailService
module.exports = new EmailService();