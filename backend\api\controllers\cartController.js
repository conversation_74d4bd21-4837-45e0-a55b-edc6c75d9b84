const { Cart, CartItem, Product, Category, User, Farmer } = require('../../data/models');

exports.getCart = async (req, res, next) => {
  try {
    let cart = await Cart.findOne({
      where: { userId: req.user.id },
      include: [
        {
          model: CartItem,
          include: [
            {
              model: Product,
              include: [
                {
                  model: Category,
                  attributes: ['id', 'name']
                },
                {
                  model: Farmer,
                  include: [
                    {
                      model: User,
                      attributes: ['id', 'firstName', 'lastName']
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    });

    // Create cart if it doesn't exist
    if (!cart) {
      cart = await Cart.create({
        userId: req.user.id,
        total: 0
      });
      cart.CartItems = [];
    }

    // Calculate totals
    const subtotal = cart.CartItems ? cart.CartItems.reduce((sum, item) => {
      return sum + (item.quantity * item.price);
    }, 0) : 0;

    // Update cart total
    if (cart.total !== subtotal) {
      await cart.update({ total: subtotal });
    }

    res.json({
      success: true,
      data: {
        cart: {
          id: cart.id,
          userId: cart.userId,
          total: subtotal,
          itemCount: cart.CartItems ? cart.CartItems.length : 0,
          items: cart.CartItems || []
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

exports.addToCart = async (req, res, next) => {
  try {
    const { productId, quantity = 1 } = req.body;

    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    // Check if product exists and is available
    const product = await Product.findByPk(productId, {
      include: [
        {
          model: Category,
          attributes: ['id', 'name']
        },
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id', 'firstName', 'lastName']
            }
          ]
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    if (product.status !== 'ACTIVE') {
      return res.status(400).json({
        success: false,
        message: 'Product is not available'
      });
    }

    if (product.stock < quantity) {
      return res.status(400).json({
        success: false,
        message: `Only ${product.stock} items available in stock`
      });
    }

    // Get or create cart
    let cart = await Cart.findOne({
      where: { userId: req.user.id }
    });

    if (!cart) {
      cart = await Cart.create({
        userId: req.user.id,
        total: 0
      });
    }

    // Check if item already exists in cart
    let cartItem = await CartItem.findOne({
      where: {
        cartId: cart.id,
        productId: productId
      }
    });

    if (cartItem) {
      // Update quantity
      const newQuantity = cartItem.quantity + quantity;

      if (product.stock < newQuantity) {
        return res.status(400).json({
          success: false,
          message: `Cannot add ${quantity} more items. Only ${product.stock - cartItem.quantity} more available.`
        });
      }

      cartItem.quantity = newQuantity;
      await cartItem.save();
    } else {
      // Create new cart item
      cartItem = await CartItem.create({
        cartId: cart.id,
        productId: productId,
        quantity,
        price: product.price
      });
    }

    // Get updated cart with all details
    const updatedCart = await Cart.findOne({
      where: { userId: req.user.id },
      include: [
        {
          model: CartItem,
          include: [
            {
              model: Product,
              include: [
                {
                  model: Category,
                  attributes: ['id', 'name']
                },
                {
                  model: Farmer,
                  include: [
                    {
                      model: User,
                      attributes: ['id', 'firstName', 'lastName']
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    });

    // Calculate and update total
    const subtotal = updatedCart.CartItems.reduce((sum, item) => {
      return sum + (item.quantity * item.price);
    }, 0);

    await cart.update({ total: subtotal });

    res.json({
      success: true,
      message: 'Item added to cart successfully',
      data: {
        cart: {
          id: updatedCart.id,
          userId: updatedCart.userId,
          total: subtotal,
          itemCount: updatedCart.CartItems.length,
          items: updatedCart.CartItems
        },
        addedItem: cartItem
      }
    });
  } catch (error) {
    next(error);
  }
};

exports.updateCartItem = async (req, res, next) => {
  try {
    const { itemId } = req.params;
    const { quantity } = req.body;

    if (!quantity || quantity < 1) {
      return res.status(400).json({
        success: false,
        message: 'Quantity must be at least 1'
      });
    }

    const cartItem = await CartItem.findByPk(itemId, {
      include: [
        {
          model: Product,
          include: [
            {
              model: Category,
              attributes: ['id', 'name']
            },
            {
              model: Farmer,
              include: [
                {
                  model: User,
                  attributes: ['id', 'firstName', 'lastName']
                }
              ]
            }
          ]
        }
      ]
    });

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        message: 'Cart item not found'
      });
    }

    // Check if the cart item belongs to the user
    const cart = await Cart.findByPk(cartItem.cartId);
    if (cart.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized access to cart item'
      });
    }

    // Check stock availability
    if (cartItem.Product.stock < quantity) {
      return res.status(400).json({
        success: false,
        message: `Only ${cartItem.Product.stock} items available in stock`
      });
    }

    // Update quantity
    cartItem.quantity = quantity;
    await cartItem.save();

    // Get updated cart and recalculate total
    const updatedCart = await Cart.findOne({
      where: { userId: req.user.id },
      include: [
        {
          model: CartItem,
          include: [
            {
              model: Product,
              include: [
                {
                  model: Category,
                  attributes: ['id', 'name']
                },
                {
                  model: Farmer,
                  include: [
                    {
                      model: User,
                      attributes: ['id', 'firstName', 'lastName']
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    });

    const subtotal = updatedCart.CartItems.reduce((sum, item) => {
      return sum + (item.quantity * item.price);
    }, 0);

    await cart.update({ total: subtotal });

    res.json({
      success: true,
      message: 'Cart item updated successfully',
      data: {
        cart: {
          id: updatedCart.id,
          userId: updatedCart.userId,
          total: subtotal,
          itemCount: updatedCart.CartItems.length,
          items: updatedCart.CartItems
        },
        updatedItem: cartItem
      }
    });
  } catch (error) {
    next(error);
  }
};

exports.removeFromCart = async (req, res, next) => {
  try {
    const { itemId } = req.params;

    const cartItem = await CartItem.findByPk(itemId);

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        message: 'Cart item not found'
      });
    }

    // Check if the cart item belongs to the user
    const cart = await Cart.findByPk(cartItem.cartId);
    if (cart.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized access to cart item'
      });
    }

    // Remove the item
    await cartItem.destroy();

    // Get updated cart and recalculate total
    const updatedCart = await Cart.findOne({
      where: { userId: req.user.id },
      include: [
        {
          model: CartItem,
          include: [
            {
              model: Product,
              include: [
                {
                  model: Category,
                  attributes: ['id', 'name']
                },
                {
                  model: Farmer,
                  include: [
                    {
                      model: User,
                      attributes: ['id', 'firstName', 'lastName']
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    });

    const subtotal = updatedCart.CartItems.reduce((sum, item) => {
      return sum + (item.quantity * item.price);
    }, 0);

    await cart.update({ total: subtotal });

    res.json({
      success: true,
      message: 'Item removed from cart successfully',
      data: {
        cart: {
          id: updatedCart.id,
          userId: updatedCart.userId,
          total: subtotal,
          itemCount: updatedCart.CartItems.length,
          items: updatedCart.CartItems
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

exports.clearCart = async (req, res, next) => {
  try {
    const cart = await Cart.findOne({ where: { userId: req.user.id } });

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    // Remove all cart items
    await CartItem.destroy({ where: { cartId: cart.id } });

    // Reset cart total
    await cart.update({ total: 0 });

    res.json({
      success: true,
      message: 'Cart cleared successfully',
      data: {
        cart: {
          id: cart.id,
          userId: cart.userId,
          total: 0,
          itemCount: 0,
          items: []
        }
      }
    });
  } catch (error) {
    next(error);
  }
};