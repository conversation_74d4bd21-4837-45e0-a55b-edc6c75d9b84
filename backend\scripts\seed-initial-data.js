const { connectDB } = require('../config/database');
const { User, Category, Product, Farmer } = require('../data/models');
const bcrypt = require('bcryptjs');

async function seedInitialData() {
  try {
    console.log('🌱 Starting initial data seeding...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Database connected');

    // Create categories
    console.log('📂 Creating categories...');
    const categories = await Category.bulkCreate([
      {
        name: 'Vegetables',
        description: 'Fresh vegetables from Cameroon farms',
        isActive: true
      },
      {
        name: 'Fruits',
        description: 'Organic fruits grown in Cameroon',
        isActive: true
      },
      {
        name: 'Grains & Cereals',
        description: 'Locally grown grains and cereals',
        isActive: true
      },
      {
        name: 'Dairy Products',
        description: 'Fresh dairy products',
        isActive: true
      },
      {
        name: 'Meat & Poultry',
        description: 'Locally raised meat and poultry',
        isActive: true
      },
      {
        name: 'Spices & Herbs',
        description: 'Traditional Cameroonian spices and herbs',
        isActive: true
      },
      {
        name: 'Cocoa & Coffee',
        description: 'Premium cocoa beans and coffee',
        isActive: true
      },
      {
        name: 'Nuts & Seeds',
        description: 'Various nuts and seeds',
        isActive: true
      }
    ], { ignoreDuplicates: true });
    console.log(`✅ Created ${categories.length} categories`);

    // Create admin user
    console.log('👤 Creating admin user...');
    const hashedPassword = await bcrypt.hash('admin123', 12);
    const adminUser = await User.create({
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'ADMIN',
      status: 'ACTIVE',
      emailVerified: true,
      phoneVerified: false
    });
    console.log('✅ Admin user created');

    // Create sample farmer users
    console.log('👨‍🌾 Creating sample farmers...');
    const farmers = [];
    
    const farmerData = [
      {
        firstName: 'Paul',
        lastName: 'Mebe',
        email: '<EMAIL>',
        phone: '+237670123456',
        farmName: 'Green Valley Farm',
        farmLocation: 'Southwest Region, Cameroon',
        farmDescription: 'Organic cocoa and vegetable farm in the Southwest region'
      },
      {
        firstName: 'Marie',
        lastName: 'Ngozi',
        email: '<EMAIL>',
        phone: '+237680234567',
        farmName: 'Sunrise Fruits Farm',
        farmLocation: 'Littoral Region, Cameroon',
        farmDescription: 'Tropical fruits and spices farm near Douala'
      },
      {
        firstName: 'Jean',
        lastName: 'Kamga',
        email: '<EMAIL>',
        phone: '+237690345678',
        farmName: 'Highland Grains',
        farmLocation: 'West Region, Cameroon',
        farmDescription: 'Grain and cereal production in the western highlands'
      }
    ];

    for (const farmer of farmerData) {
      const hashedFarmerPassword = await bcrypt.hash('farmer123', 12);
      
      const user = await User.create({
        firstName: farmer.firstName,
        lastName: farmer.lastName,
        email: farmer.email,
        phone: farmer.phone,
        password: hashedFarmerPassword,
        role: 'FARMER',
        status: 'ACTIVE',
        emailVerified: true,
        phoneVerified: true
      });

      const farmerProfile = await Farmer.create({
        userId: user.id,
        farmName: farmer.farmName,
        farmLocation: farmer.farmLocation,
        farmDescription: farmer.farmDescription,
        verified: true,
        rating: 4.5
      });

      farmers.push({ user, farmer: farmerProfile });
    }
    console.log(`✅ Created ${farmers.length} farmers`);

    // Create sample products
    console.log('🥕 Creating sample products...');
    const vegetableCategory = categories.find(c => c.name === 'Vegetables');
    const fruitCategory = categories.find(c => c.name === 'Fruits');
    const cocoaCategory = categories.find(c => c.name === 'Cocoa & Coffee');

    const sampleProducts = [
      {
        name: 'Premium Cocoa Beans',
        description: 'High-quality organic cocoa beans from Southwest Cameroon',
        price: 2500,
        unit: 'kg',
        stock: 100,
        categoryId: cocoaCategory.id,
        farmerId: farmers[0].farmer.id,
        isOrganic: true,
        images: []
      },
      {
        name: 'Fresh Tomatoes',
        description: 'Locally grown fresh tomatoes, perfect for cooking',
        price: 800,
        unit: 'kg',
        stock: 50,
        categoryId: vegetableCategory.id,
        farmerId: farmers[0].farmer.id,
        images: []
      },
      {
        name: 'Sweet Bananas',
        description: 'Sweet and ripe bananas from the Littoral region',
        price: 500,
        unit: 'bunch',
        stock: 30,
        categoryId: fruitCategory.id,
        farmerId: farmers[1].farmer.id,
        images: []
      },
      {
        name: 'Organic Carrots',
        description: 'Fresh organic carrots grown without pesticides',
        price: 1200,
        unit: 'kg',
        stock: 25,
        categoryId: vegetableCategory.id,
        farmerId: farmers[2].farmer.id,
        isOrganic: true,
        images: []
      },
      {
        name: 'Pineapples',
        description: 'Sweet and juicy pineapples from local farms',
        price: 1000,
        unit: 'piece',
        stock: 20,
        categoryId: fruitCategory.id,
        farmerId: farmers[1].farmer.id,
        images: []
      }
    ];

    const products = await Product.bulkCreate(sampleProducts);
    console.log(`✅ Created ${products.length} sample products`);

    console.log('\n🎉 Initial data seeding completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Categories: ${categories.length}`);
    console.log(`   - Admin users: 1`);
    console.log(`   - Farmers: ${farmers.length}`);
    console.log(`   - Products: ${products.length}`);
    console.log('\n🔐 Login credentials:');
    console.log('   Admin: <EMAIL> / admin123');
    console.log('   Farmer 1: <EMAIL> / farmer123');
    console.log('   Farmer 2: <EMAIL> / farmer123');
    console.log('   Farmer 3: <EMAIL> / farmer123');

  } catch (error) {
    console.error('❌ Error seeding initial data:', error);
    process.exit(1);
  }
}

// Run the seeding
if (require.main === module) {
  seedInitialData().then(() => {
    console.log('✅ Seeding completed');
    process.exit(0);
  });
}

module.exports = seedInitialData;
