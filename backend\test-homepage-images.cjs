// Test homepage images functionality
const axios = require('axios');

async function testHomepageImages() {
  console.log('🖼️ Testing Homepage Images Functionality...\n');
  
  try {
    // Step 1: Test products API to ensure images are in database
    console.log('1. Testing products API...');
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    
    if (!productsResponse.data.success) {
      console.log('❌ Products API failed');
      return;
    }
    
    const products = productsResponse.data.data.products;
    console.log(`✅ Found ${products.length} products`);
    
    // Step 2: Check if products have images
    console.log('\n2. Checking product images...');
    let productsWithImages = 0;
    let productsWithoutImages = 0;
    
    products.forEach((product, index) => {
      if (product.images && product.images.length > 0) {
        productsWithImages++;
        if (index < 5) { // Show first 5 as examples
          console.log(`   ✅ ${product.name}: ${product.images[0]}`);
        }
      } else {
        productsWithoutImages++;
        if (index < 3) { // Show first 3 missing as examples
          console.log(`   ❌ ${product.name}: No images`);
        }
      }
    });
    
    console.log(`\n   Products with images: ${productsWithImages}`);
    console.log(`   Products without images: ${productsWithoutImages}`);
    
    // Step 3: Test specific image URLs
    console.log('\n3. Testing image URL accessibility...');
    const sampleProduct = products.find(p => p.images && p.images.length > 0);
    
    if (sampleProduct) {
      const imageUrl = sampleProduct.images[0];
      console.log(`   Testing URL: ${imageUrl}`);
      
      try {
        const imageResponse = await axios.head(imageUrl, { timeout: 5000 });
        if (imageResponse.status === 200) {
          console.log(`   ✅ Image accessible: ${imageResponse.headers['content-type']}`);
        } else {
          console.log(`   ⚠️ Image response: ${imageResponse.status}`);
        }
      } catch (error) {
        console.log(`   ❌ Image not accessible: ${error.message}`);
      }
    } else {
      console.log('   ❌ No products with images found');
    }
    
    // Step 4: Test categories API
    console.log('\n4. Testing categories API...');
    const categoriesResponse = await axios.get('http://localhost:3001/api/categories');
    
    if (categoriesResponse.data.success) {
      const categories = categoriesResponse.data.data;
      console.log(`   ✅ Found ${categories.length} categories`);
      categories.forEach(cat => {
        const categoryProducts = products.filter(p => p.Category?.id === cat.id);
        console.log(`   ${cat.name}: ${categoryProducts.length} products`);
      });
    } else {
      console.log('   ❌ Categories API failed');
    }
    
    // Step 5: Simulate homepage data structure
    console.log('\n5. Simulating homepage data structure...');
    
    // Featured products (first 8)
    const featuredProducts = products.slice(0, 8);
    console.log(`   Featured products: ${featuredProducts.length}`);
    
    let featuredWithImages = 0;
    featuredProducts.forEach(product => {
      if (product.images && product.images.length > 0) {
        featuredWithImages++;
      }
    });
    console.log(`   Featured with images: ${featuredWithImages}/${featuredProducts.length}`);
    
    // Deals (first 4)
    const deals = products.slice(0, 4);
    console.log(`   Deals products: ${deals.length}`);
    
    let dealsWithImages = 0;
    deals.forEach(product => {
      if (product.images && product.images.length > 0) {
        dealsWithImages++;
      }
    });
    console.log(`   Deals with images: ${dealsWithImages}/${deals.length}`);
    
    // Hero section featured product
    const heroProduct = products.length > 0 ? products[0] : null;
    if (heroProduct) {
      console.log(`   Hero product: ${heroProduct.name}`);
      if (heroProduct.images && heroProduct.images.length > 0) {
        console.log(`   ✅ Hero has image: ${heroProduct.images[0]}`);
      } else {
        console.log(`   ❌ Hero missing image`);
      }
    }
    
    // Step 6: Summary and recommendations
    console.log('\n🎉 Homepage Images Test Summary:');
    console.log(`   Total products: ${products.length}`);
    console.log(`   Products with images: ${productsWithImages} (${Math.round(productsWithImages/products.length*100)}%)`);
    console.log(`   Featured products ready: ${featuredWithImages}/8`);
    console.log(`   Deals products ready: ${dealsWithImages}/4`);
    console.log(`   Hero product ready: ${heroProduct && heroProduct.images ? 'Yes' : 'No'}`);
    
    // Step 7: Frontend testing instructions
    console.log('\n📋 Frontend Testing Instructions:');
    console.log('   1. Go to: http://localhost:5173/');
    console.log('   2. Check hero section for real product image');
    console.log('   3. Scroll to "Featured Products" - should show 8 real images');
    console.log('   4. Scroll to "Deals Of The Day" - should show 4 real images');
    console.log('   5. All images should be high-quality agricultural photos');
    console.log('   6. No placeholder images should be visible');
    
    if (productsWithImages === products.length) {
      console.log('\n🎊 SUCCESS: All products have images! Homepage should look professional.');
    } else {
      console.log('\n⚠️ WARNING: Some products missing images. Homepage may show placeholders.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the backend server is running:');
      console.log('   cd backend && node server.js');
    }
  }
}

testHomepageImages();
