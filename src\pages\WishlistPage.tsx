import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import {
  Heart,
  ShoppingCart,
  Star,
  Trash2,
  ArrowLeft,
  Share2,
  Eye
} from 'lucide-react';
import { getProductImage } from '@/utils/productImages';
import { toast } from 'sonner';
import wishlistService from '@/services/wishlistService';
import cartService from '@/services/cartService';
import authService from '@/services/authService';

const WishlistPage: React.FC = () => {
  const [user, setUser] = useState(authService.getCurrentUser());
  const queryClient = useQueryClient();

  // Fetch wishlist data from backend
  const { data: wishlistData, isLoading, refetch } = useQuery({
    queryKey: ['wishlist'],
    queryFn: wishlistService.getWishlist,
    enabled: !!user,
    staleTime: 1 * 60 * 1000, // 1 minute
  });

  const wishlistItems = wishlistData?.data?.wishlist || [];

  const removeFromWishlist = async (productId: number) => {
    try {
      // Convert to string for API compatibility
      const response = await wishlistService.removeFromWishlist(String(productId));
      if (response.success) {
        toast.success('Item removed from wishlist');
        refetch();
        window.dispatchEvent(new Event('cartUpdated'));
      } else {
        toast.error(response.message || 'Failed to remove from wishlist');
      }
    } catch (error) {
      console.error('Remove from wishlist error:', error);
      toast.error('Failed to remove from wishlist');
    }
  };

  const addToCart = async (item: any) => {
    if (item.Product.stock < 1) {
      toast.error('This item is currently out of stock');
      return;
    }

    try {
      // Convert ProductId to string to ensure compatibility
      const productId = String(item.ProductId || item.Product?.id);
      const response = await cartService.addToCart(productId, 1);
      if (response.success) {
        toast.success(`${item.Product.name} added to cart!`);
        window.dispatchEvent(new Event('cartUpdated'));
      } else {
        toast.error(response.message || 'Failed to add to cart');
      }
    } catch (error) {
      console.error('Add to cart error:', error);
      toast.error('Failed to add to cart');
    }
  };

  const clearWishlist = async () => {
    try {
      const response = await wishlistService.clearWishlist();
      if (response.success) {
        toast.success('Wishlist cleared');
        refetch();
        window.dispatchEvent(new Event('cartUpdated'));
      } else {
        toast.error(response.message || 'Failed to clear wishlist');
      }
    } catch (error) {
      console.error('Clear wishlist error:', error);
      toast.error('Failed to clear wishlist');
    }
  };

  const addAllToCart = async () => {
    const inStockItems = wishlistItems.filter((item: any) => item.Product.stock > 0);
    if (inStockItems.length === 0) {
      toast.error('No items in stock to add to cart');
      return;
    }

    try {
      // Convert ProductIds to strings for compatibility
      const productIds = inStockItems.map((item: any) => String(item.ProductId || item.Product?.id));
      const response = await wishlistService.moveToCart(productIds);
      if (response.success) {
        toast.success(`${response.data.moved.length} items moved to cart!`);
        refetch();
        window.dispatchEvent(new Event('cartUpdated'));
      } else {
        toast.error(response.message || 'Failed to move items to cart');
      }
    } catch (error) {
      console.error('Move to cart error:', error);
      toast.error('Failed to move items to cart');
    }
  };

  const shareWishlist = () => {
    toast.success('Wishlist link copied to clipboard!');
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your wishlist...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Show login required state
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart className="w-12 h-12 text-gray-400" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Please login to view your wishlist</h1>
            <p className="text-gray-600 mb-8">
              You need to be logged in to access your wishlist.
            </p>
            <Button asChild className="bg-green-600 hover:bg-green-700">
              <Link to="/login">Login</Link>
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (wishlistItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart className="w-12 h-12 text-gray-400" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Your wishlist is empty</h1>
            <p className="text-gray-600 mb-8">
              Save items you love to your wishlist and come back to them later.
            </p>
            <Button asChild className="bg-green-600 hover:bg-green-700">
              <Link to="/products">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Discover Products
              </Link>
            </Button>
          </div>
        </div>
        
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Wishlist</h1>
              <p className="text-gray-600 mt-1">
                {wishlistItems.length} {wishlistItems.length === 1 ? 'item' : 'items'} saved
              </p>
            </div>
            <Button variant="outline" onClick={shareWishlist}>
              <Share2 className="w-4 h-4 mr-2" />
              Share Wishlist
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {wishlistItems.map((item) => (
            <Card key={item.id} className="group bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
              {/* Product Image */}
              <div className="relative aspect-square overflow-hidden">
                <img
                  src={(item.Product as any).images && (item.Product as any).images.length > 0 ? (item.Product as any).images[0] : '/placeholder.svg'}
                  alt={item.Product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {/* Stock Status */}
                <div className="absolute top-3 left-3">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    item.Product.stock > 0
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {item.Product.stock > 0 ? 'In Stock' : 'Out of Stock'}
                  </span>
                </div>
                {/* Remove Button */}
                <button
                  onClick={() => removeFromWishlist(item.ProductId)}
                  className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-red-50 hover:text-red-600 transition-colors duration-200"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>

              {/* Product Info */}
              <CardContent className="p-4">
                <div className="mb-3">
                  <h3 className="font-semibold text-gray-900 text-lg mb-1 line-clamp-2">
                    {item.Product.name}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {item.Product.Category?.name}
                  </p>
                  <p className="text-sm text-gray-500 line-clamp-2">
                    {item.Product.description}
                  </p>
                </div>

                {/* Price */}
                <div className="mb-4">
                  <span className="text-xl font-bold text-green-600">
                    {item.Product.price?.toLocaleString()} FCFA
                  </span>
                  {(item.Product as any).unit && (
                    <span className="text-sm text-gray-500 ml-1">
                      /{(item.Product as any).unit}
                    </span>
                  )}
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Button
                    onClick={() => addToCart(item)}
                    disabled={item.Product.stock < 1}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    {item.Product.stock > 0 ? 'Add to Cart' : 'Out of Stock'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Actions */}
        <div className="flex justify-between items-center mt-8 pt-8 border-t">
          <Button variant="outline" asChild>
            <Link to="/products">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Continue Shopping
            </Link>
          </Button>
          
          <div className="flex space-x-4">
            <Button variant="outline" onClick={clearWishlist}>
              <Trash2 className="w-4 h-4 mr-2" />
              Clear Wishlist
            </Button>

            <Button
              onClick={addAllToCart}
              className="bg-green-600 hover:bg-green-700"
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              Add All to Cart
            </Button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default WishlistPage;
