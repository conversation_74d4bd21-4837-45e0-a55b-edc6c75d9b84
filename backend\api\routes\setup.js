const express = require('express');
const router = express.Router();
const { Category, Product, User, Farmer } = require('../../data/models');
const { asyncHandler } = require('../../middleware/errorHandler');

// Add comprehensive Cameroon categories
router.post('/add-categories', asyncHandler(async (req, res) => {
  try {
    const newCategories = [
      { name: 'Cereals & Grains', description: 'Maize, rice, millet, sorghum, wheat, fonio, teff' },
      { name: 'Root Crops & Tubers', description: 'Cassava, yam, sweet potato, Irish potato, cocoyam, plantain' },
      { name: 'Major Cash Crops', description: 'Cocoa, coffee, cotton, rubber, palm oil, sugarcane, tobacco, tea' },
      { name: 'Tropical Fruits', description: 'Banana, pineapple, mango, papaya, avocado, citrus fruits, guava' },
      { name: 'Indigenous/Wild Fruits', description: 'Bush mango, African cherry, velvet tamarind, African pear' },
      { name: 'Leafy Greens', description: 'Spinach, lettuce, cabbage, bitter leaf, water leaf, pumpkin leaves' },
      { name: 'Fruit Vegetables', description: 'Tomato, pepper, eggplant, okra, cucumber, zucchini, pumpkin' },
      { name: 'Other Vegetables', description: 'Onion, garlic, carrot, beetroot, green beans, celery' },
      { name: 'Legumes & Pulses', description: 'Groundnuts, cowpea, beans, soybeans, bambara groundnut' },
      { name: 'Spices & Seasonings', description: 'Ginger, turmeric, pepper, njangsa seeds, eru seeds' },
      { name: 'Herbs & Aromatics', description: 'Basil, lemongrass, mint, rosemary, thyme, oregano' },
      { name: 'Tree Crops & Nuts', description: 'Oil palm, coconut, kola nut, African walnut, cashew, shea nut' },
      { name: 'Livestock - Ruminants', description: 'Cattle, goat, sheep, buffalo' },
      { name: 'Livestock - Monogastrics', description: 'Pig, poultry, duck, guinea fowl, rabbit, grasscutter' },
      { name: 'Aquaculture & Fisheries', description: 'Tilapia, catfish, carp, marine fish, shrimp, oysters' },
      { name: 'Medicinal & Aromatic Plants', description: 'Moringa, aloe vera, neem, artemisia, hibiscus' },
      { name: 'Fiber Crops', description: 'Cotton, sisal, jute, kenaf, bamboo, raffia palm' },
      { name: 'Beverage Crops', description: 'Coffee, tea, cocoa, kola nut, hibiscus, ginger drinks' },
      { name: 'Oil Crops', description: 'Oil palm, groundnut, soybean, sesame, sunflower, castor bean' },
      { name: 'Mushrooms & Fungi', description: 'Oyster mushrooms, shiitake, wild edible mushrooms' },
      { name: 'Beekeeping Products', description: 'Honey, beeswax, propolis, royal jelly' },
      { name: 'Forestry Products', description: 'Rubber trees, teak, eucalyptus, fruit tree timber' },
      { name: 'Flowers & Ornamentals', description: 'Cut flowers, ornamental plants, landscaping plants' }
    ];

    // Check existing categories
    const existingCategories = await Category.findAll();
    const existingNames = existingCategories.map(cat => cat.name);
    
    // Filter new categories
    const categoriesToAdd = newCategories.filter(cat => !existingNames.includes(cat.name));
    
    let addedCategories = [];
    if (categoriesToAdd.length > 0) {
      addedCategories = await Category.bulkCreate(categoriesToAdd);
    }

    const allCategories = await Category.findAll();

    res.json({
      success: true,
      message: `Added ${addedCategories.length} new categories`,
      data: {
        addedCategories,
        totalCategories: allCategories.length,
        allCategories
      }
    });

  } catch (error) {
    console.error('Error adding categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add categories',
      error: error.message
    });
  }
}));

// Add sample products for new categories
router.post('/add-sample-products', asyncHandler(async (req, res) => {
  try {
    // Find or create sample farmer
    let sampleUser = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!sampleUser) {
      sampleUser = await User.create({
        firstName: 'Sample',
        lastName: 'Farmer',
        email: '<EMAIL>',
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm',
        role: 'FARMER',
        status: 'ACTIVE',
        emailVerified: true
      });
    }

    let sampleFarmer = await Farmer.findOne({ where: { userId: sampleUser.id } });
    if (!sampleFarmer) {
      sampleFarmer = await Farmer.create({
        userId: sampleUser.id,
        farmName: 'Green Valley Farm',
        farmLocation: 'Douala, Cameroon',
        verified: true,
        rating: 4.5
      });
    }

    // Get all categories
    const categories = await Category.findAll();
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat.id;
    });

    // Sample products for each category
    const sampleProducts = [
      // Cereals & Grains
      { name: 'Yellow Maize (Corn)', description: 'High-quality yellow corn, locally grown', price: 400, unit: 'kg', stock: 500, category: 'Cereals & Grains', images: ['https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=400&h=300&fit=crop&q=80'] },
      { name: 'Premium White Rice', description: 'High quality lowland white rice', price: 800, unit: 'kg', stock: 200, category: 'Cereals & Grains', images: ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&q=80'] },
      { name: 'Pearl Millet', description: 'Nutritious pearl millet grains', price: 600, unit: 'kg', stock: 150, category: 'Cereals & Grains', images: ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=300&fit=crop&q=80'] },
      { name: 'Red Sorghum', description: 'Traditional red sorghum variety', price: 550, unit: 'kg', stock: 120, category: 'Cereals & Grains', images: ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=300&fit=crop&q=80'] },
      { name: 'Fonio (Ancient Grain)', description: 'Premium fonio, ancient West African superfood', price: 1200, unit: 'kg', stock: 80, category: 'Cereals & Grains', images: ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=300&fit=crop&q=80'] },
      
      // Root Crops & Tubers
      { name: 'Fresh Cassava', description: 'Sweet cassava variety, freshly harvested', price: 300, unit: 'kg', stock: 300, category: 'Root Crops & Tubers', images: ['https://images.unsplash.com/photo-1583258292688-d0213dc5a3a8?w=400&h=300&fit=crop&q=80'] },
      { name: 'White Yam', description: 'Premium white yam tubers', price: 700, unit: 'kg', stock: 200, category: 'Root Crops & Tubers', images: ['https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&q=80'] },
      { name: 'Orange Sweet Potato', description: 'Vitamin A rich orange sweet potatoes', price: 400, unit: 'kg', stock: 250, category: 'Root Crops & Tubers', images: ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop&q=80'] },
      { name: 'Irish Potato', description: 'Fresh Irish potatoes from highland farms', price: 600, unit: 'kg', stock: 180, category: 'Root Crops & Tubers', images: ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop&q=80'] },
      { name: 'Cooking Plantain', description: 'Green cooking plantains, perfect for frying', price: 350, unit: 'bunch', stock: 100, category: 'Root Crops & Tubers', images: ['https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=400&h=300&fit=crop&q=80'] },
      
      // Major Cash Crops
      { name: 'Premium Cocoa Beans', description: 'Fine flavor cocoa beans, sun-dried', price: 2500, unit: 'kg', stock: 100, category: 'Major Cash Crops', images: ['https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=300&fit=crop&q=80'] },
      { name: 'Arabica Coffee Beans', description: 'High-altitude Arabica coffee beans', price: 3000, unit: 'kg', stock: 80, category: 'Major Cash Crops', images: ['https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop&q=80'] },
      { name: 'Crude Palm Oil', description: 'Fresh crude palm oil, locally processed', price: 1800, unit: 'liter', stock: 150, category: 'Major Cash Crops', images: ['https://images.unsplash.com/photo-1605034313761-73ea4a0cfbf3?w=400&h=300&fit=crop&q=80'] },
      
      // Tropical Fruits
      { name: 'Sweet Bananas', description: 'Ripe yellow bananas, perfect for eating', price: 300, unit: 'bunch', stock: 150, category: 'Tropical Fruits', images: ['https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=300&fit=crop&q=80'] },
      { name: 'Fresh Pineapples', description: 'Sweet smooth cayenne pineapples', price: 800, unit: 'piece', stock: 80, category: 'Tropical Fruits', images: ['https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400&h=300&fit=crop&q=80'] },
      { name: 'Ripe Mangoes', description: 'Juicy local mango variety', price: 600, unit: 'kg', stock: 120, category: 'Tropical Fruits', images: ['https://images.unsplash.com/photo-1553279768-865429fa0078?w=400&h=300&fit=crop&q=80'] },
      { name: 'Fresh Papayas', description: 'Sweet orange papayas, rich in vitamins', price: 400, unit: 'kg', stock: 100, category: 'Tropical Fruits', images: ['https://images.unsplash.com/photo-1617112848923-cc2234396a8d?w=400&h=300&fit=crop&q=80'] },
      { name: 'Hass Avocados', description: 'Premium Hass avocados, creamy and nutritious', price: 1000, unit: 'kg', stock: 60, category: 'Tropical Fruits', images: ['https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400&h=300&fit=crop&q=80'] },
      { name: 'Sweet Oranges', description: 'Juicy sweet oranges, high in vitamin C', price: 500, unit: 'kg', stock: 200, category: 'Tropical Fruits', images: ['https://images.unsplash.com/photo-1547514701-42782101795e?w=400&h=300&fit=crop&q=80'] },
      
      // Indigenous/Wild Fruits
      { name: 'Bush Mango (Irvingia)', description: 'Traditional bush mango seeds and fruit', price: 1500, unit: 'kg', stock: 40, category: 'Indigenous/Wild Fruits', images: ['https://images.unsplash.com/photo-1553279768-865429fa0078?w=400&h=300&fit=crop&q=80'] },
      { name: 'African Pear (Safou)', description: 'Nutritious African pear, butter fruit', price: 800, unit: 'kg', stock: 60, category: 'Indigenous/Wild Fruits', images: ['https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400&h=300&fit=crop&q=80'] },
      
      // Leafy Greens
      { name: 'Fresh Spinach', description: 'Tender green spinach leaves', price: 400, unit: 'kg', stock: 80, category: 'Leafy Greens', images: ['https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400&h=300&fit=crop&q=80'] },
      { name: 'Bitter Leaf (Vernonia)', description: 'Traditional bitter leaf for soup', price: 300, unit: 'bundle', stock: 100, category: 'Leafy Greens', images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'] },
      { name: 'Water Leaf', description: 'Fresh water leaf for traditional soups', price: 250, unit: 'bundle', stock: 120, category: 'Leafy Greens', images: ['https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400&h=300&fit=crop&q=80'] },
      { name: 'Pumpkin Leaves (Ugu)', description: 'Fluted pumpkin leaves, rich in nutrients', price: 350, unit: 'bundle', stock: 90, category: 'Leafy Greens', images: ['https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400&h=300&fit=crop&q=80'] },
      
      // Fruit Vegetables
      { name: 'Fresh Tomatoes', description: 'Organic red tomatoes, freshly harvested', price: 500, unit: 'kg', stock: 200, category: 'Fruit Vegetables', images: ['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80'] },
      { name: 'Hot Pepper (Scotch Bonnet)', description: 'Spicy scotch bonnet peppers', price: 1200, unit: 'kg', stock: 50, category: 'Fruit Vegetables', images: ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=300&fit=crop&q=80'] },
      { name: 'Garden Egg (African Eggplant)', description: 'Traditional white garden eggs', price: 400, unit: 'kg', stock: 100, category: 'Fruit Vegetables', images: ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=300&fit=crop&q=80'] },
      { name: 'Fresh Okra', description: 'Tender okra pods, perfect for soup', price: 600, unit: 'kg', stock: 80, category: 'Fruit Vegetables', images: ['https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=400&h=300&fit=crop&q=80'] },
      
      // Other Vegetables
      { name: 'Red Onions', description: 'Fresh red bulb onions, locally grown', price: 700, unit: 'kg', stock: 150, category: 'Other Vegetables', images: ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop&q=80'] },
      { name: 'Fresh Carrots', description: 'Orange carrots, sweet and crunchy', price: 800, unit: 'kg', stock: 100, category: 'Other Vegetables', images: ['https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400&h=300&fit=crop&q=80'] },
      
      // Legumes & Pulses
      { name: 'Groundnuts (Peanuts)', description: 'Spanish variety groundnuts, freshly harvested', price: 900, unit: 'kg', stock: 200, category: 'Legumes & Pulses', images: ['https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=300&fit=crop&q=80'] },
      { name: 'Black-eyed Peas (Cowpea)', description: 'Traditional cowpea variety, protein-rich', price: 800, unit: 'kg', stock: 150, category: 'Legumes & Pulses', images: ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&q=80'] },
      { name: 'Soybeans', description: 'High-protein soybeans for oil and food', price: 1000, unit: 'kg', stock: 120, category: 'Legumes & Pulses', images: ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&q=80'] },
      
      // Spices & Seasonings
      { name: 'Fresh Ginger', description: 'Yellow ginger variety, aromatic and spicy', price: 1500, unit: 'kg', stock: 80, category: 'Spices & Seasonings', images: ['https://images.unsplash.com/photo-1599639832862-bd92c6e10b5b?w=400&h=300&fit=crop&q=80'] },
      { name: 'Turmeric Root', description: 'Fresh turmeric root, anti-inflammatory', price: 2000, unit: 'kg', stock: 60, category: 'Spices & Seasonings', images: ['https://images.unsplash.com/photo-1599639832862-bd92c6e10b5b?w=400&h=300&fit=crop&q=80'] },
      { name: 'Cameroon Pepper', description: 'Traditional Piper guineense, aromatic spice', price: 3000, unit: 'kg', stock: 30, category: 'Spices & Seasonings', images: ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=300&fit=crop&q=80'] },
      
      // Herbs & Aromatics
      { name: 'Fresh Basil', description: 'Sweet basil leaves, aromatic herb', price: 800, unit: 'bundle', stock: 50, category: 'Herbs & Aromatics', images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'] },
      { name: 'Lemongrass', description: 'Fresh lemongrass stalks for tea and cooking', price: 600, unit: 'bundle', stock: 70, category: 'Herbs & Aromatics', images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'] },
      
      // Medicinal & Aromatic Plants
      { name: 'Moringa Leaves', description: 'Fresh moringa leaves, superfood with high nutrients', price: 1000, unit: 'kg', stock: 60, category: 'Medicinal & Aromatic Plants', images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'] },
      { name: 'Aloe Vera', description: 'Fresh aloe vera leaves for medicinal use', price: 500, unit: 'piece', stock: 100, category: 'Medicinal & Aromatic Plants', images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'] },
      
      // Beekeeping Products
      { name: 'Pure Forest Honey', description: 'Raw forest honey, unprocessed and natural', price: 2500, unit: 'liter', stock: 40, category: 'Beekeeping Products', images: ['https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400&h=300&fit=crop&q=80'] },
      { name: 'Natural Beeswax', description: 'Pure beeswax for cosmetics and crafts', price: 3000, unit: 'kg', stock: 20, category: 'Beekeeping Products', images: ['https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400&h=300&fit=crop&q=80'] }
    ];

    const productsToCreate = [];
    for (const product of sampleProducts) {
      const categoryId = categoryMap[product.category];
      if (categoryId) {
        productsToCreate.push({
          name: product.name,
          description: product.description,
          price: product.price,
          unit: product.unit,
          stock: product.stock,
          farmerId: sampleFarmer.id,
          categoryId: categoryId,
          images: product.images,
          status: 'ACTIVE'
        });
      }
    }

    const createdProducts = await Product.bulkCreate(productsToCreate);

    res.json({
      success: true,
      message: `Added ${createdProducts.length} sample products`,
      data: {
        productsAdded: createdProducts.length,
        farmer: sampleFarmer
      }
    });

  } catch (error) {
    console.error('Error adding products:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add products',
      error: error.message
    });
  }
}));

module.exports = router;
