// Import necessary modules
const { body } = require('express-validator');
const validate = require('../middleware/validate');

// Validation rules for creating an order
exports.validateOrder = [
  body('totalAmount').isFloat({ min: 0 }).withMessage('Total amount must be a positive number'),
  body('items').isArray({ min: 1 }).withMessage('Order must contain at least one item'),
  body('items.*.productId').isInt({ min: 1 }).withMessage('Valid product ID is required'),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
  body('items.*.price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  validate,
];