
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MapPin, Phone, Mail, Calendar, ShoppingBag, Star } from "lucide-react";

interface CustomerProfileProps {
  name: string;
  email: string;
  phone: string;
  avatar: string;
  location: string;
  joinDate: string;
  totalOrders: number;
  favoriteCategories: string[];
  loyaltyPoints: number;
  verified: boolean;
}

const CustomerProfile = ({ 
  name, 
  email, 
  phone, 
  avatar, 
  location, 
  joinDate, 
  totalOrders, 
  favoriteCategories, 
  loyaltyPoints,
  verified 
}: CustomerProfileProps) => {
  return (
    <Card className="hover:shadow-lg transition-shadow duration-300">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <Avatar className="w-20 h-20">
            <AvatarImage src={avatar} alt={name} />
            <AvatarFallback>{name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
        </div>
        <CardTitle className="flex items-center justify-center gap-2">
          {name}
          {verified && (
            <Badge variant="secondary" className="bg-green-100 text-green-700">
              Verified
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Mail className="w-4 h-4" />
          <span className="text-sm">{email}</span>
        </div>
        
        <div className="flex items-center gap-2 text-muted-foreground">
          <Phone className="w-4 h-4" />
          <span className="text-sm">{phone}</span>
        </div>
        
        <div className="flex items-center gap-2 text-muted-foreground">
          <MapPin className="w-4 h-4" />
          <span className="text-sm">{location}</span>
        </div>
        
        <div className="flex items-center gap-2 text-muted-foreground">
          <Calendar className="w-4 h-4" />
          <span className="text-sm">Member since {joinDate}</span>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div className="text-center p-3 bg-muted/30 rounded-lg">
            <ShoppingBag className="w-5 h-5 mx-auto mb-1 text-primary" />
            <div className="font-semibold">{totalOrders}</div>
            <div className="text-xs text-muted-foreground">Orders</div>
          </div>
          <div className="text-center p-3 bg-muted/30 rounded-lg">
            <Star className="w-5 h-5 mx-auto mb-1 text-primary" />
            <div className="font-semibold">{loyaltyPoints}</div>
            <div className="text-xs text-muted-foreground">Points</div>
          </div>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Favorite Categories</h4>
          <div className="flex flex-wrap gap-2">
            {favoriteCategories.map((category, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {category}
              </Badge>
            ))}
          </div>
        </div>
        
        <div className="flex gap-2 mt-4">
          <Button size="sm" className="flex-1">
            View Orders
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            Edit Profile
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomerProfile;
