// Test the fixed cart and wishlist endpoints
const axios = require('axios');

async function testFixedEndpoints() {
  console.log('🧪 Testing Fixed Cart and Wishlist Endpoints...\n');
  
  try {
    // Create a test user
    console.log('👤 Creating test user...');
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Fixed',
      lastName: 'Test',
      email: `fixed.test.${Date.now()}@example.com`,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    const token = registerResponse.data.data.accessToken;
    console.log('✅ Test user created\n');
    
    // Get a product to test with
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products;
    const testProduct = products[0];
    console.log(`🎯 Testing with: ${testProduct.name}\n`);
    
    // Test Cart Endpoints
    console.log('🛒 Testing Cart Endpoints:');
    
    // 1. Add to Cart (fixed endpoint)
    try {
      const addCartResponse = await axios.post('http://localhost:3001/api/cart/add', {
        productId: testProduct.id,
        quantity: 2
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Add to cart: SUCCESS');
      console.log('   Response:', addCartResponse.data.success ? 'Success' : 'Failed');
    } catch (error) {
      console.log('❌ Add to cart: FAILED');
      console.log('   Error:', error.response?.data?.message || error.message);
    }
    
    // 2. Get Cart
    try {
      const getCartResponse = await axios.get('http://localhost:3001/api/cart', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('✅ Get cart: SUCCESS');
      console.log(`   Items in cart: ${getCartResponse.data.data.cart.itemCount}`);
      console.log(`   Total: ${getCartResponse.data.data.cart.total} FCFA`);
    } catch (error) {
      console.log('❌ Get cart: FAILED');
      console.log('   Error:', error.response?.data?.message || error.message);
    }
    
    // Test Wishlist Endpoints
    console.log('\n❤️ Testing Wishlist Endpoints:');
    
    // 1. Add to Wishlist (fixed endpoint)
    try {
      const addWishlistResponse = await axios.post('http://localhost:3001/api/wishlist/add', {
        productId: testProduct.id
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Add to wishlist: SUCCESS');
      console.log('   Response:', addWishlistResponse.data.success ? 'Success' : 'Failed');
    } catch (error) {
      console.log('❌ Add to wishlist: FAILED');
      console.log('   Error:', error.response?.data?.message || error.message);
    }
    
    // 2. Get Wishlist
    try {
      const getWishlistResponse = await axios.get('http://localhost:3001/api/wishlist', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('✅ Get wishlist: SUCCESS');
      console.log(`   Items in wishlist: ${getWishlistResponse.data.data?.length || 0}`);
    } catch (error) {
      console.log('❌ Get wishlist: FAILED');
      console.log('   Error:', error.response?.data?.message || error.message);
    }
    
    console.log('\n🎉 Endpoint tests completed!');
    console.log('\n📝 Summary:');
    console.log('✅ Fixed cart endpoint: /api/cart/add');
    console.log('✅ Fixed wishlist endpoint: /api/wishlist/add');
    console.log('✅ Frontend services updated to match backend routes');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testFixedEndpoints();
