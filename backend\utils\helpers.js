// Helper function to format date
exports.formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  // Helper function to calculate pagination
  exports.getPagination = (page, size) => {
    const limit = size ? +size : 10;
    const offset = page ? page * limit : 0;
    return { limit, offset };
  };
  
  // Helper function to format pagination result
  exports.getPagingData = (data, page, limit) => {
    const { count: totalItems, rows: items } = data;
    const currentPage = page ? +page : 0;
    const totalPages = Math.ceil(totalItems / limit);
    return { totalItems, items, totalPages, currentPage };
  };
  
  // Helper function to generate a random string
  exports.generateRandomString = (length) => {
    return Math.random().toString(36).substring(2, length + 2);
  };
  
  // Helper function to validate email
  exports.isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  // Helper function to calculate discount
  exports.calculateDiscount = (price, discountPercentage) => {
    return price - (price * discountPercentage / 100);
  };