const express = require('express');
const path = require('path');
const config = require('./config/config');
const { getSequelize } = require('./config/database');

// Import middleware
const { errorHandler, notFound } = require('./middleware/errorHandler');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./api/routes/users');
const productRoutes = require('./api/routes/products');
const categoryRoutes = require('./api/routes/categories');
const orderRoutes = require('./api/routes/orders');
const cartRoutes = require('./api/routes/cart');
const wishlistRoutes = require('./api/routes/wishlist');
const reviewRoutes = require('./api/routes/reviews');
const vendorsRoutes = require('./api/routes/vendors');
const notificationsRoutes = require('./api/routes/notifications');
const deliveryRoutes = require('./api/routes/delivery');
const setupRoutes = require('./api/routes/setup');
const aiRoutes = require('./api/routes/ai');

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later.'
  }
});
app.use('/api/', limiter);

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const sequelize = getSequelize();
    await sequelize.authenticate();
    const dialect = sequelize.getDialect();

    res.status(200).json({
      status: 'OK',
      message: 'Cameroon Farm Connect API is running',
      timestamp: new Date().toISOString(),
      database: {
        status: 'connected',
        type: dialect.toUpperCase()
      }
    });
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      message: 'Service unavailable',
      timestamp: new Date().toISOString(),
      database: {
        status: 'disconnected',
        error: error.message
      }
    });
  }
});

// Add a root route for friendly API message
app.get('/', (req, res) => {
  res.send('Cameroon Farm Connect API is running!');
});

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/public', express.static(path.join(__dirname, 'public')));

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/products', productRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/wishlist', wishlistRoutes);
app.use('/api/notifications', notificationsRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/vendors', vendorsRoutes);
app.use('/api/deliveries', deliveryRoutes);
app.use('/api/setup', setupRoutes);
app.use('/api/ai', aiRoutes);

// 404 handler for undefined routes
app.use(notFound);

// Global error handling middleware (must be last)
app.use(errorHandler);

module.exports = app;
