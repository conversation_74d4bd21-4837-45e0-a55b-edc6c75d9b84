// Test farmer-specific features
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function loginAsFarmer() {
  try {
    console.log('🧪 Logging in as farmer...');
    
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Test123!'
    });
    
    console.log('✅ Farmer login successful');
    return response.data.data.accessToken;
  } catch (error) {
    console.log('❌ Farmer login failed:', error.response?.data || error.message);
    return null;
  }
}

async function getCategories() {
  try {
    console.log('\n🧪 Getting categories...');
    
    const response = await axios.get(`${BASE_URL}/categories`);
    console.log('✅ Categories loaded:', response.data.length, 'categories found');
    return response.data;
  } catch (error) {
    console.log('❌ Categories failed:', error.response?.data || error.message);
    return null;
  }
}

async function addProduct(token, categoryId) {
  try {
    console.log('\n🧪 Testing Add Product...');
    
    const productData = {
      name: 'Fresh Test Tomatoes',
      description: 'Organic red tomatoes, freshly harvested from our farm',
      price: 600,
      unit: 'kg',
      stock: 100,
      minOrder: 1,
      categoryId: categoryId,
      images: ['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80'],
      isOrganic: true
    };
    
    const response = await axios.post(`${BASE_URL}/products`, productData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Product added successfully:', response.data);
    return response.data.data.product;
  } catch (error) {
    console.log('❌ Add product failed:', error.response?.data || error.message);
    return null;
  }
}

async function getFarmerProducts(token) {
  try {
    console.log('\n🧪 Getting farmer products...');
    
    const response = await axios.get(`${BASE_URL}/products/farmer/my-products`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Farmer products loaded:', response.data);
    return response.data;
  } catch (error) {
    console.log('❌ Get farmer products failed:', error.response?.data || error.message);
    return null;
  }
}

async function testCheckoutFlow(customerToken, productId) {
  try {
    console.log('\n🧪 Testing Checkout Flow...');
    
    // First, add product to cart
    await axios.post(`${BASE_URL}/cart/add`, {
      productId: productId,
      quantity: 3
    }, {
      headers: {
        'Authorization': `Bearer ${customerToken}`
      }
    });
    
    // Get cart
    const cartResponse = await axios.get(`${BASE_URL}/cart`, {
      headers: {
        'Authorization': `Bearer ${customerToken}`
      }
    });
    
    console.log('✅ Cart retrieved:', cartResponse.data);
    
    // Create order
    const orderData = {
      shippingAddress: {
        firstName: 'John',
        lastName: 'Doe',
        address: '123 Test Street',
        city: 'Douala',
        region: 'Littoral',
        postalCode: '12345',
        phone: '+237 6XX XXX XXX'
      },
      paymentMethod: 'cod',
      notes: 'Test order from API'
    };
    
    const orderResponse = await axios.post(`${BASE_URL}/orders`, orderData, {
      headers: {
        'Authorization': `Bearer ${customerToken}`
      }
    });
    
    console.log('✅ Order created successfully:', orderResponse.data);
    return orderResponse.data;
  } catch (error) {
    console.log('❌ Checkout flow failed:', error.response?.data || error.message);
    return null;
  }
}

async function runFarmerTests() {
  console.log('🚀 Starting Farmer Feature Tests...\n');
  
  // Login as farmer
  const farmerToken = await loginAsFarmer();
  if (!farmerToken) return;
  
  // Get categories
  const categories = await getCategories();
  if (!categories || categories.length === 0) return;
  
  // Add a product
  const product = await addProduct(farmerToken, categories[0].id);
  
  // Get farmer's products
  await getFarmerProducts(farmerToken);
  
  // Test checkout flow with customer
  if (product) {
    // Login as customer first
    try {
      const customerResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'Test123!'
      });
      
      const customerToken = customerResponse.data.data.accessToken;
      await testCheckoutFlow(customerToken, product.id);
    } catch (error) {
      console.log('❌ Customer login for checkout failed:', error.response?.data || error.message);
    }
  }
  
  console.log('\n🎉 Farmer Feature Tests Complete!');
}

runFarmerTests().catch(console.error);
