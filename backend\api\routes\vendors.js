const express = require('express');
const multer = require('multer');
const path = require('path');
const vendorController = require('../controllers/vendorController');
const {authenticateToken} = require('../middleware/auth');
const { validateVendor, validateProductListing } = require('../validators/vendorValidators');

const router = express.Router();

// Configure multer for file upload
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });
router.get('/', vendorController.listVendor);
router.post('/register', authenticateToken, validateVendor, vendorController.registerVendor);
router.get('/me', authenticateToken, vendorController.getVendorProfile);
router.put('/me', authenticateToken, validateVendor, vendorController.updateVendorProfile);
router.post('/listings', authenticateToken, validateProductListing, vendorController.createProductListing);
router.put('/listings/:id', authenticateToken, validateProductListing, vendorController.updateProductListing);
router.delete('/listings/:id', authenticateToken, vendorController.deleteProductListing);
router.get('/listings', authenticateToken, vendorController.getVendorListings);

router.get('/dashboard', authenticateToken, vendorController.getDashboard);
router.post('/products', authenticateToken, validateProductListing, vendorController.createProduct);

// New route for uploading product image
router.post('/products/:id/upload-image', authenticateToken, upload.single('image'), vendorController.uploadProductImage);

// Delete vendor account
router.delete('/account', authenticateToken, vendorController.deleteAccount);

module.exports = router;