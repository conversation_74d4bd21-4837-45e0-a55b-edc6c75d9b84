// Quick product seeding
const mysql = require('mysql2/promise');

async function quickSeed() {
  console.log('🚀 Quick Product Seeding...\n');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to database');
    
    // Step 1: Create sample user
    console.log('1. Creating sample farmer user...');
    
    const userId = 'sample-farmer-' + Date.now();
    const farmerId = 'sample-farm-' + Date.now();
    
    await connection.execute(`
      INSERT IGNORE INTO users (id, firstName, lastName, email, password, role, status, emailVerified, createdAt, updatedAt)
      VALUES (?, 'Sample', 'Farmer', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'FARMER', 'ACTIVE', true, NOW(), NOW())
    `, [userId]);
    
    console.log('   ✅ User created');
    
    // Step 2: Create farmer profile
    console.log('2. Creating farmer profile...');
    
    await connection.execute(`
      INSERT IGNORE INTO farmers (id, userId, farmName, farmLocation, verified, rating, totalSales, createdAt, updatedAt)
      VALUES (?, ?, 'Green Valley Farm', 'Douala, Cameroon', true, 4.5, 0, NOW(), NOW())
    `, [farmerId, userId]);
    
    console.log('   ✅ Farmer profile created');
    
    // Step 3: Get categories
    console.log('3. Getting categories...');
    
    const [categories] = await connection.execute('SELECT id, name FROM categories LIMIT 10');
    console.log(`   ✅ Found ${categories.length} categories`);
    
    if (categories.length === 0) {
      console.log('   ❌ No categories found');
      return;
    }
    
    // Step 4: Create sample products
    console.log('4. Creating sample products...');
    
    const products = [
      ['Fresh Tomatoes', 'Organic red tomatoes, freshly harvested', 500, 'kg', 200, true],
      ['Yellow Maize', 'High-quality yellow corn, locally grown', 400, 'kg', 500, false],
      ['Fresh Cassava', 'Sweet cassava variety, freshly harvested', 300, 'kg', 300, false],
      ['Sweet Bananas', 'Ripe yellow bananas, perfect for eating', 300, 'bunch', 150, false],
      ['Fresh Spinach', 'Tender green spinach leaves, locally grown', 400, 'kg', 80, true],
      ['Groundnuts', 'Spanish variety groundnuts, freshly harvested', 900, 'kg', 200, false],
      ['Fresh Ginger', 'Yellow ginger variety, aromatic and spicy', 1500, 'kg', 80, false],
      ['Red Onions', 'Fresh red bulb onions, locally grown', 700, 'kg', 150, false]
    ];
    
    let createdCount = 0;
    
    for (let i = 0; i < products.length && i < categories.length; i++) {
      const [name, description, price, unit, stock, isOrganic] = products[i];
      const category = categories[i];
      const productId = 'product-' + Date.now() + '-' + i;
      
      try {
        await connection.execute(`
          INSERT INTO products (
            id, name, description, price, unit, stock, minOrder, images, status, 
            farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, 
            totalSold, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, 1, ?, 'ACTIVE', ?, ?, ?, false, 0, 0, 0, NOW(), NOW())
        `, [
          productId, name, description, price, unit, stock,
          JSON.stringify(['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80']),
          farmerId, category.id, isOrganic
        ]);
        
        createdCount++;
        console.log(`   ✅ Created: ${name} in ${category.name}`);
        
      } catch (error) {
        console.log(`   ⚠️ Failed to create ${name}:`, error.message);
      }
    }
    
    // Step 5: Verify results
    const [productCount] = await connection.execute('SELECT COUNT(*) as count FROM products');
    
    console.log(`\n🎉 Quick seeding completed!`);
    console.log(`   Created ${createdCount} new products`);
    console.log(`   Total products in database: ${productCount[0].count}`);
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Quick seeding failed:', error.message);
  }
  
  process.exit(0);
}

quickSeed();
