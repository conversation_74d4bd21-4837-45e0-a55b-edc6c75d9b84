import authService from './authService';

const API_BASE = 'http://localhost:3001/api';

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  unit: string;
  stock: number;
  minOrder: number;
  categoryId: string;
  farmerId: string;
  harvestDate?: string;
  expiryDate?: string;
  nutritionInfo?: string;
  storageInfo?: string;
  isOrganic: boolean;
  isFairTrade: boolean;
  status: 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK';
  images: string[];
  rating: number;
  totalReviews: number;
  totalSold: number;
  createdAt: string;
  updatedAt: string;
  Category?: {
    id: string;
    name: string;
    description: string;
  };
  Farmer?: {
    id: string;
    farmName: string;
    farmLocation: string;
    verified: boolean;
    rating: number;
    User: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      phone?: string;
    };
  };
}

export interface ProductFilters {
  category?: string;
  search?: string;
  sort?: 'price_asc' | 'price_desc' | 'newest' | 'rating' | 'popular';
  limit?: number;
  offset?: number;
  status?: string;
}

class ProductService {
  private getAuthHeaders() {
    const token = authService.getToken();
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  // Get all products with filters
  async getProducts(filters: ProductFilters = {}): Promise<{
    products: Product[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      pages: number;
    };
  }> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE}/products?${queryParams}`);
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to fetch products');
    }

    return result.data;
  }

  // Get single product by ID
  async getProduct(id: string): Promise<Product> {
    const response = await fetch(`${API_BASE}/products/${id}`);
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to fetch product');
    }

    return result.data;
  }

  // Get products by farmer
  async getProductsByFarmer(farmerId: string, filters: Partial<ProductFilters> = {}): Promise<{
    farmer: any;
    products: Product[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      pages: number;
    };
  }> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE}/products/farmer/${farmerId}?${queryParams}`);
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to fetch farmer products');
    }

    return result.data;
  }

  // Create new product
  async createProduct(productData: FormData): Promise<Product> {
    const response = await fetch(`${API_BASE}/products`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: productData,
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to create product');
    }

    return result.data;
  }

  // Update product
  async updateProduct(id: string, productData: FormData): Promise<Product> {
    const response = await fetch(`${API_BASE}/products/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: productData,
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to update product');
    }

    return result.data;
  }

  // Delete product
  async deleteProduct(id: string): Promise<void> {
    const response = await fetch(`${API_BASE}/products/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to delete product');
    }
  }

  // Upload images to existing product
  async uploadProductImages(id: string, images: File[]): Promise<{
    images: string[];
    newImages: string[];
  }> {
    const formData = new FormData();
    images.forEach(image => {
      formData.append('images', image);
    });

    const response = await fetch(`${API_BASE}/products/${id}/images`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: formData,
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to upload images');
    }

    return result.data;
  }

  // Delete specific image from product
  async deleteProductImage(id: string, imagePath: string): Promise<{
    images: string[];
  }> {
    const response = await fetch(`${API_BASE}/products/${id}/images`, {
      method: 'DELETE',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imagePath }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to delete image');
    }

    return result.data;
  }

  // Search products
  async searchProducts(query: string, filters: Partial<ProductFilters> = {}): Promise<{
    products: Product[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      pages: number;
    };
  }> {
    return this.getProducts({
      ...filters,
      search: query,
    });
  }

  // Get featured products
  async getFeaturedProducts(limit: number = 6): Promise<Product[]> {
    const result = await this.getProducts({
      sort: 'rating',
      limit,
      status: 'ACTIVE',
    });
    return result.products;
  }

  // Get products by category
  async getProductsByCategory(categoryId: string, filters: Partial<ProductFilters> = {}): Promise<{
    products: Product[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      pages: number;
    };
  }> {
    return this.getProducts({
      ...filters,
      category: categoryId,
    });
  }

  // Get popular products
  async getPopularProducts(limit: number = 10): Promise<Product[]> {
    const result = await this.getProducts({
      sort: 'popular',
      limit,
      status: 'ACTIVE',
    });
    return result.products;
  }

  // Get newest products
  async getNewestProducts(limit: number = 10): Promise<Product[]> {
    const result = await this.getProducts({
      sort: 'newest',
      limit,
      status: 'ACTIVE',
    });
    return result.products;
  }

  // Get low stock products (for farmers)
  async getLowStockProducts(threshold: number = 10): Promise<Product[]> {
    const result = await this.getProducts({
      status: 'ACTIVE',
      limit: 100, // Get more to filter
    });
    
    return result.products.filter(product => product.stock <= threshold);
  }

  // Update product stock
  async updateProductStock(id: string, stock: number): Promise<Product> {
    const formData = new FormData();
    formData.append('stock', stock.toString());

    return this.updateProduct(id, formData);
  }

  // Toggle product status
  async toggleProductStatus(id: string, status: 'ACTIVE' | 'INACTIVE'): Promise<Product> {
    const formData = new FormData();
    formData.append('status', status);

    return this.updateProduct(id, formData);
  }
}

export default new ProductService();
