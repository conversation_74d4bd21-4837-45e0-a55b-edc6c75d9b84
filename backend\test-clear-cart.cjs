// Test the fixed clear cart functionality
const axios = require('axios');

async function testClearCart() {
  console.log('🧪 Testing Fixed Clear Cart Functionality...\n');
  
  try {
    // Create a test user
    console.log('1. Creating test user...');
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Clear',
      lastName: 'Test',
      email: `clear.test.${Date.now()}@example.com`,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    const token = registerResponse.data.data.accessToken;
    console.log('✅ Test user created\n');
    
    // Add items to cart
    console.log('2. Adding items to cart...');
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products;
    
    // Add multiple items
    for (let i = 0; i < 3; i++) {
      const product = products[i];
      await axios.post('http://localhost:3001/api/cart/add', {
        productId: product.id,
        quantity: 2
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
    }
    
    console.log('✅ Added 3 items to cart\n');
    
    // Check cart contents
    console.log('3. Checking cart contents...');
    const cartResponse = await axios.get('http://localhost:3001/api/cart', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const cart = cartResponse.data.data.cart;
    console.log(`✅ Cart has ${cart.itemCount} items, total: ${cart.total} FCFA\n`);
    
    // Test clear cart
    console.log('4. Testing clear cart...');
    const clearResponse = await axios.delete('http://localhost:3001/api/cart/clear', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Clear cart API call successful');
    console.log('   Response:', clearResponse.data.success ? 'Success' : 'Failed');
    console.log('   Message:', clearResponse.data.message);
    
    // Verify cart is empty
    console.log('\n5. Verifying cart is empty...');
    const emptyCartResponse = await axios.get('http://localhost:3001/api/cart', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const emptyCart = emptyCartResponse.data.data.cart;
    console.log(`✅ Cart now has ${emptyCart.itemCount} items, total: ${emptyCart.total} FCFA`);
    
    if (emptyCart.itemCount === 0 && emptyCart.total === 0) {
      console.log('\n🎉 Clear Cart functionality is working perfectly!');
      console.log('\n📝 Summary:');
      console.log('✅ Fixed endpoint: /api/cart/clear');
      console.log('✅ Cart clearing works correctly');
      console.log('✅ Frontend should now work without "API not found" error');
    } else {
      console.log('\n❌ Cart was not properly cleared');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 404) {
      console.log('\n🔍 This was the "API not found" error you were seeing');
      console.log('💡 The fix should resolve this issue');
    }
  }
}

testClearCart();
