// Import required modules
const Product = require('../../data/models/product');

// Define the InventoryService class
class InventoryService {
  // Method to update product stock
  async updateStock(productId, quantity) {
    try {
      // Find the product
      const product = await Product.findByPk(productId);
      if (!product) {
        throw new Error('Product not found');
      }

      // Update stock
      product.stock += quantity;
      await product.save();

      return product;
    } catch (error) {
      throw error;
    }
  }

  // Method to check if a product is in stock
  async isInStock(productId, requiredQuantity) {
    try {
      // Find the product
      const product = await Product.findByPk(productId);
      if (!product) {
        throw new Error('Product not found');
      }

      // Check if the required quantity is available
      return product.stock >= requiredQuantity;
    } catch (error) {
      throw error;
    }
  }

  // Method to get low stock products
  async getLowStockProducts(threshold = 10) {
    try {
      // Find products with stock below the threshold
      const lowStockProducts = await Product.findAll({
        where: {
          stock: {
            [Op.lt]: threshold
          }
        }
      });

      return lowStockProducts;
    } catch (error) {
      throw error;
    }
  }
}

// Export the InventoryService
module.exports = new InventoryService();