class Product {
    constructor(id, name, description, price, categoryId, stock) {
      this.id = id;
      this.name = name;
      this.description = description;
      this.price = price;
      this.categoryId = categoryId;
      this.stock = stock;
    }
  
    isInStock() {
      return this.stock > 0;
    }
  
    decreaseStock(quantity) {
      if (this.stock < quantity) {
        throw new Error('Not enough stock');
      }
      this.stock -= quantity;
    }
  
    increaseStock(quantity) {
      this.stock += quantity;
    }
  
    toJSON() {
      return {
        id: this.id,
        name: this.name,
        description: this.description,
        price: this.price,
        categoryId: this.categoryId,
        stock: this.stock,
        inStock: this.isInStock()
      };
    }
  
    // Add more product-specific methods as needed
  }
  
  module.exports = Product;