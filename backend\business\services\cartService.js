const { Cart, CartItem, Product } = require('../../data/models');
const cartRepository = require('../../data/repositories/cartRepository');

class CartService {
  async getOrCreateCart(userId) {
    let cart = await cartRepository.findByUserId(userId);
    if (!cart) {
      cart = await cartRepository.create({ userId });
    }
    return cart;
  }

  async addToCart(userId, productId, quantity) {
    const cart = await this.getOrCreateCart(userId);
    const product = await Product.findByPk(productId);
    
    if (!product) {
      throw new Error('Product not found');
    }

    let cartItem = await CartItem.findOne({
      where: { cartId: cart.id, productId }
    });

    if (cartItem) {
      cartItem.quantity += quantity;
      await cartItem.save();
    } else {
      cartItem = await CartItem.create({
        cartId: cart.id,
        productId,
        quantity
      });
    }

    return this.getCartContents(userId);
  }

  async removeFromCart(userId, productId) {
    const cart = await this.getOrCreateCart(userId);
    await CartItem.destroy({
      where: { cartId: cart.id, productId }
    });
    return this.getCartContents(userId);
  }

  async getCartContents(userId) {
    const cart = await Cart.findOne({
      where: { userId },
      include: [{ model: CartItem, include: [Product] }]
    });
    return cart;
  }

  async clearCart(userId) {
    const cart = await this.getOrCreateCart(userId);
    await CartItem.destroy({
      where: { cartId: cart.id }
    });
    return this.getCartContents(userId);
  }
}

module.exports = new CartService();