// Test the fixed cart functionality
const axios = require('axios');

async function testFixedCart() {
  console.log('🧪 Testing Fixed Cart Functionality...\n');
  
  try {
    // Create a test user
    console.log('👤 Creating test user...');
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Cart',
      lastName: 'Test',
      email: `cart.test.${Date.now()}@example.com`,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    const token = registerResponse.data.data.accessToken;
    console.log('✅ Test user created\n');
    
    // Get products
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products;
    const testProduct = products[0];
    
    console.log(`🎯 Testing with product: ${testProduct.name}`);
    console.log(`   Product ID: ${testProduct.id}`);
    console.log(`   Product ID type: ${typeof testProduct.id}\n`);
    
    // Test add to cart with correct string ID
    console.log('🛒 Testing Add to Cart with string ID...');
    try {
      const cartResponse = await axios.post('http://localhost:3001/api/cart/add', {
        productId: testProduct.id, // Using string ID directly
        quantity: 2
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Add to cart SUCCESS!');
      console.log('   Response:', cartResponse.data.success ? 'Success' : 'Failed');
      console.log('   Message:', cartResponse.data.message || 'No message');
    } catch (cartError) {
      console.log('❌ Add to cart FAILED:');
      console.log('   Status:', cartError.response?.status);
      console.log('   Error:', cartError.response?.data?.message || cartError.message);
    }
    
    // Get cart to verify
    console.log('\n🛒 Getting cart contents...');
    try {
      const getCartResponse = await axios.get('http://localhost:3001/api/cart', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('✅ Get cart SUCCESS!');
      const cart = getCartResponse.data.data.cart;
      console.log(`   Items in cart: ${cart.itemCount || 0}`);
      console.log(`   Total: ${cart.total || 0} FCFA`);
      
      if (cart.items && cart.items.length > 0) {
        cart.items.forEach(item => {
          console.log(`   - ${item.Product?.name}: ${item.quantity}x ${item.price} FCFA`);
        });
      }
    } catch (getCartError) {
      console.log('❌ Get cart failed:', getCartError.response?.data || getCartError.message);
    }
    
    // Test wishlist
    console.log('\n❤️ Testing Add to Wishlist...');
    try {
      const wishlistResponse = await axios.post('http://localhost:3001/api/wishlist/add', {
        productId: testProduct.id // Using string ID
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Add to wishlist SUCCESS!');
      console.log('   Response:', wishlistResponse.data.success ? 'Success' : 'Failed');
    } catch (wishlistError) {
      console.log('❌ Add to wishlist failed:', wishlistError.response?.data || wishlistError.message);
    }
    
    console.log('\n🎉 Fixed Cart Tests Complete!');
    console.log('\n📝 Summary:');
    console.log('✅ Product IDs are now handled as strings (UUIDs)');
    console.log('✅ No more parseInt() conversion issues');
    console.log('✅ Foreign key constraints should work properly');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testFixedCart();
