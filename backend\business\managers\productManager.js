const productService = require('../services/productService');
const categoryService = require('../services/categoryService');

class ProductManager {
  async createProduct(productData) {
    // Validate category existence
    await categoryService.getCategoryById(productData.categoryId);
    return await productService.createProduct(productData);
  }

  async getProductById(id) {
    return await productService.getProductById(id);
  }

  async updateProduct(id, productData) {
    if (productData.categoryId) {
      // Validate category existence if changing
      await categoryService.getCategoryById(productData.categoryId);
    }
    return await productService.updateProduct(id, productData);
  }

  async deleteProduct(id) {
    // Add any additional checks before deletion
    return await productService.deleteProduct(id);
  }

  async getAllProducts(options) {
    // Add any filtering or pagination logic here
    return await productService.getAllProducts(options);
  }

  async getProductsByCategory(categoryId) {
    // Validate category existence
    await categoryService.getCategoryById(categoryId);
    return await productService.getProductsByCategory(categoryId);
  }

  async searchProducts(query) {
    return await productService.searchProducts(query);
  }
}

module.exports = new ProductManager();