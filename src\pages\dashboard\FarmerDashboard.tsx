import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Package,
  TrendingUp,
  DollarSign,
  AlertTriangle,
  MoreHorizontal,
  User,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';
import productService, { Product } from '@/services/productService';
import authService from '@/services/authService';
import ProductForm from '@/components/products/ProductForm';
import FarmerProfileForm from '@/components/farmer/FarmerProfileForm';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const FarmerDashboard: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showProductForm, setShowProductForm] = useState(false);
  const [showProfileForm, setShowProfileForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [stats, setStats] = useState({
    totalProducts: 0,
    activeProducts: 0,
    totalSales: 0,
    lowStockProducts: 0
  });

  const user = authService.getCurrentUser();

  useEffect(() => {
    if (user?.farmerProfile) {
      fetchFarmerProducts();
      fetchStats();
    }
  }, [user]);

  const fetchFarmerProducts = async () => {
    try {
      setLoading(true);
      if (user?.farmerProfile) {
        const result = await productService.getProductsByFarmer(user.farmerProfile.id);
        setProducts(result.products);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to fetch products');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      if (user?.farmerProfile) {
        const result = await productService.getProductsByFarmer(user.farmerProfile.id, { limit: 1000 });
        const allProducts = result.products;
        
        const activeProducts = allProducts.filter(p => p.status === 'ACTIVE');
        const lowStockProducts = allProducts.filter(p => p.stock <= 10);
        const totalSales = allProducts.reduce((sum, p) => sum + (p.totalSold * p.price), 0);

        setStats({
          totalProducts: allProducts.length,
          activeProducts: activeProducts.length,
          totalSales,
          lowStockProducts: lowStockProducts.length
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleCreateProduct = async (formData: FormData) => {
    try {
      await productService.createProduct(formData);
      toast.success('Product created successfully!');
      setShowProductForm(false);
      fetchFarmerProducts();
      fetchStats();
    } catch (error: any) {
      toast.error(error.message || 'Failed to create product');
      throw error;
    }
  };

  const handleUpdateProduct = async (formData: FormData) => {
    try {
      if (editingProduct) {
        await productService.updateProduct(editingProduct.id, formData);
        toast.success('Product updated successfully!');
        setEditingProduct(null);
        setShowProductForm(false);
        fetchFarmerProducts();
        fetchStats();
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to update product');
      throw error;
    }
  };

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm('Are you sure you want to delete this product?')) {
      return;
    }

    try {
      await productService.deleteProduct(productId);
      toast.success('Product deleted successfully!');
      fetchFarmerProducts();
      fetchStats();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete product');
    }
  };

  const handleToggleStatus = async (product: Product) => {
    try {
      const newStatus = product.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
      await productService.toggleProductStatus(product.id, newStatus);
      toast.success(`Product ${newStatus.toLowerCase()} successfully!`);
      fetchFarmerProducts();
      fetchStats();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update product status');
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!user?.farmerProfile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-bold mb-4">Farmer Profile Required</h2>
            <p className="text-gray-600 mb-4">
              You need to complete your farmer profile to access the dashboard.
            </p>
            <Button>Complete Profile</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (showProductForm) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <ProductForm
          product={editingProduct}
          onSubmit={editingProduct ? handleUpdateProduct : handleCreateProduct}
          onCancel={() => {
            setShowProductForm(false);
            setEditingProduct(null);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user.firstName}!
          </h1>
          <p className="text-gray-600">
            Manage your products and track your farm's performance
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Products</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.totalProducts}</p>
                </div>
                <Package className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Products</p>
                  <p className="text-3xl font-bold text-green-600">{stats.activeProducts}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Sales</p>
                  <p className="text-3xl font-bold text-emerald-600">
                    {stats.totalSales.toLocaleString()} FCFA
                  </p>
                </div>
                <DollarSign className="w-8 h-8 text-emerald-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Low Stock</p>
                  <p className="text-3xl font-bold text-red-600">{stats.lowStockProducts}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Products Section */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-2xl font-bold">My Products</CardTitle>
              <div className="flex gap-3">
                <Button
                  onClick={() => setShowProfileForm(true)}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <User className="w-4 h-4" />
                  Manage Profile
                </Button>
                <Button
                  onClick={() => setShowProductForm(true)}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Product
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Search */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Products Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                    <div className="bg-gray-200 h-4 rounded mb-2"></div>
                    <div className="bg-gray-200 h-4 rounded w-3/4"></div>
                  </div>
                ))}
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-12">
                <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                <p className="text-gray-600 mb-4">
                  {searchQuery ? 'Try adjusting your search terms' : 'Start by adding your first product'}
                </p>
                {!searchQuery && (
                  <Button 
                    onClick={() => setShowProductForm(true)}
                    className="bg-emerald-600 hover:bg-emerald-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Your First Product
                  </Button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProducts.map((product) => (
                  <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative">
                      <img
                        src={product.images[0] ? `http://localhost:3001${product.images[0]}` : '/placeholder-product.jpg'}
                        alt={product.name}
                        className="w-full h-48 object-cover"
                      />
                      <Badge 
                        className={`absolute top-2 right-2 ${
                          product.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-500'
                        }`}
                      >
                        {product.status}
                      </Badge>
                      {product.stock <= 10 && (
                        <Badge className="absolute top-2 left-2 bg-red-500">
                          Low Stock
                        </Badge>
                      )}
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-bold text-lg mb-2 line-clamp-1">{product.name}</h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
                      
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-2xl font-bold text-emerald-600">
                          {product.price.toLocaleString()} FCFA
                        </span>
                        <span className="text-sm text-gray-500">
                          Stock: {product.stock}
                        </span>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setEditingProduct(product);
                              setShowProductForm(true);
                            }}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteProduct(product.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button size="sm" variant="outline">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => handleToggleStatus(product)}>
                              {product.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              View Details
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Profile Management Modal */}
      {showProfileForm && (
        <FarmerProfileForm
          onClose={() => setShowProfileForm(false)}
          onSuccess={() => {
            setShowProfileForm(false);
            toast.success('Profile updated successfully!');
          }}
        />
      )}
    </div>
  );
};

export default FarmerDashboard;
