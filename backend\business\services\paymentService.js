// Import required modules
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Order = require('../../data/models/order');

// Define the PaymentService class
class PaymentService {
  // Method to process payment
  async processPayment(orderId, token) {
    try {
      // Find the order
      const order = await Order.findByPk(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      // Create a charge using Stripe
      const charge = await stripe.charges.create({
        amount: order.total * 100, // Amount in cents
        currency: 'usd',
        source: token,
        description: `Payment for order ${orderId}`
      });

      // Update order status
      order.status = 'paid';
      await order.save();

      return { success: true, charge };
    } catch (error) {
      throw error;
    }
  }

  // Method to refund payment
  async refundPayment(orderId) {
    try {
      // Find the order
      const order = await Order.findByPk(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      // Refund the charge using Stripe
      const refund = await stripe.refunds.create({
        charge: order.paymentId
      });

      // Update order status
      order.status = 'refunded';
      await order.save();

      return { success: true, refund };
    } catch (error) {
      throw error;
    }
  }
}

// Export the PaymentService
module.exports = new PaymentService();
