import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Truck, 
  Package, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Phone,
  Navigation,
  Star
} from 'lucide-react';
import authService from '@/services/authService';
import { toast } from 'sonner';

const API_BASE = 'http://localhost:3001/api';

interface DeliveryOrder {
  id: string;
  orderNumber: string;
  status: string;
  totalAmount: number;
  shippingAddress: any;
  phoneNumber: string;
  estimatedDelivery: string;
  actualDelivery?: string;
  trackingNumber: string;
  Order: {
    User: {
      firstName: string;
      lastName: string;
      email: string;
    };
    OrderItems: Array<{
      quantity: number;
      Product: {
        name: string;
        unit: string;
      };
    }>;
  };
}

const DeliveryDashboard: React.FC = () => {
  const [user] = useState(authService.getCurrentUser());
  const queryClient = useQueryClient();

  // Fetch delivery orders assigned to this agent
  const { data: deliveries = [], isLoading } = useQuery({
    queryKey: ['delivery-orders'],
    queryFn: async () => {
      const response = await authService.authenticatedFetch(`${API_BASE}/deliveries/agent`);
      const result = await response.json();
      return result.success ? result.data : [];
    },
    enabled: !!user && user.role === 'DELIVERY_AGENT',
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Update delivery status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async ({ deliveryId, status }: { deliveryId: string; status: string }) => {
      const response = await authService.authenticatedFetch(`${API_BASE}/deliveries/${deliveryId}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status }),
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['delivery-orders'] });
      toast.success('Delivery status updated successfully!');
    },
    onError: () => {
      toast.error('Failed to update delivery status');
    },
  });

  const handleStatusUpdate = (deliveryId: string, status: string) => {
    updateStatusMutation.mutate({ deliveryId, status });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-700';
      case 'ASSIGNED': return 'bg-blue-100 text-blue-700';
      case 'IN_TRANSIT': return 'bg-purple-100 text-purple-700';
      case 'DELIVERED': return 'bg-green-100 text-green-700';
      case 'FAILED': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusActions = (delivery: DeliveryOrder) => {
    switch (delivery.status) {
      case 'ASSIGNED':
        return (
          <Button 
            size="sm" 
            onClick={() => handleStatusUpdate(delivery.id, 'IN_TRANSIT')}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Truck className="w-4 h-4 mr-1" />
            Start Delivery
          </Button>
        );
      case 'IN_TRANSIT':
        return (
          <div className="flex gap-2">
            <Button 
              size="sm" 
              onClick={() => handleStatusUpdate(delivery.id, 'DELIVERED')}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="w-4 h-4 mr-1" />
              Mark Delivered
            </Button>
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => handleStatusUpdate(delivery.id, 'FAILED')}
            >
              <AlertCircle className="w-4 h-4 mr-1" />
              Report Issue
            </Button>
          </div>
        );
      default:
        return null;
    }
  };

  const pendingDeliveries = deliveries.filter((d: DeliveryOrder) => ['ASSIGNED', 'IN_TRANSIT'].includes(d.status));
  const completedDeliveries = deliveries.filter((d: DeliveryOrder) => ['DELIVERED', 'FAILED'].includes(d.status));

  if (!user || user.role !== 'DELIVERY_AGENT') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="p-8 text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-600">You need to be a delivery agent to access this dashboard.</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Delivery Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user.firstName}!</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge className="bg-green-100 text-green-700">
                {pendingDeliveries.length} Active Deliveries
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Package className="w-8 h-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Deliveries</p>
                  <p className="text-2xl font-bold">{deliveries.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Truck className="w-8 h-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">In Transit</p>
                  <p className="text-2xl font-bold">{deliveries.filter((d: DeliveryOrder) => d.status === 'IN_TRANSIT').length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold">{completedDeliveries.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Star className="w-8 h-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold">
                    {deliveries.length > 0 ? Math.round((deliveries.filter((d: DeliveryOrder) => d.status === 'DELIVERED').length / deliveries.length) * 100) : 0}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Delivery Orders */}
        <Tabs defaultValue="pending" className="space-y-6">
          <TabsList>
            <TabsTrigger value="pending">Pending Deliveries ({pendingDeliveries.length})</TabsTrigger>
            <TabsTrigger value="completed">Completed ({completedDeliveries.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="space-y-4">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                <p className="text-gray-600 mt-2">Loading deliveries...</p>
              </div>
            ) : pendingDeliveries.length === 0 ? (
              <Card className="p-8 text-center">
                <Truck className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Pending Deliveries</h3>
                <p className="text-gray-600">All caught up! Check back later for new delivery assignments.</p>
              </Card>
            ) : (
              pendingDeliveries.map((delivery: DeliveryOrder) => (
                <Card key={delivery.id} className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold">Order #{delivery.orderNumber}</h3>
                      <p className="text-gray-600">Tracking: {delivery.trackingNumber}</p>
                    </div>
                    <Badge className={getStatusColor(delivery.status)}>
                      {delivery.status.replace('_', ' ')}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                    <div>
                      <h4 className="font-medium mb-2 flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        Delivery Address
                      </h4>
                      <p className="text-sm text-gray-600">
                        {delivery.Order.User.firstName} {delivery.Order.User.lastName}<br />
                        {delivery.shippingAddress?.address}<br />
                        {delivery.shippingAddress?.city}, {delivery.shippingAddress?.region}
                      </p>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2 flex items-center">
                        <Phone className="w-4 h-4 mr-1" />
                        Contact Info
                      </h4>
                      <p className="text-sm text-gray-600">
                        Phone: {delivery.phoneNumber}<br />
                        Email: {delivery.Order.User.email}
                      </p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-medium mb-2">Order Items</h4>
                    <div className="text-sm text-gray-600">
                      {delivery.Order.OrderItems.map((item, index) => (
                        <span key={index}>
                          {item.quantity} {item.Product.unit} {item.Product.name}
                          {index < delivery.Order.OrderItems.length - 1 && ', '}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="w-4 h-4 mr-1" />
                      Est. Delivery: {new Date(delivery.estimatedDelivery).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="font-semibold">{delivery.totalAmount.toLocaleString()} FCFA</span>
                      {getStatusActions(delivery)}
                    </div>
                  </div>
                </Card>
              ))
            )}
          </TabsContent>

          <TabsContent value="completed" className="space-y-4">
            {completedDeliveries.length === 0 ? (
              <Card className="p-8 text-center">
                <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Completed Deliveries</h3>
                <p className="text-gray-600">Completed deliveries will appear here.</p>
              </Card>
            ) : (
              completedDeliveries.map((delivery: DeliveryOrder) => (
                <Card key={delivery.id} className="p-6 opacity-75">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold">Order #{delivery.orderNumber}</h3>
                      <p className="text-gray-600">Tracking: {delivery.trackingNumber}</p>
                    </div>
                    <Badge className={getStatusColor(delivery.status)}>
                      {delivery.status}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      Delivered to: {delivery.Order.User.firstName} {delivery.Order.User.lastName}
                    </div>
                    <div className="text-sm text-gray-600">
                      {delivery.actualDelivery ? new Date(delivery.actualDelivery).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>
                </Card>
              ))
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default DeliveryDashboard;
