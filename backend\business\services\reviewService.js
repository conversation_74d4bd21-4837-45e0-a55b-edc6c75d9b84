const { Review } = require('../../data/models');
const reviewRepository = require('../../data/repositories/reviewRepository');

class ReviewService {
  async createReview(reviewData) {
    return await reviewRepository.create(reviewData);
  }

  async getReviewById(id) {
    return await reviewRepository.findById(id);
  }

  async updateReview(id, reviewData) {
    return await reviewRepository.update(id, reviewData);
  }

  async deleteReview(id) {
    return await reviewRepository.delete(id);
  }

  async getProductReviews(productId) {
    return await Review.findAll({
      where: { productId },
      include: ['user']
    });
  }

  async getUserReviews(userId) {
    return await Review.findAll({
      where: { userId },
      include: ['product']
    });
  }

  async getAverageRating(productId) {
    const result = await Review.findAll({
      where: { productId },
      attributes: [
        [Review.sequelize.fn('AVG', Review.sequelize.col('rating')), 'averageRating']
      ]
    });
    return result[0].get('averageRating');
  }
}

module.exports = new ReviewService();