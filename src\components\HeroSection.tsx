import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Leaf, Users, Globe } from "lucide-react";

const HeroSection = () => {
  return (
    <div className="gradient-agricultural text-white py-20 px-4">
      <div className="max-w-6xl mx-auto text-center">
        <div className="mb-8">
          <Leaf className="w-16 h-16 mx-auto mb-4 text-green-200" />
          <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
            <span className="text-green-600 font-extrabold">Agri Connect</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-green-100 max-w-3xl mx-auto">
            Connecting farmers, buyers, and agricultural communities across Cameroon. 
            Discover fresh, locally-grown products from cocoa to cassava.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Button size="lg" className="bg-white text-green-600 hover:bg-green-50 font-semibold px-8 py-3">
            Explore Products <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
          <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-green-600 font-semibold px-8 py-3">
            Join as Farmer <Users className="ml-2 w-5 h-5" />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="bg-white/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Leaf className="w-8 h-8" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Fresh Products</h3>
            <p className="text-green-100">Direct from local farms across Cameroon's regions</p>
          </div>
          <div className="text-center">
            <div className="bg-white/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Community Driven</h3>
            <p className="text-green-100">Supporting farmers and strengthening communities</p>
          </div>
          <div className="text-center">
            <div className="bg-white/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Globe className="w-8 h-8" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Regional Reach</h3>
            <p className="text-green-100">From Centre to Far North, Coast to East</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;

