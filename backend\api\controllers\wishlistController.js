const { Wishlist, Product, Category, User, Farmer } = require('../../data/models');

exports.getWishlist = async (req, res, next) => {
  try {
    const wishlistItems = await Wishlist.findAll({
      where: { userId: req.user.id },
      include: [
        {
          model: Product,
          include: [
            {
              model: Category,
              attributes: ['id', 'name', 'description']
            },
            {
              model: Farmer,
              include: [
                {
                  model: User,
                  attributes: ['id', 'firstName', 'lastName', 'email']
                }
              ]
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        wishlist: wishlistItems,
        count: wishlistItems.length
      }
    });
  } catch (error) {
    next(error);
  }
};

exports.addToWishlist = async (req, res, next) => {
  try {
    const { productId } = req.body;

    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    // Check if product exists
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if item already exists in wishlist
    const existingItem = await Wishlist.findOne({
      where: {
        userId: req.user.id,
        productId: productId
      }
    });

    if (existingItem) {
      return res.status(400).json({
        success: false,
        message: 'Product already in wishlist'
      });
    }

    // Add to wishlist
    const wishlistItem = await Wishlist.create({
      userId: req.user.id,
      productId: productId
    });

    // Get the created item with product details
    const createdItem = await Wishlist.findByPk(wishlistItem.id, {
      include: [
        {
          model: Product,
          include: [
            {
              model: Category,
              attributes: ['id', 'name', 'description']
            },
            {
              model: Farmer,
              include: [
                {
                  model: User,
                  attributes: ['id', 'firstName', 'lastName', 'email']
                }
              ]
            }
          ]
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Product added to wishlist successfully',
      data: {
        wishlistItem: createdItem
      }
    });
  } catch (error) {
    next(error);
  }
};

exports.removeFromWishlist = async (req, res, next) => {
  try {
    const { productId } = req.params;

    const wishlistItem = await Wishlist.findOne({
      where: {
        userId: req.user.id,
        productId: productId
      }
    });

    if (!wishlistItem) {
      return res.status(404).json({
        success: false,
        message: 'Product not found in wishlist'
      });
    }

    await wishlistItem.destroy();

    res.json({
      success: true,
      message: 'Product removed from wishlist successfully'
    });
  } catch (error) {
    next(error);
  }
};

exports.clearWishlist = async (req, res, next) => {
  try {
    await Wishlist.destroy({
      where: { userId: req.user.id }
    });

    res.json({
      success: true,
      message: 'Wishlist cleared successfully'
    });
  } catch (error) {
    next(error);
  }
};

exports.checkWishlist = async (req, res, next) => {
  try {
    const { productId } = req.params;

    const wishlistItem = await Wishlist.findOne({
      where: {
        userId: req.user.id,
        productId: productId
      }
    });

    res.json({
      success: true,
      data: {
        inWishlist: !!wishlistItem
      }
    });
  } catch (error) {
    next(error);
  }
};

// Move wishlist items to cart
exports.moveToCart = async (req, res, next) => {
  try {
    const { productIds } = req.body;
    const userId = req.user.id;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Product IDs array is required'
      });
    }

    const { Cart, CartItem } = require('../../data/models');
    const moved = [];
    const failed = [];

    // Get or create user's cart
    let cart = await Cart.findOne({ where: { userId } });
    if (!cart) {
      cart = await Cart.create({ userId });
    }

    // Process each product
    for (const productId of productIds) {
      try {
        // Check if product exists in wishlist
        const wishlistItem = await Wishlist.findOne({
          where: { userId, productId },
          include: [{ model: Product }]
        });

        if (!wishlistItem) {
          failed.push({ productId, reason: 'Not in wishlist' });
          continue;
        }

        // Check if product is available
        const product = wishlistItem.Product;
        if (!product || product.status !== 'ACTIVE' || product.stock < 1) {
          failed.push({ productId, reason: 'Product not available' });
          continue;
        }

        // Check if already in cart
        let cartItem = await CartItem.findOne({
          where: { cartId: cart.id, productId }
        });

        if (cartItem) {
          // Update quantity if already in cart
          cartItem.quantity += 1;
          await cartItem.save();
        } else {
          // Add new item to cart
          await CartItem.create({
            cartId: cart.id,
            productId,
            quantity: 1,
            price: product.price
          });
        }

        // Remove from wishlist
        await wishlistItem.destroy();
        moved.push({ productId, name: product.name });

      } catch (error) {
        console.error(`Error moving product ${productId}:`, error);
        failed.push({ productId, reason: 'Processing error' });
      }
    }

    res.json({
      success: true,
      message: `${moved.length} items moved to cart`,
      data: {
        moved,
        failed,
        totalMoved: moved.length,
        totalFailed: failed.length
      }
    });

  } catch (error) {
    next(error);
  }
};