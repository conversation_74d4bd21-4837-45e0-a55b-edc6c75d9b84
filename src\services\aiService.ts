// AI Service for Cameroon Farm Connect Hub
// Integrates various AI capabilities for farming and e-commerce

interface CropRecommendation {
  crop: string;
  successProbability: number;
  expectedYield: string;
  plantingTime: string;
  harvestTime: string;
  tips: string[];
}

interface ProductRecommendation {
  productId: string;
  productName: string;
  confidence: number;
  reason: string;
  price: number;
}

interface MarketPrediction {
  product: string;
  predictedDemand: 'high' | 'medium' | 'low';
  suggestedPrice: number;
  confidence: number;
  factors: string[];
}

interface WeatherData {
  temperature: number;
  humidity: number;
  rainfall: number;
  season: 'dry' | 'rainy';
}

class AIService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.REACT_APP_AI_API_KEY || '';
    this.baseUrl = 'http://localhost:3001/api/ai';
  }

  // 1. CROP RECOMMENDATION SYSTEM
  async recommendCrops(params: {
    location: string;
    soilType: string;
    farmSize: number;
    season?: string;
    previousCrops?: string[];
  }): Promise<CropRecommendation[]> {
    try {
      const response = await fetch(`${this.baseUrl}/crop-recommendations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify(params)
      });

      const result = await response.json();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Crop recommendation error:', error);
      return this.getFallbackCropRecommendations(params);
    }
  }

  // 2. PRODUCT RECOMMENDATIONS FOR CUSTOMERS
  async getProductRecommendations(userId: string, limit: number = 5): Promise<ProductRecommendation[]> {
    try {
      const response = await fetch(`${this.baseUrl}/product-recommendations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({ userId, limit })
      });

      const result = await response.json();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Product recommendation error:', error);
      return [];
    }
  }

  // 3. MARKET PREDICTIONS FOR FARMERS
  async getMarketPredictions(farmerId: string): Promise<MarketPrediction[]> {
    try {
      const response = await fetch(`${this.baseUrl}/market-predictions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({ farmerId })
      });

      const result = await response.json();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Market prediction error:', error);
      return [];
    }
  }

  // 4. CHATBOT INTEGRATION
  async getChatbotResponse(message: string, context?: any): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/chatbot`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({ message, context })
      });

      const result = await response.json();
      return result.success ? result.response : 'Sorry, I could not understand your question.';
    } catch (error) {
      console.error('Chatbot error:', error);
      return 'Sorry, I am currently unavailable. Please try again later.';
    }
  }

  // 5. IMAGE ANALYSIS FOR QUALITY CONTROL
  async analyzeProductImage(imageFile: File): Promise<{
    quality: 'excellent' | 'good' | 'fair' | 'poor';
    freshness: number; // 0-100
    defects: string[];
    grade: 'A' | 'B' | 'C';
    suggestions: string[];
  }> {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await fetch(`${this.baseUrl}/image-analysis`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: formData
      });

      const result = await response.json();
      return result.success ? result.data : this.getFallbackImageAnalysis();
    } catch (error) {
      console.error('Image analysis error:', error);
      return this.getFallbackImageAnalysis();
    }
  }

  // 6. WEATHER-BASED FARMING ADVICE
  async getFarmingAdvice(location: string): Promise<{
    currentWeather: WeatherData;
    advice: string[];
    warnings: string[];
    recommendations: string[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/farming-advice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({ location })
      });

      const result = await response.json();
      return result.success ? result.data : this.getFallbackFarmingAdvice();
    } catch (error) {
      console.error('Farming advice error:', error);
      return this.getFallbackFarmingAdvice();
    }
  }

  // 7. PRICE OPTIMIZATION
  async getOptimalPrice(productId: string, currentPrice: number): Promise<{
    suggestedPrice: number;
    confidence: number;
    reasoning: string;
    marketFactors: string[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/price-optimization`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({ productId, currentPrice })
      });

      const result = await response.json();
      return result.success ? result.data : {
        suggestedPrice: currentPrice,
        confidence: 0.5,
        reasoning: 'Using current price as baseline',
        marketFactors: ['Insufficient data for analysis']
      };
    } catch (error) {
      console.error('Price optimization error:', error);
      return {
        suggestedPrice: currentPrice,
        confidence: 0.5,
        reasoning: 'Error in analysis',
        marketFactors: ['Analysis unavailable']
      };
    }
  }

  // FALLBACK METHODS (when AI service is unavailable)
  private getFallbackCropRecommendations(params: any): CropRecommendation[] {
    // Basic recommendations based on Cameroon's agricultural patterns
    const cameroonCrops: CropRecommendation[] = [
      {
        crop: 'Plantain',
        successProbability: 0.85,
        expectedYield: '15-25 tons/hectare',
        plantingTime: 'March-May',
        harvestTime: '12-15 months',
        tips: ['Requires well-drained soil', 'Plant during rainy season', 'Space plants 3m apart']
      },
      {
        crop: 'Cassava',
        successProbability: 0.90,
        expectedYield: '10-20 tons/hectare',
        plantingTime: 'March-June',
        harvestTime: '8-12 months',
        tips: ['Drought resistant', 'Grows in poor soil', 'Harvest before rainy season']
      },
      {
        crop: 'Cocoyam',
        successProbability: 0.80,
        expectedYield: '8-15 tons/hectare',
        plantingTime: 'April-June',
        harvestTime: '6-9 months',
        tips: ['Prefers humid conditions', 'Needs rich organic soil', 'Regular watering required']
      }
    ];

    return cameroonCrops.slice(0, 3);
  }

  private getFallbackImageAnalysis() {
    return {
      quality: 'good' as const,
      freshness: 75,
      defects: [],
      grade: 'B' as const,
      suggestions: ['Image analysis temporarily unavailable']
    };
  }

  private getFallbackFarmingAdvice() {
    return {
      currentWeather: {
        temperature: 28,
        humidity: 75,
        rainfall: 150,
        season: 'rainy' as const
      },
      advice: ['Monitor crops for fungal diseases during rainy season'],
      warnings: ['Heavy rains expected this week'],
      recommendations: ['Consider harvesting mature crops before heavy rains']
    };
  }
}

export default new AIService();
