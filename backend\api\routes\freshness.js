const express = require('express');
const freshnessController = require('../controllers/freshnessController');
const {authenticateToken} = require('../middleware/auth');

const router = express.Router();

router.get('/products/:productId', freshnessController.getProductFreshness);
router.post('/products/:productId', authenticateToken, requireRole(['vendor', 'admin']), freshnessController.updateProductFreshness);
router.get('/metrics', freshnessController.getFreshnessMetrics);
router.get('/recommendations', freshnessController.getFreshnessRecommendations);

module.exports = router;