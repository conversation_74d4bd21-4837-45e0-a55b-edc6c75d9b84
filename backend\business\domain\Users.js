class User {
    constructor(id, email, firstName, lastName, role) {
      this.id = id;
      this.email = email;
      this.firstName = firstName;
      this.lastName = lastName;
      this.role = role;
    }
  
    get fullName() {
      return `${this.firstName} ${this.lastName}`;
    }
  
    isAdmin() {
      return this.role === 'admin';
    }
  
    toJSON() {
      return {
        id: this.id,
        email: this.email,
        firstName: this.firstName,
        lastName: this.lastName,
        fullName: this.fullName,
        role: this.role
      };
    }
  
    // Add more user-specific methods as needed
  }
  
  module.exports = User;