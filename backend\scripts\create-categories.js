const { connectDB } = require('../config/database');
const { Category } = require('../data/models');

async function createCategories() {
  try {
    console.log(' Creating initial categories...');
    
    // Connect to database
    await connectDB();
    console.log(' Database connected');

    // Create categories
    const categories = [
      {
        name: 'Vegetables',
        description: 'Fresh vegetables from Cameroon farms',
        isActive: true
      },
      {
        name: 'Fruits',
        description: 'Organic fruits grown in Cameroon',
        isActive: true
      },
      {
        name: 'Grains & Cereals',
        description: 'Locally grown grains and cereals',
        isActive: true
      },
      {
        name: 'Dairy Products',
        description: 'Fresh dairy products',
        isActive: true
      },
      {
        name: 'Meat & Poultry',
        description: 'Locally raised meat and poultry',
        isActive: true
      },
      {
        name: 'Spices & Herbs',
        description: 'Traditional Cameroonian spices and herbs',
        isActive: true
      },
      {
        name: 'Cocoa & Coffee',
        description: 'Premium cocoa beans and coffee',
        isActive: true
      },
      {
        name: 'Nuts & Seeds',
        description: 'Various nuts and seeds',
        isActive: true
      }
    ];

    // Check if categories already exist
    const existingCategories = await Category.findAll();
    if (existingCategories.length > 0) {
      console.log(` Categories already exist (${existingCategories.length} found)`);
      return;
    }

    // Create categories
    const createdCategories = await Category.bulkCreate(categories, { 
      ignoreDuplicates: true 
    });
    
    console.log(` Created ${createdCategories.length} categories`);
    console.log('Categories:', createdCategories.map(c => c.name).join(', '));

  } catch (error) {
    console.error(' Error creating categories:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createCategories().then(() => {
    console.log(' Categories creation completed');
    process.exit(0);
  });
}

module.exports = createCategories;
