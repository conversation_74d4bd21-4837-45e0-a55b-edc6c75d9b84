const { connectDB } = require('../config/database');
const { initializeModels } = require('../data/models');
const fs = require('fs');
const path = require('path');

async function runMigrations() {
  try {
    console.log(' Starting database migration...');
    
    // Connect to database
    const sequelize = await connectDB();
    console.log(' Database connected');
    
    // Initialize models
    await initializeModels();
    console.log(' Models initialized');
    
    // Sync database (create tables)
    await sequelize.sync({ alter: true });
    console.log(' Database schema synchronized');
    
    console.log(' Migration completed successfully');
  } catch (error) {
    console.error(' Migration failed:', error);
    process.exit(1);
  }
}

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to database
    const sequelize = await connectDB();
    console.log('Database connected');
    
    // Get model references
    const { User, Category, Product, Farmer } = require('../data/models');
    
    // Create categories
    const categories = await Category.bulkCreate([
      { name: 'Vegetables', description: 'Fresh vegetables from Cameroon farms' },
      { name: 'Fruits', description: 'Organic fruits grown in Cameroon' },
      { name: 'Grains', description: 'Locally grown grains and cereals' },
      { name: 'Dairy', description: 'Fresh dairy products' },
      { name: 'Meat', description: 'Locally raised meat products' },
      { name: 'Spices', description: 'Traditional Cameroonian spices' }
    ]);
    console.log(' Categories created');
    
    // Create admin user
    const adminUser = await User.create({
      email: '<EMAIL>',
      phone: '+237600000000',
      password: '$2a$10$yCzWZDx.h8J5APKiVH7kEOtXl5nKjRZ9n5H1EyBtAqaFj5vW0CuMG', // 'password123'
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
      status: 'ACTIVE',
      emailVerified: true,
      phoneVerified: true
    });
    console.log(' Admin user created');
    
    // Create farmer user
    const farmerUser = await User.create({
      email: '<EMAIL>',
      phone: '+237600000001',
      password: '$2a$10$yCzWZDx.h8J5APKiVH7kEOtXl5nKjRZ9n5H1EyBtAqaFj5vW0CuMG', // 'password123'
      firstName: 'John',
      lastName: 'Farmer',
      role: 'FARMER',
      status: 'ACTIVE',
      emailVerified: true,
      phoneVerified: true
    });
    
    // Create farmer profile
    const farmer = await Farmer.create({
      userId: farmerUser.id,
      farmName: 'Green Valley Farm',
      farmDescription: 'Organic farm in the Southwest region of Cameroon',
      farmLocation: 'Southwest Region, Cameroon',
      latitude: 4.1537,
      longitude: 9.2920,
      verified: true
    });
    console.log(' Farmer created');
    
    // Create sample products
    const products = await Product.bulkCreate([
      {
        name: 'Premium Cocoa Beans',
        description: 'High-quality cocoa beans from Southwest Cameroon',
        price: 2500,
        unit: 'kg',
        stock: 100,
        minOrder: 5,
        images: ['https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=500&h=500&fit=crop'],
        status: 'ACTIVE',
        farmerId: farmer.id,
        categoryId: categories[1].id,
        harvestDate: new Date(),
        expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
        isOrganic: true,
        isFairTrade: true
      },
      {
        name: 'Fresh Plantains',
        description: 'Locally grown plantains from Cameroon',
        price: 1200,
        unit: 'bunch',
        stock: 50,
        minOrder: 1,
        images: ['https://images.unsplash.com/photo-1603052875302-d376b7c0638a?w=500&h=500&fit=crop'],
        status: 'ACTIVE',
        farmerId: farmer.id,
        categoryId: categories[1].id,
        harvestDate: new Date(),
        expiryDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        isOrganic: true,
        isFairTrade: false
      },
      {
        name: 'Cassava',
        description: 'Fresh cassava roots from Central Cameroon',
        price: 800,
        unit: 'kg',
        stock: 200,
        minOrder: 5,
        images: ['https://images.unsplash.com/photo-1598512752271-33f913a5af13?w=500&h=500&fit=crop'],
        status: 'ACTIVE',
        farmerId: farmer.id,
        categoryId: categories[0].id,
        harvestDate: new Date(),
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isOrganic: false,
        isFairTrade: true
      }
    ]);
    console.log(' Sample products created');
    
    console.log( 'Database seeding completed successfully');
  } catch (error) {
    console.error(' Seeding failed:', error);
    process.exit(1);
  }
}

// Add command line arguments to run either migration or seeding
const args = process.argv.slice(2);
if (args.includes('--seed')) {
  seedDatabase();
} else {
  runMigrations();
}