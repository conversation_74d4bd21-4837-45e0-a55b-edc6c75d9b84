const fs = require('fs');
const path = require('path');
const config = require('../config/config');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Log levels
const LOG_LEVELS = {
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG'
};

// Logger class
class Logger {
  constructor() {
    this.logFile = path.join(logsDir, 'app.log');
    this.errorFile = path.join(logsDir, 'error.log');
    this.accessFile = path.join(logsDir, 'access.log');
  }

  // Format log message
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...meta
    };
    return JSON.stringify(logEntry) + '\n';
  }

  // Write to file
  writeToFile(filename, content) {
    try {
      fs.appendFileSync(filename, content);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  // Log methods
  error(message, meta = {}) {
    const formattedMessage = this.formatMessage(LOG_LEVELS.ERROR, message, meta);
    
    if (config.isDevelopment) {
      console.error(`🔴 ERROR: ${message}`, meta);
    }
    
    this.writeToFile(this.logFile, formattedMessage);
    this.writeToFile(this.errorFile, formattedMessage);
  }

  warn(message, meta = {}) {
    const formattedMessage = this.formatMessage(LOG_LEVELS.WARN, message, meta);
    
    if (config.isDevelopment) {
      console.warn(`🟡 WARN: ${message}`, meta);
    }
    
    this.writeToFile(this.logFile, formattedMessage);
  }

  info(message, meta = {}) {
    const formattedMessage = this.formatMessage(LOG_LEVELS.INFO, message, meta);
    
    if (config.isDevelopment) {
      console.log(`🔵 INFO: ${message}`, meta);
    }
    
    this.writeToFile(this.logFile, formattedMessage);
  }

  debug(message, meta = {}) {
    if (!config.isDevelopment) return;
    
    const formattedMessage = this.formatMessage(LOG_LEVELS.DEBUG, message, meta);
    console.log(`🟢 DEBUG: ${message}`, meta);
    this.writeToFile(this.logFile, formattedMessage);
  }

  // Log HTTP requests
  access(req, res, responseTime) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.originalUrl,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userId: req.user?.id || null,
      userRole: req.user?.role || null
    };

    const formattedMessage = JSON.stringify(logEntry) + '\n';
    this.writeToFile(this.accessFile, formattedMessage);

    if (config.isDevelopment) {
      const statusColor = res.statusCode >= 400 ? '🔴' : res.statusCode >= 300 ? '🟡' : '🟢';
      console.log(`${statusColor} ${req.method} ${req.originalUrl} - ${res.statusCode} - ${responseTime}ms`);
    }
  }
}

// Create logger instance
const logger = new Logger();

// Request logging middleware
const requestLogger = (req, res, next) => {
  const startTime = Date.now();

  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    logger.access(req, res, responseTime);
    originalEnd.apply(this, args);
  };

  next();
};

// Security logging middleware
const securityLogger = (req, res, next) => {
  // Log suspicious activities
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
  ];

  const url = req.originalUrl;
  const body = JSON.stringify(req.body);
  const query = JSON.stringify(req.query);

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(url) || pattern.test(body) || pattern.test(query)) {
      logger.warn('Suspicious request detected', {
        ip: req.ip,
        method: req.method,
        url: req.originalUrl,
        userAgent: req.get('User-Agent'),
        body: req.body,
        query: req.query,
        pattern: pattern.toString()
      });
      break;
    }
  }

  next();
};

// Database operation logger
const dbLogger = {
  logQuery: (query, duration) => {
    if (config.isDevelopment) {
      logger.debug('Database Query', {
        query: query.replace(/\s+/g, ' ').trim(),
        duration: `${duration}ms`
      });
    }
  },

  logError: (error, query) => {
    logger.error('Database Error', {
      error: error.message,
      query: query?.replace(/\s+/g, ' ').trim(),
      stack: error.stack
    });
  }
};

// Authentication logger
const authLogger = {
  logLogin: (userId, email, ip, success) => {
    const message = success ? 'User login successful' : 'User login failed';
    const logMethod = success ? 'info' : 'warn';
    
    logger[logMethod](message, {
      userId,
      email,
      ip,
      success
    });
  },

  logRegistration: (userId, email, role, ip) => {
    logger.info('User registration', {
      userId,
      email,
      role,
      ip
    });
  },

  logLogout: (userId, email, ip) => {
    logger.info('User logout', {
      userId,
      email,
      ip
    });
  },

  logPasswordReset: (email, ip) => {
    logger.info('Password reset requested', {
      email,
      ip
    });
  }
};

// Business operation logger
const businessLogger = {
  logProductCreated: (productId, farmerId, productName) => {
    logger.info('Product created', {
      productId,
      farmerId,
      productName
    });
  },

  logOrderPlaced: (orderId, userId, totalAmount) => {
    logger.info('Order placed', {
      orderId,
      userId,
      totalAmount
    });
  },

  logPaymentProcessed: (paymentId, orderId, amount, method, status) => {
    logger.info('Payment processed', {
      paymentId,
      orderId,
      amount,
      method,
      status
    });
  }
};

module.exports = {
  logger,
  requestLogger,
  securityLogger,
  dbLogger,
  authLogger,
  businessLogger
};
