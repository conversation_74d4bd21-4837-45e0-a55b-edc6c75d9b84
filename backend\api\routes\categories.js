// Import required modules
const express = require('express');
const { body } = require('express-validator');
const categoryController = require('../controllers/categoryController');
const {authenticateToken} = require('../middleware/auth');
const validate = require('../middleware/validate');

// Create a router
const router = express.Router();

// Define category routes
router.post(
  '/',
  authenticateToken,
  [
    body('name').isString().trim().isLength({ min: 2 }).withMessage('Category name must be at least 2 characters long')
  ],
  categoryController.createCategory
);

router.get('/', categoryController.getAllCategories);
router.get('/:id', categoryController.getCategoryById);

router.put(
  '/:id',
  authenticateToken,
  [
    body('name').isString().trim().isLength({ min: 2 }).withMessage('Category name must be at least 2 characters long')
  ],
  validate,
  categoryController.updateCategory
);

router.delete('/:id', authenticateToken, categoryController.deleteCategory);


// Export the router
module.exports = router;