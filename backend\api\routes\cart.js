const express = require('express');
const cartController = require('../controllers/cartController');
const { protect } = require('../../middleware/auth');

const router = express.Router();

// All cart routes require authentication
router.use(protect);

// Cart routes
router.get('/', cartController.getCart);
router.post('/add', cartController.addToCart);
router.put('/update/:itemId', cartController.updateCartItem);
router.delete('/remove/:itemId', cartController.removeFromCart);
router.delete('/clear', cartController.clearCart);

module.exports = router;