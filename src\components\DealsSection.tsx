import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Star, ShoppingCart, ArrowRight } from 'lucide-react';

interface Deal {
  id: string;
  name: string;
  image: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  rating: number;
  reviewCount: number;
  vendor: string;
  sold: number;
  total: number;
  timeLeft?: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  };
}

interface DealsSectionProps {
  title?: string;
  deals: Deal[];
  onViewAllDeals?: () => void;
  onAddToCart?: (dealId: string) => void;
  className?: string;
}

const DealsSection: React.FC<DealsSectionProps> = ({
  title = "Deals Of The Day",
  deals,
  onViewAllDeals,
  onAddToCart,
  className = ''
}) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const formatTime = (time: number) => {
    return time.toString().padStart(2, '0');
  };

  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
              {title}
            </h2>
            <p className="text-gray-600">Limited time offers on fresh products</p>
          </div>
          {onViewAllDeals && (
            <Button 
              variant="outline"
              onClick={onViewAllDeals}
              className="hidden md:flex items-center gap-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white"
            >
              All Deals
              <ArrowRight className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Deals Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {deals.map((deal) => (
            <Card 
              key={deal.id}
              className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-0 bg-white rounded-2xl overflow-hidden"
            >
              <div className="relative overflow-hidden">
                {/* Product Image */}
                <div className="relative bg-gray-50 aspect-square">
                  <img
                    src={deal.image}
                    alt={deal.name}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  
                  {/* Discount Badge */}
                  <Badge className="absolute top-3 left-3 bg-red-500 text-white font-bold px-2 py-1 rounded-full text-xs">
                    Save {deal.discount}%
                  </Badge>
                </div>
              </div>

              <CardContent className="p-4">
                {/* Vendor */}
                <div className="mb-2">
                  <span className="text-xs text-green-600 font-semibold">
                    {deal.vendor}
                  </span>
                </div>

                {/* Product Name */}
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-green-600 transition-colors duration-200">
                  {deal.name}
                </h3>

                {/* Rating */}
                <div className="flex items-center gap-1 mb-3">
                  <div className="flex">
                    {renderStars(deal.rating)}
                  </div>
                  <span className="text-xs text-gray-500">
                    ({deal.reviewCount})
                  </span>
                </div>

                {/* Price */}
                <div className="flex items-center gap-2 mb-4">
                  <span className="text-lg font-bold text-green-600">
                    {parseFloat(deal.salePrice || 0).toLocaleString()} FCFA
                  </span>
                  <span className="text-sm text-gray-400 line-through">
                    {parseFloat(deal.originalPrice || 0).toLocaleString()} FCFA
                  </span>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between text-xs text-gray-600 mb-1">
                    <span>Sold: {deal.sold}/{deal.total}</span>
                    <span>{Math.round((deal.sold / deal.total) * 100)}%</span>
                  </div>
                  <Progress 
                    value={(deal.sold / deal.total) * 100} 
                    className="h-2 bg-gray-200"
                  />
                </div>

                {/* Countdown Timer */}
                {deal.timeLeft && (
                  <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                    <p className="text-xs text-gray-600 mb-2 text-center">Time left:</p>
                    <div className="flex justify-center gap-1">
                      <div className="bg-green-600 text-white rounded px-1.5 py-1 text-xs font-bold text-center min-w-[28px]">
                        {formatTime(deal.timeLeft.days)}
                        <div className="text-[10px] opacity-80">d</div>
                      </div>
                      <div className="bg-green-600 text-white rounded px-1.5 py-1 text-xs font-bold text-center min-w-[28px]">
                        {formatTime(deal.timeLeft.hours)}
                        <div className="text-[10px] opacity-80">h</div>
                      </div>
                      <div className="bg-green-600 text-white rounded px-1.5 py-1 text-xs font-bold text-center min-w-[28px]">
                        {formatTime(deal.timeLeft.minutes)}
                        <div className="text-[10px] opacity-80">m</div>
                      </div>
                      <div className="bg-green-600 text-white rounded px-1.5 py-1 text-xs font-bold text-center min-w-[28px]">
                        {formatTime(deal.timeLeft.seconds)}
                        <div className="text-[10px] opacity-80">s</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Add to Cart Button */}
                <Button
                  className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2.5 rounded-lg transition-all duration-200 hover:shadow-lg"
                  onClick={() => onAddToCart?.(deal.id)}
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Add To Cart
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Mobile View All Button */}
        {onViewAllDeals && (
          <div className="flex justify-center mt-8 md:hidden">
            <Button 
              variant="outline"
              onClick={onViewAllDeals}
              className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white px-8"
            >
              View All Deals
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default DealsSection;
