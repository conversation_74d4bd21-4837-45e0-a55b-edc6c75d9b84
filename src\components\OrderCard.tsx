
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Package, MapPin, Calendar, Truck, Star } from "lucide-react";

interface OrderItem {
  name: string;
  quantity: number;
  price: number;
  farmer: string;
}

interface OrderCardProps {
  orderId: string;
  date: string;
  status: "pending" | "confirmed" | "shipped" | "delivered" | "cancelled";
  items: OrderItem[];
  total: number;
  deliveryAddress: string | object;
  estimatedDelivery?: string;
  trackingNumber?: string;
}

const OrderCard = ({
  orderId,
  date,
  status,
  items,
  total,
  deliveryAddress,
  estimatedDelivery,
  trackingNumber
}: OrderCardProps) => {
  // Helper function to format address
  const formatAddress = (address: string | object): string => {
    if (typeof address === 'string') {
      return address;
    }

    if (typeof address === 'object' && address !== null) {
      const addr = address as any;
      const parts = [
        addr.address,
        addr.city,
        addr.region,
        addr.postalCode
      ].filter(Boolean);

      return parts.join(', ') || 'Address not available';
    }

    return 'Address not available';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-700";
      case "confirmed": return "bg-blue-100 text-blue-700";
      case "shipped": return "bg-purple-100 text-purple-700";
      case "delivered": return "bg-green-100 text-green-700";
      case "cancelled": return "bg-red-100 text-red-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending": return <Package className="w-4 h-4" />;
      case "confirmed": return <Package className="w-4 h-4" />;
      case "shipped": return <Truck className="w-4 h-4" />;
      case "delivered": return <Package className="w-4 h-4" />;
      default: return <Package className="w-4 h-4" />;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">Order #{orderId}</CardTitle>
            <div className="flex items-center gap-2 mt-2 text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span className="text-sm">{date}</span>
            </div>
          </div>
          <Badge className={`${getStatusColor(status)} flex items-center gap-1`}>
            {getStatusIcon(status)}
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-2">
          {items.map((item, index) => (
            <div key={index} className="flex justify-between items-center text-sm">
              <div>
                <span className="font-medium">{item.name}</span>
                <span className="text-muted-foreground"> × {item.quantity}</span>
                <div className="text-xs text-muted-foreground">by {item.farmer}</div>
              </div>
              <span className="font-medium">{(item.price * item.quantity).toLocaleString()} FCFA</span>
            </div>
          ))}
        </div>
        
        <Separator />
        
        <div className="flex justify-between items-center font-semibold">
          <span>Total:</span>
          <span>{total.toLocaleString()} FCFA</span>
        </div>
        
        <div className="flex items-start gap-2 text-sm text-muted-foreground">
          <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
          <span>{formatAddress(deliveryAddress)}</span>
        </div>
        
        {estimatedDelivery && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Truck className="w-4 h-4" />
            <span>Estimated delivery: {estimatedDelivery}</span>
          </div>
        )}
        
        {trackingNumber && (
          <div className="bg-muted/30 p-3 rounded-lg">
            <p className="text-sm font-medium">Tracking Number</p>
            <p className="text-sm text-muted-foreground font-mono">{trackingNumber}</p>
          </div>
        )}
        
        <div className="flex gap-2">
          {status === "delivered" && (
            <Button size="sm" variant="outline" className="flex items-center gap-1">
              <Star className="w-4 h-4" />
              Rate Order
            </Button>
          )}
          <Button size="sm" variant="outline" className="flex-1">
            View Details
          </Button>
          {(status === "pending" || status === "confirmed") && (
            <Button size="sm" variant="destructive">
              Cancel
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default OrderCard;
