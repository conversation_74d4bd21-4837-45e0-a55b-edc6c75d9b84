const express = require('express');
const wishlistController = require('../controllers/wishlistController');
const { protect } = require('../../middleware/auth');

const router = express.Router();

// All wishlist routes require authentication
router.use(protect);

// Wishlist routes
router.get('/', wishlistController.getWishlist);
router.post('/add', wishlistController.addToWishlist);
router.delete('/remove/:productId', wishlistController.removeFromWishlist);
router.delete('/clear', wishlistController.clearWishlist);
router.get('/check/:productId', wishlistController.checkWishlist);
router.post('/move-to-cart', wishlistController.moveToCart);

module.exports = router;