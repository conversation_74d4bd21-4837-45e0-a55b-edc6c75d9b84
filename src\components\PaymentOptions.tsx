
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Smartphone, CreditCard, Coins, Users, CheckCircle } from "lucide-react";

const PaymentOptions = () => {
  const paymentMethods = [
    {
      title: "Mobile Money",
      providers: ["Orange Money", "MTN MoMo", "Express Union"],
      description: "Most popular payment method in Cameroon",
      icon: <Smartphone className="w-6 h-6" />,
      availability: "99% coverage",
      className: "mobile-money"
    },
    {
      title: "Cash on Delivery",
      providers: ["Pay on delivery", "Trusted local agents"],
      description: "Pay when you receive your products",
      icon: <Coins className="w-6 h-6" />,
      availability: "All regions",
      className: "payment-card bg-gradient-to-r from-amber-500 to-orange-600"
    },
    {
      title: "Bank Transfer",
      providers: ["All major banks", "Microfinance institutions"],
      description: "Traditional banking methods",
      icon: <CreditCard className="w-6 h-6" />,
      availability: "Urban areas",
      className: "payment-card bg-gradient-to-r from-indigo-500 to-purple-600"
    },
    {
      title: "Group Buying",
      providers: ["Community cooperatives", "Bulk orders"],
      description: "Collective purchasing for better prices",
      icon: <Users className="w-6 h-6" />,
      availability: "Rural communities",
      className: "payment-card bg-gradient-to-r from-teal-500 to-cyan-600"
    }
  ];

  return (
    <Card className="enhanced-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="w-5 h-5 text-green-600" />
          Flexible Payment Solutions
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Multiple payment options for all Cameroonians
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {paymentMethods.map((method, index) => (
            <div key={index} className={method.className}>
              <div className="flex items-center gap-3 mb-3">
                {method.icon}
                <div>
                  <h4 className="font-semibold">{method.title}</h4>
                  <Badge variant="secondary" className="text-xs bg-white/20">
                    {method.availability}
                  </Badge>
                </div>
              </div>
              
              <p className="text-sm mb-3 opacity-90">
                {method.description}
              </p>
              
              <div className="space-y-1">
                {method.providers.map((provider, providerIndex) => (
                  <div key={providerIndex} className="flex items-center gap-2 text-sm">
                    <CheckCircle className="w-3 h-3" />
                    {provider}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="border-t pt-4">
          <h4 className="font-semibold mb-3">Payment Security Features</h4>
          <div className="grid grid-cols-3 gap-3 text-center">
            <div className="p-3 bg-green-50 rounded-lg">
              <CheckCircle className="w-5 h-5 mx-auto mb-1 text-green-600" />
              <div className="text-xs font-medium">Secure</div>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <CheckCircle className="w-5 h-5 mx-auto mb-1 text-blue-600" />
              <div className="text-xs font-medium">Verified</div>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <CheckCircle className="w-5 h-5 mx-auto mb-1 text-purple-600" />
              <div className="text-xs font-medium">Insured</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentOptions;
