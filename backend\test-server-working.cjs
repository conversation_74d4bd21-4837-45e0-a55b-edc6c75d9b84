// Test if server is working
const axios = require('axios');

async function testServer() {
  try {
    console.log('🧪 Testing if server is working...');
    
    const response = await axios.get('http://localhost:3001/api/products');
    console.log('✅ Server is working!');
    console.log(`   Found ${response.data.data.products.length} products`);
    
  } catch (error) {
    console.log('❌ Server not responding:', error.message);
  }
}

testServer();
