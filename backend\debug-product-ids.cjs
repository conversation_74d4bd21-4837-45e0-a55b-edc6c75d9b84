// Debug product ID mismatch issue
const mysql = require('mysql2/promise');

async function debugProductIds() {
  console.log('🔍 Debugging Product ID Issues...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    // Check what products exist in database
    console.log('📦 Products in Database:');
    const [products] = await connection.execute(`
      SELECT id, name, price, stock 
      FROM products 
      ORDER BY name 
      LIMIT 10
    `);
    
    console.log(`Found ${products.length} products:`);
    products.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}`);
      console.log(`      ID: ${product.id}`);
      console.log(`      Price: ${product.price} FCFA`);
      console.log('');
    });
    
    // Check what the API returns
    console.log('🌐 Testing API Response:');
    const axios = require('axios');
    
    try {
      const apiResponse = await axios.get('http://localhost:3001/api/products');
      const apiProducts = apiResponse.data.data.products;
      
      console.log(`API returned ${apiProducts.length} products:`);
      apiProducts.slice(0, 5).forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.name}`);
        console.log(`      ID: ${product.id}`);
        console.log(`      Type: ${typeof product.id}`);
        console.log('');
      });
      
      // Check if IDs match
      const dbIds = products.map(p => p.id);
      const apiIds = apiProducts.map(p => p.id);
      
      console.log('🔗 ID Comparison:');
      console.log(`   Database IDs (first 3): ${dbIds.slice(0, 3)}`);
      console.log(`   API IDs (first 3): ${apiIds.slice(0, 3)}`);
      
      // Check for mismatches
      const mismatchedIds = apiIds.filter(id => !dbIds.includes(id));
      if (mismatchedIds.length > 0) {
        console.log(`❌ Mismatched IDs found: ${mismatchedIds.length}`);
        mismatchedIds.slice(0, 3).forEach(id => {
          console.log(`   - ${id} (type: ${typeof id})`);
        });
      } else {
        console.log('✅ All API IDs match database IDs');
      }
      
    } catch (apiError) {
      console.log('❌ API Error:', apiError.message);
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugProductIds();
