const express = require('express');
const { Notification } = require('../data/models');
const { protect } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const router = express.Router();

// @desc    Get user's notifications
// @route   GET /api/notifications
// @access  Private
const getNotifications = asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, type, read } = req.query;
  const offset = (page - 1) * limit;

  const whereClause = { UserId: req.user.id };
  
  if (type) {
    whereClause.type = type;
  }
  
  if (read !== undefined) {
    whereClause.read = read === 'true';
  }

  const notifications = await Notification.findAndCountAll({
    where: whereClause,
    order: [['createdAt', 'DESC']],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  // Get unread count
  const unreadCount = await Notification.count({
    where: { 
      UserId: req.user.id, 
      read: false 
    }
  });

  res.json({
    success: true,
    data: {
      notifications: notifications.rows,
      unreadCount,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(notifications.count / limit),
        totalNotifications: notifications.count,
        hasNext: page * limit < notifications.count,
        hasPrev: page > 1
      }
    }
  });
});

// @desc    Mark notification as read
// @route   PUT /api/notifications/:id/read
// @access  Private
const markAsRead = asyncHandler(async (req, res) => {
  const notification = await Notification.findOne({
    where: { 
      id: req.params.id,
      UserId: req.user.id 
    }
  });

  if (!notification) {
    return res.status(404).json({
      success: false,
      message: 'Notification not found'
    });
  }

  notification.read = true;
  notification.readAt = new Date();
  await notification.save();

  res.json({
    success: true,
    message: 'Notification marked as read',
    data: {
      notification
    }
  });
});

// @desc    Mark notification as unread
// @route   PUT /api/notifications/:id/unread
// @access  Private
const markAsUnread = asyncHandler(async (req, res) => {
  const notification = await Notification.findOne({
    where: { 
      id: req.params.id,
      UserId: req.user.id 
    }
  });

  if (!notification) {
    return res.status(404).json({
      success: false,
      message: 'Notification not found'
    });
  }

  notification.read = false;
  notification.readAt = null;
  await notification.save();

  res.json({
    success: true,
    message: 'Notification marked as unread',
    data: {
      notification
    }
  });
});

// @desc    Mark all notifications as read
// @route   PUT /api/notifications/mark-all-read
// @access  Private
const markAllAsRead = asyncHandler(async (req, res) => {
  await Notification.update(
    { 
      read: true,
      readAt: new Date()
    },
    { 
      where: { 
        UserId: req.user.id,
        read: false
      }
    }
  );

  res.json({
    success: true,
    message: 'All notifications marked as read'
  });
});

// @desc    Delete notification
// @route   DELETE /api/notifications/:id
// @access  Private
const deleteNotification = asyncHandler(async (req, res) => {
  const notification = await Notification.findOne({
    where: { 
      id: req.params.id,
      UserId: req.user.id 
    }
  });

  if (!notification) {
    return res.status(404).json({
      success: false,
      message: 'Notification not found'
    });
  }

  await notification.destroy();

  res.json({
    success: true,
    message: 'Notification deleted successfully'
  });
});

// @desc    Clear all notifications
// @route   DELETE /api/notifications/clear
// @access  Private
const clearAllNotifications = asyncHandler(async (req, res) => {
  await Notification.destroy({
    where: { UserId: req.user.id }
  });

  res.json({
    success: true,
    message: 'All notifications cleared successfully'
  });
});

// @desc    Create notification (Internal function)
// @access  Internal
const createNotification = async (userId, type, title, message, actionUrl = null) => {
  try {
    const notification = await Notification.create({
      UserId: userId,
      type,
      title,
      message,
      actionUrl,
      read: false
    });
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    return null;
  }
};

// @desc    Create bulk notifications (Internal function)
// @access  Internal
const createBulkNotifications = async (userIds, type, title, message, actionUrl = null) => {
  try {
    const notifications = userIds.map(userId => ({
      UserId: userId,
      type,
      title,
      message,
      actionUrl,
      read: false
    }));

    await Notification.bulkCreate(notifications);
    return true;
  } catch (error) {
    console.error('Error creating bulk notifications:', error);
    return false;
  }
};

// @desc    Get notification statistics
// @route   GET /api/notifications/stats
// @access  Private
const getNotificationStats = asyncHandler(async (req, res) => {
  const stats = await Notification.findAll({
    where: { UserId: req.user.id },
    attributes: [
      'type',
      [Notification.sequelize.fn('COUNT', '*'), 'count'],
      [Notification.sequelize.fn('SUM', Notification.sequelize.literal('CASE WHEN read = false THEN 1 ELSE 0 END')), 'unread']
    ],
    group: ['type']
  });

  const totalCount = await Notification.count({
    where: { UserId: req.user.id }
  });

  const totalUnread = await Notification.count({
    where: { 
      UserId: req.user.id,
      read: false 
    }
  });

  res.json({
    success: true,
    data: {
      byType: stats,
      total: {
        count: totalCount,
        unread: totalUnread
      }
    }
  });
});

// Routes
router.get('/', protect, getNotifications);
router.get('/stats', protect, getNotificationStats);
router.put('/:id/read', protect, markAsRead);
router.put('/:id/unread', protect, markAsUnread);
router.put('/mark-all-read', protect, markAllAsRead);
router.delete('/:id', protect, deleteNotification);
router.delete('/clear', protect, clearAllNotifications);

// Export functions for internal use
module.exports = {
  router,
  createNotification,
  createBulkNotifications
};
