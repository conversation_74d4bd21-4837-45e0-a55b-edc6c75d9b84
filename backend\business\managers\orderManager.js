const orderService = require('../services/orderService');
const cartService = require('../services/cartService');

class OrderManager {
  async createOrderFromCart(userId) {
    const cart = await cartService.getCartContents(userId);
    if (!cart || cart.cartItems.length === 0) {
      throw new Error('Cart is empty');
    }

    const orderItems = cart.cartItems.map(item => ({
      productId: item.productId,
      quantity: item.quantity
    }));

    const order = await orderService.createOrder(userId, orderItems);
    await cartService.clearCart(userId);
    return order;
  }

  async getOrderById(id) {
    return await orderService.getOrderById(id);
  }

  async updateOrderStatus(id, status) {
    return await orderService.updateOrderStatus(id, status);
  }

  async getUserOrders(userId) {
    return await orderService.getUserOrders(userId);
  }

  async cancelOrder(id) {
    return await orderService.cancelOrder(id);
  }

  // Add more complex order management functions here
}

module.exports = new OrderManager();