const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to the database
const dbPath = path.join(__dirname, 'data', 'database.sqlite');
const db = new sqlite3.Database(dbPath);

console.log('🔧 Creating missing tables...');

// Create Cart table
const createCartTable = `
CREATE TABLE IF NOT EXISTS Carts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  UserId INTEGER NOT NULL,
  createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (UserId) REFERENCES Users(id) ON DELETE CASCADE
);
`;

// Create CartItem table
const createCartItemTable = `
CREATE TABLE IF NOT EXISTS CartItems (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  CartId INTEGER NOT NULL,
  ProductId INTEGER NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  price DECIMAL(10,2) NOT NULL,
  createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (CartId) REFERENCES Carts(id) ON DELETE CASCADE,
  FOREIGN KEY (ProductId) REFERENCES Products(id) ON DELETE CASCADE
);
`;

// Create Wishlist table
const createWishlistTable = `
CREATE TABLE IF NOT EXISTS Wishlists (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  UserId INTEGER NOT NULL,
  ProductId INTEGER NOT NULL,
  createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (UserId) REFERENCES Users(id) ON DELETE CASCADE,
  FOREIGN KEY (ProductId) REFERENCES Products(id) ON DELETE CASCADE,
  UNIQUE(UserId, ProductId)
);
`;

// Execute table creation
db.serialize(() => {
  db.run(createCartTable, (err) => {
    if (err) {
      console.error('❌ Error creating Carts table:', err);
    } else {
      console.log('✅ Carts table created/verified');
    }
  });

  db.run(createCartItemTable, (err) => {
    if (err) {
      console.error('❌ Error creating CartItems table:', err);
    } else {
      console.log('✅ CartItems table created/verified');
    }
  });

  db.run(createWishlistTable, (err) => {
    if (err) {
      console.error('❌ Error creating Wishlists table:', err);
    } else {
      console.log('✅ Wishlists table created/verified');
    }
  });

  // Close the database connection
  db.close((err) => {
    if (err) {
      console.error('❌ Error closing database:', err);
    } else {
      console.log('🎉 Database tables created successfully!');
    }
  });
});
