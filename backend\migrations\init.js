const { sequelize } = require('../config/database');
const fs = require('fs');
const path = require('path');

async function runMigrations() {
  try {
    // Read all model files
    const modelsDir = path.join(__dirname, '../data/models');
    const modelFiles = fs.readdirSync(modelsDir)
      .filter(file => file.endsWith('.js') && file !== 'index.js');
    
    // Import all models to register them with Sequelize
    modelFiles.forEach(file => {
      require(path.join(modelsDir, file));
    });
    
    // Sync all models with the database
    console.log('Syncing database...');
    await sequelize.sync({ force: true });
    console.log('Database synced successfully');
    
    // Add seed data if needed
    // await seedDatabase();
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    process.exit();
  }
}

runMigrations();