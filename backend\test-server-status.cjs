// Test if server is running
const axios = require('axios');

async function testServer() {
  console.log('🧪 Testing Server Status...\n');
  
  try {
    console.log('Testing connection to http://localhost:3001...');
    
    const response = await axios.get('http://localhost:3001/api/products', {
      timeout: 5000
    });
    
    console.log('✅ Server is running!');
    console.log('   Status:', response.status);
    console.log('   Products found:', response.data.data?.products?.length || 0);
    
    // Test auth endpoints
    console.log('\n🔐 Testing Auth Endpoints...');
    
    try {
      const authTest = await axios.post('http://localhost:3001/api/auth/register', {
        firstName: 'Test',
        lastName: 'User',
        email: `test.${Date.now()}@example.com`,
        password: 'Test123!',
        role: 'CUSTOMER'
      }, { timeout: 5000 });
      
      console.log('✅ Auth endpoint working');
      console.log('   Registration successful');
      
    } catch (authError) {
      if (authError.response?.status === 400) {
        console.log('✅ Auth endpoint working (validation error expected)');
      } else {
        console.log('❌ Auth endpoint failed:', authError.message);
      }
    }
    
  } catch (error) {
    console.log('❌ Server not responding:');
    console.log('   Error:', error.message);
    console.log('   Code:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Server is not running. Please start it with: node server.js');
    }
  }
}

testServer();
