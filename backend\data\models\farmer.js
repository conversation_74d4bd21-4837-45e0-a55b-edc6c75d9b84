const { DataTypes } = require('sequelize');
const { getSequelize } = require('../../config/database');

const Farmer = getSequelize().define('Farmer', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    unique: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  farmName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  farmDescription: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  farmSize: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Farm size in hectares'
  },
  farmLocation: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 8),
    allowNull: true,
  },
  longitude: {
    type: DataTypes.DECIMAL(11, 8),
    allowNull: true,
  },
  certifications: {
    type: DataTypes.JSON,
    defaultValue: [],
    comment: 'Array of certifications like organic, fair trade, etc.'
  },
  specializations: {
    type: DataTypes.JSON,
    defaultValue: [],
    comment: 'Array of crops they specialize in'
  },
  verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0,
  },
  totalSales: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
}, {
  tableName: 'farmers',
  timestamps: true,
});

module.exports = Farmer;
