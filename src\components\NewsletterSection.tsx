import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Mail, CheckCircle, Leaf, Bell } from 'lucide-react';
import { toast } from 'sonner';

interface NewsletterSectionProps {
  title?: string;
  subtitle?: string;
  placeholder?: string;
  onSubscribe?: (email: string) => Promise<void>;
  className?: string;
  variant?: 'default' | 'compact' | 'sidebar';
}

const NewsletterSection: React.FC<NewsletterSectionProps> = ({
  title = "Stay home & get your daily needs from our shop",
  subtitle = "Start Your Daily Shopping with Agri Connect",
  placeholder = "Your email address",
  onSubscribe,
  className = '',
  variant = 'default'
}) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast.error('Please enter your email address');
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    try {
      if (onSubscribe) {
        await onSubscribe(email);
      }
      setIsSubscribed(true);
      toast.success('Successfully subscribed to our newsletter!');
      setEmail('');
    } catch (error) {
      toast.error('Failed to subscribe. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (variant === 'compact') {
    return (
      <div className={`bg-green-50 rounded-2xl p-6 ${className}`}>
        <div className="flex items-center gap-3 mb-4">
          <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
            <Mail className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900">Newsletter</h3>
            <p className="text-sm text-gray-600">Get fresh updates</p>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-3">
          <Input
            type="email"
            placeholder={placeholder}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="bg-white border-gray-200"
            disabled={isLoading || isSubscribed}
          />
          <Button 
            type="submit" 
            className="w-full bg-green-600 hover:bg-green-700"
            disabled={isLoading || isSubscribed}
          >
            {isLoading ? 'Subscribing...' : isSubscribed ? 'Subscribed!' : 'Subscribe'}
          </Button>
        </form>
      </div>
    );
  }

  if (variant === 'sidebar') {
    return (
      <div className={`bg-gradient-to-br from-green-600 to-emerald-600 rounded-2xl p-6 text-white ${className}`}>
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Bell className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-xl font-bold mb-2">Fresh Vegetables</h3>
          <h4 className="text-2xl font-black mb-2">Big discount</h4>
          <p className="text-green-100 text-sm">Save up to 50% off on your first order</p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-3">
          <Input
            type="email"
            placeholder={placeholder}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="bg-white/90 border-0 text-gray-900 placeholder-gray-500"
            disabled={isLoading || isSubscribed}
          />
          <Button 
            type="submit" 
            className="w-full bg-white text-green-600 hover:bg-gray-100 font-bold"
            disabled={isLoading || isSubscribed}
          >
            {isLoading ? 'Subscribing...' : isSubscribed ? 'Subscribed!' : 'Subscribe'}
          </Button>
        </form>
      </div>
    );
  }

  // Default variant
  return (
    <section className={`py-16 bg-gradient-to-br from-green-50 via-emerald-50 to-green-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div>
            <Badge className="mb-6 bg-green-600 text-white px-4 py-2 text-sm font-semibold">
              <Leaf className="w-4 h-4 mr-2" />
              Newsletter Subscription
            </Badge>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              {title}
            </h2>
            
            <p className="text-xl text-gray-600 mb-8">
              {subtitle}
            </p>

            {/* Benefits */}
            <div className="space-y-4 mb-8">
              <div className="flex items-center text-gray-700">
                <CheckCircle className="w-5 h-5 text-green-600 mr-3 flex-shrink-0" />
                <span>Get notified about fresh arrivals and special deals</span>
              </div>
              <div className="flex items-center text-gray-700">
                <CheckCircle className="w-5 h-5 text-green-600 mr-3 flex-shrink-0" />
                <span>Exclusive discounts for newsletter subscribers</span>
              </div>
              <div className="flex items-center text-gray-700">
                <CheckCircle className="w-5 h-5 text-green-600 mr-3 flex-shrink-0" />
                <span>Weekly updates from local farmers</span>
              </div>
            </div>

            {/* Newsletter Form */}
            <form onSubmit={handleSubmit} className="max-w-md">
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex-1">
                  <Input
                    type="email"
                    placeholder={placeholder}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="bg-white border-gray-200 py-3 px-4 rounded-xl text-gray-900 placeholder-gray-500 focus:border-green-500 focus:ring-green-500"
                    disabled={isLoading || isSubscribed}
                  />
                </div>
                <Button 
                  type="submit" 
                  className="bg-green-600 hover:bg-green-700 text-white font-bold px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isLoading || isSubscribed}
                >
                  {isLoading ? (
                    'Subscribing...'
                  ) : isSubscribed ? (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Subscribed!
                    </>
                  ) : (
                    'Subscribe'
                  )}
                </Button>
              </div>
              <p className="text-sm text-gray-500 mt-3">
                We respect your privacy. Unsubscribe at any time.
              </p>
            </form>
          </div>

          {/* Right Content - Illustration */}
          <div className="hidden lg:flex justify-center">
            <div className="relative">
              <div className="w-80 h-80 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full opacity-20 absolute -top-10 -left-10" />
              <div className="w-64 h-64 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full opacity-20 absolute -bottom-10 -right-10" />
              <img 
                src="/placeholder.svg" 
                alt="Newsletter illustration" 
                className="relative z-10 w-96 h-96 object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsletterSection;
