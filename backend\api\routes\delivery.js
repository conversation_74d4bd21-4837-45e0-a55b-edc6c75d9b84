const express = require('express');
const { Delivery, Order, OrderItem, Product, User } = require('../../data/models');
const { protect } = require('../../middleware/auth');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// @desc    Get deliveries assigned to delivery agent
// @route   GET /api/deliveries/agent
// @access  Private (Delivery Agent)
const getAgentDeliveries = asyncHandler(async (req, res) => {
  const deliveries = await Delivery.findAll({
    where: { deliveryAgentId: req.user.id },
    include: [
      {
        model: Order,
        include: [
          {
            model: User,
            attributes: ['id', 'firstName', 'lastName', 'email']
          },
          {
            model: OrderItem,
            include: [
              {
                model: Product,
                attributes: ['id', 'name', 'unit']
              }
            ]
          }
        ]
      }
    ],
    order: [['createdAt', 'DESC']]
  });

  res.json({
    success: true,
    data: deliveries
  });
});

// @desc    Update delivery status
// @route   PUT /api/deliveries/:id/status
// @access  Private (Delivery Agent)
const updateDeliveryStatus = asyncHandler(async (req, res) => {
  const { status } = req.body;
  const deliveryId = req.params.id;

  const delivery = await Delivery.findOne({
    where: {
      id: deliveryId,
      deliveryAgentId: req.user.id
    }
  });

  if (!delivery) {
    return res.status(404).json({
      success: false,
      message: 'Delivery not found or not assigned to you'
    });
  }

  // Update delivery status
  delivery.status = status;

  // Set actual delivery time if delivered
  if (status === 'DELIVERED') {
    delivery.actualDelivery = new Date();
  }

  await delivery.save();

  // Update order status based on delivery status
  const order = await Order.findByPk(delivery.orderId);
  if (order) {
    if (status === 'DELIVERED') {
      order.status = 'DELIVERED';
    } else if (status === 'IN_TRANSIT') {
      order.status = 'SHIPPED';
    }
    await order.save();
  }

  res.json({
    success: true,
    data: delivery,
    message: 'Delivery status updated successfully'
  });
});

// @desc    Get delivery tracking info
// @route   GET /api/deliveries/tracking/:trackingNumber
// @access  Public
const getDeliveryTracking = asyncHandler(async (req, res) => {
  const { trackingNumber } = req.params;

  const delivery = await Delivery.findOne({
    where: { trackingNumber },
    include: [
      {
        model: Order,
        include: [
          {
            model: OrderItem,
            include: [
              {
                model: Product,
                attributes: ['id', 'name', 'unit']
              }
            ]
          }
        ]
      }
    ]
  });

  if (!delivery) {
    return res.status(404).json({
      success: false,
      message: 'Tracking number not found'
    });
  }

  res.json({
    success: true,
    data: delivery
  });
});

// Define routes
router.get('/agent', protect, getAgentDeliveries);
router.put('/:id/status', protect, updateDeliveryStatus);
router.get('/tracking/:trackingNumber', getDeliveryTracking);

module.exports = router;