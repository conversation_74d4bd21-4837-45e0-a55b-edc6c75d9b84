
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Filter, MapPin, Star, TrendingUp, Leaf, Heart, ShoppingCart } from "lucide-react";
import ProductCard from "./ProductCard";

const ProductCatalog = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [sortBy, setSortBy] = useState("popular");

  const allProducts = [
    // Cocoa Products
    {
      name: "Premium Cocoa Beans",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=300&fit=crop",
      price: "2,500 FCFA/kg",
      region: "Southwest Region",
      farmer: "Paul Mbe",
      rating: 4.8,
      category: "Cocoa",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-10"
    },
    {
      name: "Raw Cocoa Powder",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop",
      price: "3,800 FCFA/kg",
      region: "Southwest Region",
      farmer: "Grace Ewane",
      rating: 4.9,
      category: "Cocoa",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-15"
    },
    {
      name: "Cocoa Butter",
      image: "https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=300&fit=crop",
      price: "4,200 FCFA/kg",
      region: "Southwest Region",
      farmer: "Samuel Tabi",
      rating: 4.7,
      category: "Cocoa",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-08"
    },
    
    // Coffee Products
    {
      name: "Arabica Coffee Beans",
      image: "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop",
      price: "3,200 FCFA/kg",
      region: "Northwest Region",
      farmer: "Marie Fon",
      rating: 4.9,
      category: "Coffee",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-12"
    },
    {
      name: "Robusta Coffee",
      image: "https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=300&fit=crop",
      price: "2,800 FCFA/kg",
      region: "Northwest Region",
      farmer: "Joseph Ngwa",
      rating: 4.6,
      category: "Coffee",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-14"
    },
    {
      name: "Ground Coffee Powder",
      image: "https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop",
      price: "3,500 FCFA/kg",
      region: "Northwest Region",
      farmer: "Alice Bih",
      rating: 4.8,
      category: "Coffee",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-11"
    },

    // Fruits
    {
      name: "Fresh Plantains",
      image: "https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=400&h=300&fit=crop",
      price: "800 FCFA/bunch",
      region: "Littoral Region",
      farmer: "Jean Douala",
      rating: 4.6,
      category: "Fruits",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-18"
    },
    {
      name: "Sweet Bananas",
      image: "https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=300&fit=crop",
      price: "600 FCFA/bunch",
      region: "Littoral Region",
      farmer: "Marie Sone",
      rating: 4.5,
      category: "Fruits",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-19"
    },
    {
      name: "Fresh Pineapples",
      image: "https://images.unsplash.com/photo-1589820296156-2454bb8a6ad1?w=400&h=300&fit=crop",
      price: "1,200 FCFA/piece",
      region: "Centre Region",
      farmer: "Peter Ngoh",
      rating: 4.7,
      category: "Fruits",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-16"
    },
    {
      name: "Mangoes",
      image: "https://images.unsplash.com/photo-1605027990121-cbae9ef11c4d?w=400&h=300&fit=crop",
      price: "400 FCFA/piece",
      region: "North Region",
      farmer: "Hadja Aisha",
      rating: 4.4,
      category: "Fruits",
      inStock: false,
      isOrganic: true,
      harvestDate: "2024-01-05"
    },
    {
      name: "Avocados",
      image: "https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400&h=300&fit=crop",
      price: "300 FCFA/piece",
      region: "Centre Region",
      farmer: "Rose Mballa",
      rating: 4.6,
      category: "Fruits",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-17"
    },

    // Vegetables
    {
      name: "Fresh Tomatoes",
      image: "https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400&h=300&fit=crop",
      price: "1,500 FCFA/kg",
      region: "West Region",
      farmer: "Emmanuel Kamga",
      rating: 4.5,
      category: "Vegetables",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-18"
    },
    {
      name: "Green Pepper",
      image: "https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=300&fit=crop",
      price: "2,000 FCFA/kg",
      region: "West Region",
      farmer: "Nana Fossung",
      rating: 4.3,
      category: "Vegetables",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-19"
    },
    {
      name: "Fresh Carrots",
      image: "https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400&h=300&fit=crop",
      price: "1,800 FCFA/kg",
      region: "Northwest Region",
      farmer: "Daniel Che",
      rating: 4.7,
      category: "Vegetables",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-17"
    },
    {
      name: "Green Beans",
      image: "https://images.unsplash.com/photo-1506976785307-8732e854ad03?w=400&h=300&fit=crop",
      price: "2,200 FCFA/kg",
      region: "West Region",
      farmer: "Grace Tchinda",
      rating: 4.6,
      category: "Vegetables",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-16"
    },

    // Tubers & Roots
    {
      name: "Cassava Roots",
      image: "https://images.unsplash.com/photo-1518843875459-f738682238a6?w=400&h=300&fit=crop",
      price: "500 FCFA/kg",
      region: "East Region",
      farmer: "Grace Ngozi",
      rating: 4.5,
      category: "Tubers",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-10"
    },
    {
      name: "Sweet Potatoes",
      image: "https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop",
      price: "800 FCFA/kg",
      region: "Centre Region",
      farmer: "John Mbarga",
      rating: 4.4,
      category: "Tubers",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-13"
    },
    {
      name: "Irish Potatoes",
      image: "https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop",
      price: "1,200 FCFA/kg",
      region: "Northwest Region",
      farmer: "Martha Ndikum",
      rating: 4.8,
      category: "Tubers",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-15"
    },
    {
      name: "White Yams",
      image: "https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop",
      price: "900 FCFA/kg",
      region: "East Region",
      farmer: "Paul Atangana",
      rating: 4.6,
      category: "Tubers",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-12"
    },

    // Grains & Cereals
    {
      name: "Maize (Corn)",
      image: "https://images.unsplash.com/photo-1586201375761-83865001e8c3?w=400&h=300&fit=crop",
      price: "800 FCFA/kg",
      region: "Adamawa Region",
      farmer: "Ardo Hassan",
      rating: 4.5,
      category: "Grains",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-08"
    },
    {
      name: "Rice",
      image: "https://images.unsplash.com/photo-1586201375761-83865001e8c3?w=400&h=300&fit=crop",
      price: "1,500 FCFA/kg",
      region: "Far North Region",
      farmer: "Malam Issa",
      rating: 4.7,
      category: "Grains",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-06"
    },
    {
      name: "Millet",
      image: "https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=300&fit=crop",
      price: "1,200 FCFA/kg",
      region: "Far North Region",
      farmer: "Aichatou Bello",
      rating: 4.4,
      category: "Grains",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-09"
    },

    // Nuts & Seeds
    {
      name: "Ground Nuts",
      image: "https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=300&fit=crop",
      price: "1,200 FCFA/kg",
      region: "Far North Region",
      farmer: "Fatima Hassan",
      rating: 4.4,
      category: "Nuts",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-11"
    },
    {
      name: "Sesame Seeds",
      image: "https://images.unsplash.com/photo-1607672632458-9eb56696346b?w=400&h=300&fit=crop",
      price: "2,800 FCFA/kg",
      region: "North Region",
      farmer: "Mairamou Bouba",
      rating: 4.6,
      category: "Nuts",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-07"
    },

    // Oils
    {
      name: "Palm Oil",
      image: "https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400&h=300&fit=crop",
      price: "1,800 FCFA/L",
      region: "Southwest Region",
      farmer: "Samuel Tabi",
      rating: 4.7,
      category: "Oil",
      inStock: true,
      isOrganic: false,
      harvestDate: "2024-01-14"
    },
    {
      name: "Groundnut Oil",
      image: "https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400&h=300&fit=crop",
      price: "2,500 FCFA/L",
      region: "Far North Region",
      farmer: "Adama Sali",
      rating: 4.5,
      category: "Oil",
      inStock: true,
      isOrganic: true,
      harvestDate: "2024-01-13"
    }
  ];

  // Filter and sort products
  const filteredProducts = allProducts
    .filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          product.farmer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          product.region.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesRegion = selectedRegion === "all" || product.region === selectedRegion;
      const matchesCategory = selectedCategory === "all" || product.category === selectedCategory;
      
      return matchesSearch && matchesRegion && matchesCategory;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "price-low": return parseFloat(a.price.replace(/[^\d]/g, '')) - parseFloat(b.price.replace(/[^\d]/g, ''));
        case "price-high": return parseFloat(b.price.replace(/[^\d]/g, '')) - parseFloat(a.price.replace(/[^\d]/g, ''));
        case "rating": return b.rating - a.rating;
        case "newest": return new Date(b.harvestDate).getTime() - new Date(a.harvestDate).getTime();
        default: return b.rating - a.rating; // popular
      }
    });

  const regions = [...new Set(allProducts.map(p => p.region))];
  const categories = [...new Set(allProducts.map(p => p.category))];

  return (
    <div className="space-y-6">
      {/* Advanced Search and Filter Bar */}
      <Card className="enhanced-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5 text-primary" />
            Product Discovery & Filtering
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search products, farmers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedRegion} onValueChange={setSelectedRegion}>
              <SelectTrigger>
                <SelectValue placeholder="Select Region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                {regions.map(region => (
                  <SelectItem key={region} value={region}>{region}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort By" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="popular">Most Popular</SelectItem>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="newest">Newest Harvest</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
              <Leaf className="w-3 h-3 mr-1" />
              Organic Only
            </Badge>
            <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
              <TrendingUp className="w-3 h-3 mr-1" />
              Best Sellers
            </Badge>
            <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
              <Star className="w-3 h-3 mr-1" />
              Top Rated
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Product Categories Tabs */}
      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-9 bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg">
          <TabsTrigger value="all" className="rounded-xl">All</TabsTrigger>
          <TabsTrigger value="Cocoa" className="rounded-xl">Cocoa</TabsTrigger>
          <TabsTrigger value="Coffee" className="rounded-xl">Coffee</TabsTrigger>
          <TabsTrigger value="Fruits" className="rounded-xl">Fruits</TabsTrigger>
          <TabsTrigger value="Vegetables" className="rounded-xl">Vegetables</TabsTrigger>
          <TabsTrigger value="Tubers" className="rounded-xl">Tubers</TabsTrigger>
          <TabsTrigger value="Grains" className="rounded-xl">Grains</TabsTrigger>
          <TabsTrigger value="Nuts" className="rounded-xl">Nuts</TabsTrigger>
          <TabsTrigger value="Oil" className="rounded-xl">Oils</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-2xl font-bold">All Products ({filteredProducts.length})</h3>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Heart className="w-4 h-4 mr-2" />
                Wishlist
              </Button>
              <Button variant="outline" size="sm">
                <ShoppingCart className="w-4 h-4 mr-2" />
                Compare
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProducts.map((product, index) => (
              <ProductCard key={index} {...product} />
            ))}
          </div>
        </TabsContent>

        {categories.map(category => (
          <TabsContent key={category} value={category} className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-2xl font-bold">{category} Products ({filteredProducts.filter(p => p.category === category).length})</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredProducts.filter(p => p.category === category).map((product, index) => (
                <ProductCard key={index} {...product} />
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>

      {filteredProducts.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <Search className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-xl font-semibold mb-2">No products found</h3>
            <p className="text-muted-foreground">Try adjusting your search criteria or filters</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProductCatalog;
