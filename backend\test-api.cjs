// Simple API testing script
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testRegistration() {
  try {
    console.log('🧪 Testing User Registration...');
    
    const response = await axios.post(`${BASE_URL}/auth/register`, {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    console.log('✅ Registration successful:', response.data);
    return response.data.data.user;
  } catch (error) {
    console.log('❌ Registration failed:', error.response?.data || error.message);
    return null;
  }
}

async function testLogin() {
  try {
    console.log('\n🧪 Testing User Login...');
    
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Test123!'
    });
    
    console.log('✅ Login successful:', response.data);
    return response.data.data.accessToken;
  } catch (error) {
    console.log('❌ Login failed:', error.response?.data || error.message);
    return null;
  }
}

async function testFarmerRegistration() {
  try {
    console.log('\n🧪 Testing Farmer Registration...');
    
    const response = await axios.post(`${BASE_URL}/auth/register`, {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      password: 'Test123!',
      role: 'FARMER',
      farmName: 'Green Valley Farm',
      farmLocation: 'Douala, Littoral'
    });
    
    console.log('✅ Farmer registration successful:', response.data);
    return response.data.data.user;
  } catch (error) {
    console.log('❌ Farmer registration failed:', error.response?.data || error.message);
    return null;
  }
}

async function testProducts() {
  try {
    console.log('\n🧪 Testing Products API...');
    
    const response = await axios.get(`${BASE_URL}/products`);
    console.log('✅ Products loaded:', response.data.data.products.length, 'products found');
    return response.data.data.products;
  } catch (error) {
    console.log('❌ Products failed:', error.response?.data || error.message);
    return null;
  }
}

async function testCart(token, productId) {
  try {
    console.log('\n🧪 Testing Add to Cart...');
    
    const response = await axios.post(`${BASE_URL}/cart/add`, {
      productId: productId,
      quantity: 2
    }, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Add to cart successful:', response.data);
    return response.data;
  } catch (error) {
    console.log('❌ Add to cart failed:', error.response?.data || error.message);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting API Tests...\n');
  
  // Test registration
  const user = await testRegistration();
  
  // Test login
  const token = await testLogin();
  
  // Test farmer registration
  const farmer = await testFarmerRegistration();
  
  // Test products
  const products = await testProducts();
  
  // Test add to cart (if we have token and products)
  if (token && products && products.length > 0) {
    await testCart(token, products[0].id);
  }
  
  console.log('\n🎉 API Tests Complete!');
}

runTests().catch(console.error);
