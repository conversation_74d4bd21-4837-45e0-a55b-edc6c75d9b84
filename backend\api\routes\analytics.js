const express = require('express');
const analyticsController = require('../controllers/analyticsController');
const {authenticateToken, requireRole} = require('../middleware/auth');

const router = express.Router();

router.get('/sales', authenticateToken, requireRole(['vendor', 'admin']), analyticsController.getSalesAnalytics);
router.get('/products', authenticateToken, requireRole(['vendor', 'admin']), analyticsController.getProductAnalytics);
router.get('/customers', authenticateToken, requireRole(['vendor', 'admin']), analyticsController.getCustomerAnalytics);
router.get('/trends', authenticateToken, requireRole(['vendor', 'admin']), analyticsController.getMarketTrends);
router.get('/performance', authenticateToken, requireRole(['vendor', 'admin']), analyticsController.getPerformanceMetrics);

module.exports = router;