
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Phone, MessageSquare, Calendar, Award } from "lucide-react";

interface Professional {
  name: string;
  title: string;
  specialization: string;
  rating: number;
  experience: string;
  avatar: string;
  availability: "available" | "busy" | "offline";
  consultationFee: string;
  languages: string[];
}

interface ProfessionalServicesProps {
  professionals: Professional[];
}

const ProfessionalServices = ({ professionals }: ProfessionalServicesProps) => {
  const getAvailabilityColor = (status: string) => {
    switch (status) {
      case "available": return "bg-green-100 text-green-700";
      case "busy": return "bg-yellow-100 text-yellow-700";
      case "offline": return "bg-gray-100 text-gray-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        key={i} 
        className={`w-4 h-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} 
      />
    ));
  };

  return (
    <Card className="enhanced-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="w-5 h-5 text-blue-600" />
          Professional Advisory Services
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Get expert advice from certified professionals
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {professionals.map((professional, index) => (
          <div key={index} className="service-card">
            <div className="flex items-start gap-4">
              <Avatar className="w-16 h-16">
                <AvatarImage src={professional.avatar} alt={professional.name} />
                <AvatarFallback>{professional.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold text-green-800">{professional.name}</h4>
                  <Badge className={getAvailabilityColor(professional.availability)}>
                    {professional.availability}
                  </Badge>
                </div>
                
                <p className="text-sm font-medium text-green-700 mb-1">
                  {professional.title}
                </p>
                <p className="text-sm text-muted-foreground mb-2">
                  {professional.specialization}
                </p>
                
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex">{renderStars(professional.rating)}</div>
                  <span className="text-sm text-muted-foreground">
                    ({professional.rating}/5)
                  </span>
                </div>
                
                <p className="text-xs text-muted-foreground mb-3">
                  {professional.experience}
                </p>
                
                <div className="flex flex-wrap gap-1 mb-3">
                  {professional.languages.map((lang, langIndex) => (
                    <Badge key={langIndex} variant="outline" className="text-xs">
                      {lang}
                    </Badge>
                  ))}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-semibold text-green-700">
                    {professional.consultationFee}
                  </span>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="h-8">
                      <Phone className="w-3 h-3 mr-1" />
                      Call
                    </Button>
                    <Button size="sm" variant="outline" className="h-8">
                      <MessageSquare className="w-3 h-3 mr-1" />
                      Chat
                    </Button>
                    <Button size="sm" className="h-8">
                      <Calendar className="w-3 h-3 mr-1" />
                      Book
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        <div className="border-t pt-4">
          <h4 className="font-semibold mb-3">Service Categories</h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-blue-50 rounded-lg text-center">
              <Award className="w-5 h-5 mx-auto mb-1 text-blue-600" />
              <div className="text-sm font-medium">Nutritionists</div>
              <div className="text-xs text-muted-foreground">15 Available</div>
            </div>
            <div className="p-3 bg-green-50 rounded-lg text-center">
              <Award className="w-5 h-5 mx-auto mb-1 text-green-600" />
              <div className="text-sm font-medium">Agronomists</div>
              <div className="text-xs text-muted-foreground">12 Available</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfessionalServices;
