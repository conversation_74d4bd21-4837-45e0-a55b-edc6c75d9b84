// Import necessary modules
const { body } = require('express-validator');
const validate = require('../middleware/validate');

// Validation rules for creating/updating a review
exports.validateReview = [
  body('productId').isInt({ min: 1 }).withMessage('Valid product ID is required'),
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('comment').optional().trim().isLength({ min: 3 }).withMessage('Comment must be at least 3 characters long'),
  validate,
];