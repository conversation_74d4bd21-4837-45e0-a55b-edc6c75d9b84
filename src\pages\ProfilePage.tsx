import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import {
  User,
  Mail,
  MapPin,
  Calendar,
  Edit,
  Save,
  X,
  Shield,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';
import authService from '@/services/authService';

const ProfilePage: React.FC = () => {
  const [user, setUser] = useState(authService.getCurrentUser());
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    location: ''
  });
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Fetch user profile
  const { data: profileData, isLoading } = useQuery({
    queryKey: ['profile'],
    queryFn: async () => {
      const response = await authService.authenticatedFetch('http://localhost:3001/api/auth/profile');
      return response.json();
    },
    enabled: !!user,
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await authService.authenticatedFetch('http://localhost:3001/api/auth/profile', {
        method: 'PUT',
        body: JSON.stringify(data),
      });
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success('Profile updated successfully');
        setIsEditing(false);
        queryClient.invalidateQueries({ queryKey: ['profile'] });
      } else {
        toast.error(data.message || 'Failed to update profile');
      }
    },
    onError: () => {
      toast.error('Failed to update profile');
    }
  });

  useEffect(() => {
    if (profileData?.success) {
      const userData = profileData.data.user;
      setFormData({
        username: userData.username || '',
        email: userData.email || '',
        location: userData.location || ''
      });
    }
  }, [profileData]);

  // Redirect if not logged in
  if (!user) {
    navigate('/login');
    return null;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = () => {
    updateProfileMutation.mutate({
      username: formData.username,
      location: formData.location
    });
  };

  const handleCancel = () => {
    if (profileData?.success) {
      const userData = profileData.data.user;
      setFormData({
        username: userData.username || '',
        email: userData.email || '',
        location: userData.location || ''
      });
    }
    setIsEditing(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading profile...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const userData = profileData?.data?.user;

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-green-900 py-12 px-4">
      <Navbar />
      
      <div className="max-w-3xl mx-auto bg-gray-900 rounded-2xl shadow-2xl p-10">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-green-400">My Profile</h1>
          <p className="text-gray-300 mt-1">Manage your account information</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-2">
            <Card className="bg-gray-800 border-0 shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center">
                  <User className="w-5 h-5 mr-2 text-green-400" />
                  Profile Information
                </CardTitle>
                {!isEditing ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="text-green-400 border-green-400 hover:bg-green-400 hover:text-white"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancel}
                      className="text-gray-300 border-gray-300 hover:bg-gray-300 hover:text-gray-900"
                    >
                      <X className="w-4 h-4 mr-2" />
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSave}
                      disabled={updateProfileMutation.isPending}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      Save
                    </Button>
                  </div>
                )}
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="username" className="text-gray-300">Username</Label>
                    {isEditing ? (
                      <Input
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        className="mt-1 bg-gray-700 text-gray-100"
                      />
                    ) : (
                      <p className="mt-1 text-gray-100 font-medium">{userData?.username}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="text-gray-300">Email</Label>
                    <div className="mt-1 flex items-center">
                      <Mail className="w-4 h-4 text-gray-400 mr-2" />
                      <p className="text-gray-100 font-medium">{userData?.email}</p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
                  </div>
                  
                  <div>
                    <Label htmlFor="location" className="text-gray-300">Location</Label>
                    {isEditing ? (
                      <Input
                        id="location"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        placeholder="Enter your location"
                        className="mt-1 bg-gray-700 text-gray-100"
                      />
                    ) : (
                      <div className="mt-1 flex items-center">
                        <MapPin className="w-4 h-4 text-gray-400 mr-2" />
                        <p className="text-gray-100 font-medium">
                          {userData?.location || 'Not specified'}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-gray-300">Member Since</Label>
                    <div className="mt-1 flex items-center">
                      <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                      <p className="text-gray-100 font-medium">
                        {userData?.createdAt ? new Date(userData.createdAt).toLocaleDateString() : 'Unknown'}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Account Actions */}
          <div className="space-y-6">
            <Card className="bg-gray-800 border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-300">
                  <Settings className="w-5 h-5 mr-2" />
                  Account Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  variant="outline"
                  className="w-full justify-start text-gray-300 border-gray-300 hover:bg-gray-300 hover:text-gray-900"
                  onClick={() => navigate('/change-password')}
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Change Password
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full justify-start text-gray-300 border-gray-300 hover:bg-gray-300 hover:text-gray-900"
                  onClick={() => navigate('/account-settings')}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Account Settings
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-gray-300">Account Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-400">Account Type</span>
                  <Badge variant="secondary" className="bg-green-600 text-white">
                    {userData?.role === 'user' ? 'Customer' : userData?.role}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Status</span>
                  <Badge className="bg-green-100 text-green-800">Active</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default ProfilePage;
