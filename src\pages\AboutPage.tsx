import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import {
  Leaf,
  Users,
  Heart,
  Target,
  Award,
  MapPin,
  Phone,
  Mail,
  Globe,
} from 'lucide-react';

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-green-600 to-emerald-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            About Agri Connect
          </h1>
          <p className="text-xl text-green-100 max-w-3xl mx-auto leading-relaxed">
            Connecting Cameroon's farmers directly with customers, promoting sustainable agriculture
            and supporting local communities across all 10 regions of our beautiful nation.
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-8 mb-20">
          <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-shadow duration-300">
            <CardHeader className="pb-6">
              <div className="flex items-center space-x-4">
                <div className="p-4 bg-green-100 rounded-xl">
                  <Target className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">Our Mission</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 leading-relaxed text-lg">
                To revolutionize agriculture in Cameroon by creating a direct connection between farmers
                and consumers, ensuring fair prices for farmers while providing fresh, quality produce
                to customers across all 10 regions of our country.
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-shadow duration-300">
            <CardHeader className="pb-6">
              <div className="flex items-center space-x-4">
                <div className="p-4 bg-blue-100 rounded-xl">
                  <Globe className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">Our Vision</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 leading-relaxed text-lg">
                To become Cameroon's leading agricultural marketplace, empowering farmers with technology
                and connecting communities through sustainable, locally-sourced food systems that benefit everyone.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do at Agri Connect
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <CardContent className="p-10">
                <div className="w-20 h-20 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Leaf className="h-10 w-10 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900">Sustainability</h3>
                <p className="text-gray-600 leading-relaxed">
                  Promoting eco-friendly farming practices and sustainable agriculture for future generations
                  while protecting Cameroon's natural resources.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <CardContent className="p-10">
                <div className="w-20 h-20 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Users className="h-10 w-10 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900">Community</h3>
                <p className="text-gray-600 leading-relaxed">
                  Building strong relationships between farmers and customers, fostering local community growth
                  and economic development across Cameroon.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <CardContent className="p-10">
                <div className="w-20 h-20 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Heart className="h-10 w-10 text-purple-600" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900">Quality</h3>
                <p className="text-gray-600 leading-relaxed">
                  Ensuring the highest quality fresh produce reaches customers while maintaining fair
                  compensation and support for our hardworking farmers.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Contact CTA */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-700 rounded-3xl text-white p-12 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Join Our Community?</h2>
          <p className="text-xl mb-10 text-green-100 max-w-2xl mx-auto">
            Whether you're a farmer looking to sell your produce or a customer seeking fresh, local products,
            we're here to connect you with Cameroon's agricultural community.
          </p>

          <div className="grid md:grid-cols-3 gap-8 mb-10">
            <div className="flex flex-col items-center space-y-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <MapPin className="h-6 w-6" />
              </div>
              <div>
                <p className="font-semibold">Location</p>
                <p className="text-green-100">Douala, Littoral Region, Cameroon</p>
              </div>
            </div>
            <div className="flex flex-col items-center space-y-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <Phone className="h-6 w-6" />
              </div>
              <div>
                <p className="font-semibold">Phone</p>
                <p className="text-green-100">+237 6XX XXX XXX</p>
              </div>
            </div>
            <div className="flex flex-col items-center space-y-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <Mail className="h-6 w-6" />
              </div>
              <div>
                <p className="font-semibold">Email</p>
                <p className="text-green-100"><EMAIL></p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100 font-semibold px-8 py-3" asChild>
              <Link to="/register">
                Join as Farmer
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 font-semibold px-8 py-3" asChild>
              <Link to="/products">
                Shop Products
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AboutPage;
