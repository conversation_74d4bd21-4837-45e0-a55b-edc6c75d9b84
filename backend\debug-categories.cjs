// Debug categories for farmer product creation
const mysql = require('mysql2/promise');
const axios = require('axios');

async function debugCategories() {
  console.log('🔍 Debugging Categories for Farmer Product Creation...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    // Check categories in database
    console.log('📁 Checking Categories in Database:');
    const [categories] = await connection.execute(`
      SELECT id, name, description, isActive 
      FROM categories 
      WHERE isActive = 1
      ORDER BY name 
      LIMIT 10
    `);
    
    if (categories.length > 0) {
      console.log(`✅ Found ${categories.length} active categories in database:`);
      categories.forEach((category, index) => {
        console.log(`   ${index + 1}. ${category.name}`);
        console.log(`      ID: ${category.id}`);
        console.log(`      Active: ${category.isActive ? 'Yes' : 'No'}`);
        console.log('');
      });
    } else {
      console.log('❌ No active categories found in database');
    }
    
    // Test categories API endpoint
    console.log('🌐 Testing Categories API:');
    
    try {
      const apiResponse = await axios.get('http://localhost:3001/api/categories');
      
      console.log('✅ Categories API working');
      console.log('   Response structure:', Object.keys(apiResponse.data));
      
      if (apiResponse.data.success) {
        const apiCategories = apiResponse.data.data || apiResponse.data.categories || [];
        console.log(`   API returned ${apiCategories.length} categories`);
        
        if (apiCategories.length > 0) {
          console.log('   Sample categories from API:');
          apiCategories.slice(0, 5).forEach((cat, index) => {
            console.log(`     ${index + 1}. ${cat.name} (ID: ${cat.id})`);
          });
        }
      } else {
        console.log('   API response not successful:', apiResponse.data);
      }
      
    } catch (apiError) {
      console.log('❌ Categories API failed:');
      console.log('   Status:', apiError.response?.status);
      console.log('   Error:', apiError.response?.data?.message || apiError.message);
    }
    
    // Test with farmer authentication
    console.log('\n👨‍🌾 Testing with Farmer Authentication:');
    
    try {
      // Create a test farmer
      const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
        firstName: 'Test',
        lastName: 'Farmer',
        email: `farmer.test.${Date.now()}@example.com`,
        password: 'Test123!',
        role: 'FARMER'
      });
      
      const token = registerResponse.data.data.accessToken;
      console.log('✅ Test farmer created and logged in');
      
      // Test categories API with farmer token
      try {
        const farmerCategoriesResponse = await axios.get('http://localhost:3001/api/categories', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        console.log('✅ Categories API working with farmer auth');
        console.log('   Categories available to farmer:', farmerCategoriesResponse.data.success ? 
          (farmerCategoriesResponse.data.data?.length || 0) : 'Unknown');
        
      } catch (farmerApiError) {
        console.log('❌ Categories API failed with farmer auth:');
        console.log('   Status:', farmerApiError.response?.status);
        console.log('   Error:', farmerApiError.response?.data?.message || farmerApiError.message);
      }
      
    } catch (authError) {
      console.log('❌ Farmer authentication failed:', authError.response?.data || authError.message);
    }
    
    await connection.end();
    
    console.log('\n📝 Summary:');
    console.log('1. Check if categories exist in database ✓');
    console.log('2. Test categories API endpoint ✓');
    console.log('3. Test with farmer authentication ✓');
    console.log('\nNext: Check frontend category fetching in farmer product form');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugCategories();
