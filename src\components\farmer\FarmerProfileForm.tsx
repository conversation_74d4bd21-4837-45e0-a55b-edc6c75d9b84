import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  MapPin, 
  Phone, 
  Mail, 
  Camera, 
  Save, 
  Edit,
  Award,
  Truck,
  Shield
} from 'lucide-react';
import { toast } from 'sonner';
import authService from '@/services/authService';

interface FarmerProfileFormProps {
  onClose: () => void;
  onSuccess?: () => void;
}

const FarmerProfileForm: React.FC<FarmerProfileFormProps> = ({ onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState(authService.getCurrentUser());
  
  // Personal Information
  const [personalInfo, setPersonalInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    avatar: ''
  });

  // Farm Information
  const [farmInfo, setFarmInfo] = useState({
    farmName: '',
    farmDescription: '',
    farmSize: '',
    farmLocation: '',
    specializations: [] as string[],
    certifications: [] as string[],
    establishedYear: '',
    farmType: 'INDIVIDUAL' // INDIVIDUAL, COOPERATIVE, COMPANY
  });

  // Contact & Delivery
  const [contactInfo, setContactInfo] = useState({
    businessPhone: '',
    whatsappNumber: '',
    deliveryRadius: '',
    deliveryFee: '',
    acceptsPickup: true,
    operatingHours: {
      monday: { open: '08:00', close: '18:00', closed: false },
      tuesday: { open: '08:00', close: '18:00', closed: false },
      wednesday: { open: '08:00', close: '18:00', closed: false },
      thursday: { open: '08:00', close: '18:00', closed: false },
      friday: { open: '08:00', close: '18:00', closed: false },
      saturday: { open: '08:00', close: '16:00', closed: false },
      sunday: { open: '10:00', close: '14:00', closed: true }
    }
  });

  useEffect(() => {
    if (user) {
      // Load existing data
      setPersonalInfo({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phone: user.phone || '',
        avatar: user.avatar || ''
      });

      if (user.farmerProfile) {
        setFarmInfo({
          farmName: user.farmerProfile.farmName || '',
          farmDescription: user.farmerProfile.farmDescription || '',
          farmSize: user.farmerProfile.farmSize || '',
          farmLocation: user.farmerProfile.farmLocation || '',
          specializations: user.farmerProfile.specializations || [],
          certifications: user.farmerProfile.certifications || [],
          establishedYear: user.farmerProfile.establishedYear || '',
          farmType: user.farmerProfile.farmType || 'INDIVIDUAL'
        });
      }
    }
  }, [user]);

  const handlePersonalInfoSubmit = async () => {
    setLoading(true);
    try {
      const response = await authService.authenticatedFetch('http://localhost:3001/api/users/profile', {
        method: 'PUT',
        body: JSON.stringify(personalInfo),
      });

      const result = await response.json();
      if (result.success) {
        toast.success('Personal information updated successfully!');
        // Update local user data
        const updatedUser = { ...user, ...personalInfo };
        localStorage.setItem('user', JSON.stringify(updatedUser));
        setUser(updatedUser);
        onSuccess?.();
      } else {
        toast.error(result.message || 'Failed to update personal information');
      }
    } catch (error) {
      toast.error('Failed to update personal information');
      console.error('Error updating personal info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFarmInfoSubmit = async () => {
    setLoading(true);
    try {
      const response = await authService.authenticatedFetch('http://localhost:3001/api/farmers/profile', {
        method: 'PUT',
        body: JSON.stringify(farmInfo),
      });

      const result = await response.json();
      if (result.success) {
        toast.success('Farm information updated successfully!');
        onSuccess?.();
      } else {
        toast.error(result.message || 'Failed to update farm information');
      }
    } catch (error) {
      toast.error('Failed to update farm information');
      console.error('Error updating farm info:', error);
    } finally {
      setLoading(false);
    }
  };

  const addSpecialization = (spec: string) => {
    if (spec && !farmInfo.specializations.includes(spec)) {
      setFarmInfo(prev => ({
        ...prev,
        specializations: [...prev.specializations, spec]
      }));
    }
  };

  const removeSpecialization = (spec: string) => {
    setFarmInfo(prev => ({
      ...prev,
      specializations: prev.specializations.filter(s => s !== spec)
    }));
  };

  const addCertification = (cert: string) => {
    if (cert && !farmInfo.certifications.includes(cert)) {
      setFarmInfo(prev => ({
        ...prev,
        certifications: [...prev.certifications, cert]
      }));
    }
  };

  const removeCertification = (cert: string) => {
    setFarmInfo(prev => ({
      ...prev,
      certifications: prev.certifications.filter(c => c !== cert)
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Farmer Profile Management
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="personal" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="personal">Personal Info</TabsTrigger>
              <TabsTrigger value="farm">Farm Details</TabsTrigger>
              <TabsTrigger value="contact">Contact & Delivery</TabsTrigger>
            </TabsList>

            {/* Personal Information Tab */}
            <TabsContent value="personal" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={personalInfo.firstName}
                    onChange={(e) => setPersonalInfo(prev => ({ ...prev, firstName: e.target.value }))}
                    placeholder="Enter your first name"
                  />
                </div>

                <div>
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={personalInfo.lastName}
                    onChange={(e) => setPersonalInfo(prev => ({ ...prev, lastName: e.target.value }))}
                    placeholder="Enter your last name"
                  />
                </div>

                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={personalInfo.email}
                    onChange={(e) => setPersonalInfo(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="Enter your email"
                  />
                </div>

                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={personalInfo.phone}
                    onChange={(e) => setPersonalInfo(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="Enter your phone number"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handlePersonalInfoSubmit} disabled={loading}>
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Saving...' : 'Save Personal Info'}
                </Button>
              </div>
            </TabsContent>

            {/* Farm Details Tab */}
            <TabsContent value="farm" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="farmName">Farm Name *</Label>
                  <Input
                    id="farmName"
                    value={farmInfo.farmName}
                    onChange={(e) => setFarmInfo(prev => ({ ...prev, farmName: e.target.value }))}
                    placeholder="Enter your farm name"
                  />
                </div>

                <div>
                  <Label htmlFor="farmLocation">Farm Location *</Label>
                  <Input
                    id="farmLocation"
                    value={farmInfo.farmLocation}
                    onChange={(e) => setFarmInfo(prev => ({ ...prev, farmLocation: e.target.value }))}
                    placeholder="City, Region, Cameroon"
                  />
                </div>

                <div>
                  <Label htmlFor="farmSize">Farm Size (hectares)</Label>
                  <Input
                    id="farmSize"
                    type="number"
                    value={farmInfo.farmSize}
                    onChange={(e) => setFarmInfo(prev => ({ ...prev, farmSize: e.target.value }))}
                    placeholder="Enter farm size"
                  />
                </div>

                <div>
                  <Label htmlFor="establishedYear">Established Year</Label>
                  <Input
                    id="establishedYear"
                    type="number"
                    value={farmInfo.establishedYear}
                    onChange={(e) => setFarmInfo(prev => ({ ...prev, establishedYear: e.target.value }))}
                    placeholder="Year farm was established"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="farmDescription">Farm Description</Label>
                <Textarea
                  id="farmDescription"
                  value={farmInfo.farmDescription}
                  onChange={(e) => setFarmInfo(prev => ({ ...prev, farmDescription: e.target.value }))}
                  placeholder="Describe your farm, farming practices, and what makes it special..."
                  rows={4}
                />
              </div>

              <div className="flex justify-end">
                <Button onClick={handleFarmInfoSubmit} disabled={loading}>
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Saving...' : 'Save Farm Details'}
                </Button>
              </div>
            </TabsContent>

            {/* Contact & Delivery Tab */}
            <TabsContent value="contact" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="businessPhone">Business Phone</Label>
                  <Input
                    id="businessPhone"
                    value={contactInfo.businessPhone}
                    onChange={(e) => setContactInfo(prev => ({ ...prev, businessPhone: e.target.value }))}
                    placeholder="Business phone number"
                  />
                </div>

                <div>
                  <Label htmlFor="whatsappNumber">WhatsApp Number</Label>
                  <Input
                    id="whatsappNumber"
                    value={contactInfo.whatsappNumber}
                    onChange={(e) => setContactInfo(prev => ({ ...prev, whatsappNumber: e.target.value }))}
                    placeholder="WhatsApp number for quick contact"
                  />
                </div>

                <div>
                  <Label htmlFor="deliveryRadius">Delivery Radius (km)</Label>
                  <Input
                    id="deliveryRadius"
                    type="number"
                    value={contactInfo.deliveryRadius}
                    onChange={(e) => setContactInfo(prev => ({ ...prev, deliveryRadius: e.target.value }))}
                    placeholder="Maximum delivery distance"
                  />
                </div>

                <div>
                  <Label htmlFor="deliveryFee">Delivery Fee (FCFA)</Label>
                  <Input
                    id="deliveryFee"
                    type="number"
                    value={contactInfo.deliveryFee}
                    onChange={(e) => setContactInfo(prev => ({ ...prev, deliveryFee: e.target.value }))}
                    placeholder="Standard delivery fee"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={() => toast.success('Contact info saved!')} disabled={loading}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Contact Info
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default FarmerProfileForm;
