const { Category, Product, User, Farmer } = require('../data/models');

const addNewCategories = async () => {
  try {
    console.log('🌱 Adding new Cameroon agricultural categories...');

    // Add new categories
    const newCategories = [
      { name: 'Cereals & Grains', description: 'Maize, rice, millet, sorghum, wheat, fonio, teff' },
      { name: 'Root Crops & Tubers', description: 'Cassava, yam, sweet potato, Irish potato, cocoyam, plantain' },
      { name: 'Major Cash Crops', description: 'Cocoa, coffee, cotton, rubber, palm oil, sugarcane, tobacco, tea' },
      { name: 'Tropical Fruits', description: 'Banana, pineapple, mango, papaya, avocado, citrus fruits, guava' },
      { name: 'Indigenous/Wild Fruits', description: 'Bush mango, African cherry, velvet tamarind, African pear' },
      { name: 'Leafy Greens', description: 'Spinach, lettuce, cabbage, bitter leaf, water leaf, pumpkin leaves' },
      { name: 'Fruit Vegetables', description: 'Tomato, pepper, eggplant, okra, cucumber, zucchini, pumpkin' },
      { name: 'Other Vegetables', description: 'Onion, garlic, carrot, beetroot, green beans, celery' },
      { name: 'Legumes & Pulses', description: 'Groundnuts, cowpea, beans, soybeans, bambara groundnut' },
      { name: 'Spices & Seasonings', description: 'Ginger, turmeric, pepper, njangsa seeds, eru seeds' },
      { name: 'Herbs & Aromatics', description: 'Basil, lemongrass, mint, rosemary, thyme, oregano' },
      { name: 'Tree Crops & Nuts', description: 'Oil palm, coconut, kola nut, African walnut, cashew, shea nut' },
      { name: 'Livestock - Ruminants', description: 'Cattle, goat, sheep, buffalo' },
      { name: 'Livestock - Monogastrics', description: 'Pig, poultry, duck, guinea fowl, rabbit, grasscutter' },
      { name: 'Aquaculture & Fisheries', description: 'Tilapia, catfish, carp, marine fish, shrimp, oysters' },
      { name: 'Medicinal & Aromatic Plants', description: 'Moringa, aloe vera, neem, artemisia, hibiscus' },
      { name: 'Fiber Crops', description: 'Cotton, sisal, jute, kenaf, bamboo, raffia palm' },
      { name: 'Beverage Crops', description: 'Coffee, tea, cocoa, kola nut, hibiscus, ginger drinks' },
      { name: 'Oil Crops', description: 'Oil palm, groundnut, soybean, sesame, sunflower, castor bean' },
      { name: 'Mushrooms & Fungi', description: 'Oyster mushrooms, shiitake, wild edible mushrooms' },
      { name: 'Beekeeping Products', description: 'Honey, beeswax, propolis, royal jelly' },
      { name: 'Forestry Products', description: 'Rubber trees, teak, eucalyptus, fruit tree timber' },
      { name: 'Flowers & Ornamentals', description: 'Cut flowers, ornamental plants, landscaping plants' }
    ];

    // Check which categories already exist
    const existingCategories = await Category.findAll();
    const existingNames = existingCategories.map(cat => cat.name);
    
    // Filter out categories that already exist
    const categoriesToAdd = newCategories.filter(cat => !existingNames.includes(cat.name));
    
    if (categoriesToAdd.length > 0) {
      const addedCategories = await Category.bulkCreate(categoriesToAdd);
      console.log(` Added ${addedCategories.length} new categories`);
    } else {
      console.log(' All categories already exist');
    }

    // Get all categories for product creation
    const allCategories = await Category.findAll();
    console.log(` Total categories in database: ${allCategories.length}`);

    // Find or create sample farmer
    let sampleUser = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!sampleUser) {
      sampleUser = await User.create({
        firstName: 'Sample',
        lastName: 'Farmer',
        email: '<EMAIL>',
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', // password123
        role: 'FARMER',
        status: 'ACTIVE',
        emailVerified: true
      });
    }

    let sampleFarmer = await Farmer.findOne({ where: { userId: sampleUser.id } });
    if (!sampleFarmer) {
      sampleFarmer = await Farmer.create({
        userId: sampleUser.id,
        farmName: 'Green Valley Farm',
        farmLocation: 'Douala, Cameroon',
        verified: true,
        rating: 4.5
      });
    }

    console.log(' Sample farmer ready');
    console.log(' Categories and farmer setup complete!');
    
    return { categories: allCategories, farmer: sampleFarmer };

  } catch (error) {
    console.error(' Error adding categories:', error);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  addNewCategories()
    .then(() => {
      console.log(' Categories addition completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error(' Failed to add categories:', error);
      process.exit(1);
    });
}

module.exports = { addNewCategories };
