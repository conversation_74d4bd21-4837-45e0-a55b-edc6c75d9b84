const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User } = require('../../data/models');
const config = require('../../config/config');

class AuthService {
  async register(userData) {
    const { email, password, ...otherData } = userData;
    const existingUser = await User.findOne({ where: { email } });
    
    if (existingUser) {
      throw new Error('User already exists');
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await User.create({ ...otherData, email, password: hashedPassword });
    
    return this.generateToken(user);
  }

  async login(email, password) {
    const user = await User.findOne({ where: { email } });
    
    if (!user) {
      throw new Error('User not found');
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      throw new Error('Invalid password');
    }

    return this.generateToken(user);
  }

  generateToken(user) {
    const token = jwt.sign(
      { id: user.id, email: user.email },
      config.jwtSecret,
      { expiresIn: '1d' }
    );
    return { user, token };
  }

  async verifyToken(token) {
    try {
      const decoded = jwt.verify(token, config.jwtSecret);
      const user = await User.findByPk(decoded.id);
      if (!user) {
        throw new Error('User not found');
      }
      return user;
    } catch (error) {
      throw new Error('Invalid token');
    }
  }
}

module.exports = new AuthService();