
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Phone, Users } from "lucide-react";

interface FarmerProfileProps {
  name: string;
  image: string;
  region: string;
  specialties: string[];
  experience: string;
  phone: string;
  verified: boolean;
}

const FarmerProfile = ({ name, image, region, specialties, experience, phone, verified }: FarmerProfileProps) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-300">
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className="relative">
            <img 
              src={image} 
              alt={name}
              className="w-16 h-16 rounded-full object-cover"
            />
            {verified && (
              <div className="absolute -bottom-1 -right-1 bg-green-500 text-white rounded-full p-1">
                <Users className="w-3 h-3" />
              </div>
            )}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold text-lg">{name}</h3>
              {verified && (
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                  Verified
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2 mb-2 text-muted-foreground">
              <MapPin className="w-4 h-4" />
              <span className="text-sm">{region}</span>
            </div>
            
            <div className="flex items-center gap-2 mb-3 text-muted-foreground">
              <Phone className="w-4 h-4" />
              <span className="text-sm">{phone}</span>
            </div>
            
            <p className="text-sm text-muted-foreground mb-3">{experience}</p>
            
            <div className="flex flex-wrap gap-2">
              {specialties.map((specialty, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {specialty}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default FarmerProfile;
