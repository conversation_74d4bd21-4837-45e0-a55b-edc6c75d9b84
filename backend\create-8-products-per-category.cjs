// Create 8 products for each of the 7 categories
const mysql = require('mysql2/promise');

async function create8ProductsPerCategory() {
  console.log('🌾 Creating 8 Products Per Category (56 Total)...\n');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to database');
    
    // Step 1: Clear existing products
    console.log('1. Clearing existing products...');
    await connection.execute('DELETE FROM products');
    console.log('   ✅ Cleared existing products');
    
    // Step 2: Get categories
    console.log('2. Getting categories...');
    const [categories] = await connection.execute('SELECT id, name FROM categories ORDER BY name');
    console.log(`   ✅ Found ${categories.length} categories`);
    
    // Step 3: Get or create farmer
    console.log('3. Ensuring sample farmer exists...');
    const userId = 'sample-user-123';
    const farmerId = 'sample-farmer-123';
    
    await connection.execute(`
      INSERT IGNORE INTO users (id, firstName, lastName, email, password, role, status, emailVerified, createdAt, updatedAt)
      VALUES (?, 'Sample', 'Farmer', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'FARMER', 'ACTIVE', true, NOW(), NOW())
    `, [userId]);
    
    await connection.execute(`
      INSERT IGNORE INTO farmers (id, userId, farmName, farmLocation, verified, rating, totalSales, createdAt, updatedAt)
      VALUES (?, ?, 'Green Valley Farm', 'Douala, Cameroon', true, 4.5, 0, NOW(), NOW())
    `, [farmerId, userId]);
    
    console.log('   ✅ Sample farmer ready');
    
    // Step 4: Define 8 products for each category
    console.log('4. Creating 8 products per category...');
    
    const productsByCategory = {
      'Cereals & Grains': [
        ['Yellow Maize (Corn)', 'High-quality yellow corn, locally grown and dried', 400, 'kg', 500],
        ['Premium White Rice', 'High quality lowland white rice, locally grown', 800, 'kg', 200],
        ['Red Sorghum', 'Traditional red sorghum variety, excellent for porridge', 550, 'kg', 300],
        ['Pearl Millet', 'Nutritious pearl millet grains, drought-resistant crop', 600, 'kg', 250],
        ['Wheat Flour', 'Fresh ground wheat flour, locally processed', 700, 'kg', 150],
        ['Brown Rice', 'Organic brown rice, unpolished and nutritious', 900, 'kg', 180],
        ['White Maize', 'Premium white corn variety, sweet and tender', 450, 'kg', 400],
        ['Fonio Grain', 'Ancient West African superfood grain, gluten-free', 1200, 'kg', 100]
      ],
      
      'Root Crops & Tubers': [
        ['Fresh Cassava', 'Sweet cassava variety, freshly harvested tubers', 300, 'kg', 500],
        ['Sweet Yam', 'Fresh yam tubers, excellent for cooking and frying', 450, 'kg', 300],
        ['Irish Potatoes', 'Fresh Irish potatoes, perfect for various dishes', 600, 'kg', 250],
        ['Sweet Potatoes', 'Orange-fleshed sweet potatoes, rich in vitamins', 400, 'kg', 350],
        ['Cocoyam (Taro)', 'Fresh cocoyam tubers, traditional Cameroon staple', 500, 'kg', 200],
        ['Ripe Plantains', 'Sweet ripe plantains, ready for cooking', 350, 'bunch', 180],
        ['Green Plantains', 'Unripe plantains, perfect for boiling and frying', 300, 'bunch', 220],
        ['Water Yam', 'Large water yam variety, excellent for pounding', 550, 'kg', 150]
      ],
      
      'Fruits': [
        ['Sweet Bananas', 'Ripe yellow bananas, perfect for eating fresh', 250, 'bunch', 200],
        ['Fresh Pineapple', 'Sweet tropical pineapples, locally grown', 600, 'piece', 150],
        ['Ripe Mangoes', 'Sweet Julie mangoes, locally grown and organic', 400, 'kg', 180],
        ['Fresh Papaya', 'Ripe papayas, sweet and nutritious', 350, 'piece', 120],
        ['Avocados', 'Fresh avocados, creamy and nutritious', 800, 'kg', 100],
        ['Bush Mango', 'Wild bush mango, traditional Cameroon fruit', 1000, 'kg', 80],
        ['Oranges', 'Sweet Valencia oranges, juicy and fresh', 300, 'kg', 250],
        ['Lemons', 'Fresh lemons, perfect for cooking and drinks', 500, 'kg', 150]
      ],
      
      'Vegetables': [
        ['Fresh Tomatoes', 'Organic red tomatoes, freshly harvested', 500, 'kg', 300],
        ['Fresh Spinach', 'Tender green spinach leaves, organically grown', 400, 'kg', 150],
        ['Red Onions', 'Fresh red bulb onions, locally grown', 700, 'kg', 200],
        ['Green Cabbage', 'Fresh cabbage heads, crisp and nutritious', 300, 'head', 180],
        ['Fresh Carrots', 'Orange carrots, sweet and crunchy', 450, 'kg', 160],
        ['Green Peppers', 'Fresh green bell peppers, locally grown', 600, 'kg', 140],
        ['Fresh Okra', 'Tender okra pods, perfect for soups', 350, 'kg', 120],
        ['Cucumber', 'Fresh cucumbers, crisp and refreshing', 400, 'kg', 100]
      ],
      
      'Legumes & Beans': [
        ['Groundnuts (Peanuts)', 'Spanish variety groundnuts, freshly harvested', 900, 'kg', 300],
        ['Black-eyed Peas', 'Fresh cowpeas, high protein content', 600, 'kg', 250],
        ['Red Kidney Beans', 'Premium red kidney beans, locally grown', 800, 'kg', 200],
        ['Soybeans', 'High-protein soybeans, excellent for processing', 700, 'kg', 280],
        ['White Beans', 'Fresh white beans, tender and nutritious', 650, 'kg', 220],
        ['Bambara Groundnuts', 'Traditional bambara nuts, drought-resistant', 1000, 'kg', 150],
        ['Green Beans', 'Fresh green beans, tender and crisp', 500, 'kg', 180],
        ['Lima Beans', 'Large lima beans, creamy and delicious', 750, 'kg', 160]
      ],
      
      'Cash Crops': [
        ['Cocoa Beans', 'Premium cocoa beans, sun-dried and fermented', 2000, 'kg', 100],
        ['Coffee Beans', 'Arabica coffee beans, mountain grown', 3000, 'kg', 80],
        ['Raw Cotton', 'High-quality cotton fiber, locally grown', 1500, 'kg', 120],
        ['Palm Oil', 'Fresh red palm oil, traditionally processed', 1200, 'liter', 150],
        ['Palm Kernels', 'Fresh palm kernels for oil extraction', 800, 'kg', 200],
        ['Rubber Latex', 'Fresh rubber latex from mature trees', 1000, 'liter', 100],
        ['Cocoa Pods', 'Fresh cocoa pods for processing', 500, 'kg', 180],
        ['Coffee Cherries', 'Fresh coffee cherries, ready for processing', 1800, 'kg', 90]
      ],
      
      'Spices & Herbs': [
        ['Fresh Ginger', 'Yellow ginger variety, aromatic and spicy', 1500, 'kg', 120],
        ['Hot Pepper', 'Fresh hot peppers, locally grown and spicy', 800, 'kg', 100],
        ['Fresh Garlic', 'Local garlic bulbs, aromatic and flavorful', 1200, 'kg', 80],
        ['Fresh Basil', 'Sweet basil leaves, perfect for cooking', 600, 'kg', 60],
        ['Lemongrass', 'Fresh lemongrass stalks, aromatic herb', 400, 'kg', 90],
        ['Fresh Mint', 'Spearmint leaves, refreshing and aromatic', 500, 'kg', 70],
        ['Turmeric Root', 'Fresh turmeric roots, anti-inflammatory spice', 1000, 'kg', 85],
        ['Scotch Bonnet Peppers', 'Very hot peppers, traditional variety', 1500, 'kg', 50]
      ]
    };
    
    let totalCreated = 0;
    
    // Create products for each category
    for (const category of categories) {
      const categoryName = category.name;
      const products = productsByCategory[categoryName];
      
      if (!products) {
        console.log(`   ⚠️ No products defined for category: ${categoryName}`);
        continue;
      }
      
      console.log(`\n   Creating products for: ${categoryName}`);
      
      for (let i = 0; i < products.length; i++) {
        const [name, description, price, unit, stock] = products[i];
        const productId = `product-${category.id}-${i + 1}`;
        
        try {
          await connection.execute(`
            INSERT INTO products (
              id, name, description, price, unit, stock, minOrder, images, status, 
              farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, 
              totalSold, createdAt, updatedAt
            ) VALUES (?, ?, ?, ?, ?, ?, 1, ?, 'ACTIVE', ?, ?, false, false, 0, 0, 0, NOW(), NOW())
          `, [
            productId, name, description, price, unit, stock,
            JSON.stringify(['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80']),
            farmerId, category.id
          ]);
          
          totalCreated++;
          console.log(`     ✅ ${i + 1}. ${name} - ${price} FCFA/${unit}`);
          
        } catch (error) {
          console.log(`     ❌ Failed to create ${name}:`, error.message);
        }
      }
    }
    
    // Step 5: Final verification
    const [productCount] = await connection.execute('SELECT COUNT(*) as count FROM products');
    const [categoryProductCounts] = await connection.execute(`
      SELECT c.name, COUNT(p.id) as product_count 
      FROM categories c 
      LEFT JOIN products p ON c.id = p.categoryId 
      GROUP BY c.id, c.name 
      ORDER BY c.name
    `);
    
    console.log(`\n🎉 Product creation completed!`);
    console.log(`   Total products created: ${totalCreated}`);
    console.log(`   Total products in database: ${productCount[0].count}`);
    
    console.log('\n📊 Products per category:');
    categoryProductCounts.forEach(cat => {
      console.log(`   ${cat.name}: ${cat.product_count} products`);
    });
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Product creation failed:', error.message);
  }
  
  process.exit(0);
}

create8ProductsPerCategory();
