const { Order, OrderItem, Product, User } = require('../models');

class OrderRepository {
  async create(orderData) {
    return await Order.create(orderData, {
      include: [OrderItem]
    });
  }

  async findById(id) {
    return await Order.findByPk(id, {
      include: [
        { model: OrderItem, include: [Product] },
        { model: User, attributes: ['id', 'email', 'firstName', 'lastName'] }
      ]
    });
  }

  async findAll(options = {}) {
    return await Order.findAll({
      ...options,
      include: [
        { model: OrderItem, include: [Product] },
        { model: User, attributes: ['id', 'email', 'firstName', 'lastName'] }
      ]
    });
  }

  async update(id, orderData) {
    const [updatedRowsCount, updatedOrders] = await Order.update(orderData, {
      where: { id },
      returning: true
    });
    return updatedOrders[0];
  }

  async delete(id) {
    return await Order.destroy({ where: { id } });
  }

  async findByUser(userId) {
    return await Order.findAll({
      where: { userId },
      include: [
        { model: OrderItem, include: [Product] },
        { model: User, attributes: ['id', 'email', 'firstName', 'lastName'] }
      ]
    });
  }
}

module.exports = new OrderRepository();