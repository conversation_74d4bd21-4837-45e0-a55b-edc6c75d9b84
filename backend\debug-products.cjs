// Debug products API
const axios = require('axios');

async function debugProducts() {
  console.log('🔍 Debugging Products API...\n');
  
  try {
    // Test 1: Check if products exist in database
    console.log('1. Testing products API...');
    const response = await axios.get('http://localhost:3001/api/products');
    
    console.log('   Status:', response.status);
    console.log('   Success:', response.data.success);
    console.log('   Total products:', response.data.data?.total || 0);
    console.log('   Products count:', response.data.data?.products?.length || 0);
    
    if (response.data.data?.products?.length > 0) {
      console.log('   ✅ Products found in API');
      console.log('   First product:', response.data.data.products[0].name);
    } else {
      console.log('   ❌ No products returned from API');
    }
    
    // Test 2: Check categories
    console.log('\n2. Testing categories API...');
    const categoriesResponse = await axios.get('http://localhost:3001/api/categories');
    
    console.log('   Categories count:', categoriesResponse.data.data?.length || 0);
    if (categoriesResponse.data.data?.length > 0) {
      console.log('   ✅ Categories found');
      console.log('   First category:', categoriesResponse.data.data[0].name);
    }
    
    // Test 3: Test with query parameters
    console.log('\n3. Testing products with pagination...');
    const paginatedResponse = await axios.get('http://localhost:3001/api/products?page=1&limit=10');
    
    console.log('   Paginated products:', paginatedResponse.data.data?.products?.length || 0);
    
    // Test 4: Test specific category filter
    if (categoriesResponse.data.data?.length > 0) {
      const firstCategory = categoriesResponse.data.data[0];
      console.log('\n4. Testing category filter...');
      console.log('   Testing category:', firstCategory.name);
      
      const categoryResponse = await axios.get(`http://localhost:3001/api/products?categoryId=${firstCategory.id}`);
      console.log('   Products in category:', categoryResponse.data.data?.products?.length || 0);
    }
    
  } catch (error) {
    console.error('❌ Products API error:', error.response?.data || error.message);
  }
}

debugProducts();
