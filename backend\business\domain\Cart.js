class Cart {
    constructor(id, userId, items = []) {
      this.id = id;
      this.userId = userId;
      this.items = items; // Array of CartItem objects
    }
  
    addItem(item) {
      const existingItem = this.items.find(i => i.productId === item.productId);
      if (existingItem) {
        existingItem.quantity += item.quantity;
      } else {
        this.items.push(item);
      }
    }
  
    removeItem(productId) {
      this.items = this.items.filter(item => item.productId !== productId);
    }
  
    updateItemQuantity(productId, quantity) {
      const item = this.items.find(i => i.productId === productId);
      if (item) {
        item.quantity = quantity;
      }
    }
  
    clear() {
      this.items = [];
    }
  
    get total() {
      return this.items.reduce((sum, item) => sum + item.subtotal, 0);
    }
  
    toJSON() {
      return {
        id: this.id,
        userId: this.userId,
        items: this.items.map(item => item.toJSON()),
        total: this.total
      };
    }
  }
  
  module.exports = Cart;