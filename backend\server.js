const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const { connectDB, getSequelize } = require('./config/database');
const { initializeModels } = require('./data/models');

dotenv.config();

const app = require('./app');

const port = process.env.PORT || 3001;

// Start server with database connection
const startServer = async () => {
  try {
    // Connect to database (MySQL or SQLite fallback)
    const sequelize = await connectDB();

    // Initialize all models and their relationships
    await initializeModels();

    // Start the server
    app.listen(port, () => {
      console.log(`🚀 Server is running on port ${port}`);
      console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 Health check: http://localhost:${port}/health`);
      console.log(`🗄️ Database: SQLite with full farm marketplace schema`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();




