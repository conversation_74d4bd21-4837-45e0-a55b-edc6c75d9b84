// Simple MySQL test with your project configuration
const mysql = require('mysql2/promise');

async function testMySQL() {
  console.log('🧪 Testing MySQL with your project settings...\n');
  
  try {
    // Use the same settings as your .env file
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '', // Empty password for Laragon
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ MySQL connection successful!');
    
    // Test basic queries
    const [dbResult] = await connection.execute('SELECT DATABASE() as db_name');
    console.log('📍 Connected to database:', dbResult[0].db_name);
    
    // Check if tables exist
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 Tables in database:', tables.length);
    
    if (tables.length > 0) {
      console.log('   Existing tables:');
      tables.forEach(table => {
        console.log(`   - ${Object.values(table)[0]}`);
      });
    } else {
      console.log('   No tables yet (will be created by <PERSON><PERSON><PERSON>)');
    }
    
    // Test creating a simple table
    console.log('\n🔧 Testing table creation...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS test_connection (
        id INT AUTO_INCREMENT PRIMARY KEY,
        message VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Insert test data
    await connection.execute(`
      INSERT INTO test_connection (message) VALUES ('MySQL connection test successful!')
    `);
    
    // Read test data
    const [testData] = await connection.execute('SELECT * FROM test_connection ORDER BY id DESC LIMIT 1');
    console.log('✅ Test data:', testData[0]);
    
    // Clean up test table
    await connection.execute('DROP TABLE test_connection');
    console.log('🧹 Cleaned up test table');
    
    await connection.end();
    console.log('\n🎉 MySQL is working perfectly with your project!');
    console.log('\n📝 Next steps:');
    console.log('1. Your backend server should now connect to MySQL');
    console.log('2. Sequelize will create all necessary tables automatically');
    console.log('3. Your project data will be stored in MySQL instead of SQLite');
    
  } catch (error) {
    console.error('❌ MySQL test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 MySQL is not running. Please:');
      console.log('1. Open Laragon');
      console.log('2. Make sure MySQL service is green/started');
      console.log('3. Try again');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Database not found. Please create it:');
      console.log('1. Open Laragon terminal');
      console.log('2. Run: mysql -u root -p');
      console.log('3. Run: CREATE DATABASE cameroon_farm_connect;');
    }
  }
}

testMySQL();
