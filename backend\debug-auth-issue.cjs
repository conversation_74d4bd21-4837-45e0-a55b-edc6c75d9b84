// Debug authentication issue
const axios = require('axios');

async function debugAuthIssue() {
  console.log('🔍 Debugging Authentication Issue...\n');
  
  try {
    // Test 1: Register a new user
    console.log('1. Testing User Registration...');
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Auth',
      lastName: 'Test',
      email: `auth.test.${Date.now()}@example.com`,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    if (registerResponse.data.success) {
      console.log('✅ Registration successful');
      const token = registerResponse.data.data.accessToken;
      console.log('   Token received:', token ? 'Yes' : 'No');
      console.log('   Token length:', token ? token.length : 0);
      
      // Test 2: Test protected route with token
      console.log('\n2. Testing Protected Route (Cart)...');
      try {
        const cartResponse = await axios.get('http://localhost:3001/api/cart', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('✅ Protected route accessible');
        console.log('   Cart response:', cartResponse.data.success ? 'Success' : 'Failed');
        
      } catch (cartError) {
        console.log('❌ Protected route failed:');
        console.log('   Status:', cartError.response?.status);
        console.log('   Error:', cartError.response?.data?.message || cartError.message);
      }
      
      // Test 3: Test farmer registration
      console.log('\n3. Testing Farmer Registration...');
      try {
        const farmerResponse = await axios.post('http://localhost:3001/api/auth/register', {
          firstName: 'Test',
          lastName: 'Farmer',
          email: `farmer.test.${Date.now()}@example.com`,
          password: 'Test123!',
          role: 'FARMER',
          farmName: 'Test Farm',
          farmLocation: 'Douala, Littoral'
        });
        
        if (farmerResponse.data.success) {
          console.log('✅ Farmer registration successful');
          const farmerToken = farmerResponse.data.data.accessToken;
          
          // Test farmer-specific routes
          console.log('\n4. Testing Farmer Routes...');
          try {
            // Test categories (should work without auth)
            const categoriesResponse = await axios.get('http://localhost:3001/api/categories');
            console.log('✅ Categories accessible:', categoriesResponse.data.success ? 'Yes' : 'No');
            
            // Test product creation (requires auth)
            console.log('\n5. Testing Product Creation Route...');
            const productData = new FormData();
            productData.append('name', 'Test Product');
            productData.append('description', 'Test Description');
            productData.append('price', '1000');
            productData.append('categoryId', categoriesResponse.data.data[0].id);
            productData.append('stock', '10');
            
            const productResponse = await axios.post('http://localhost:3001/api/products', productData, {
              headers: {
                'Authorization': `Bearer ${farmerToken}`,
                'Content-Type': 'multipart/form-data'
              }
            });
            
            console.log('✅ Product creation successful');
            
          } catch (productError) {
            console.log('❌ Product creation failed:');
            console.log('   Status:', productError.response?.status);
            console.log('   Error:', productError.response?.data?.message || productError.message);
            
            if (productError.response?.status === 401) {
              console.log('   🔍 This is the authorization error you\'re seeing');
            }
          }
          
        } else {
          console.log('❌ Farmer registration failed:', farmerResponse.data.message);
        }
        
      } catch (farmerError) {
        console.log('❌ Farmer registration failed:', farmerError.response?.data || farmerError.message);
      }
      
    } else {
      console.log('❌ Registration failed:', registerResponse.data.message);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.response?.data || error.message);
  }
}

debugAuthIssue();
