const express = require('express');
const serviceController = require('../controllers/serviceController');
const {authenticateToken} = require('../middleware/auth');
const { validateService } = require('../validators/serviceValidators');

const router = express.Router();

router.get('/', serviceController.listServices);
router.get('/:id', serviceController.getServiceById);
router.post('/', authenticateToken, validateService, serviceController.createService);
router.put('/:id', authenticateToken, validateService, serviceController.updateService);
router.delete('/:id', authenticateToken, serviceController.deleteService);
router.post('/book', authenticateToken, serviceController.bookService);
router.get('/bookings', authenticateToken, serviceController.getUserBookings);
router.put('/bookings/:id', authenticateToken, serviceController.updateBookingStatus);

module.exports = router;