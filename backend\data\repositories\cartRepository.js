const { Cart, CartItem, Product } = require('../models');

class CartRepository {
  async findByUserId(userId) {
    return await Cart.findOne({
      where: { userId },
      include: [{ model: CartItem, include: [Product] }]
    });
  }

  async create(cartData) {
    return await Cart.create(cartData);
  }

  async addItem(cartId, itemData) {
    return await CartItem.create({ ...itemData, cartId });
  }

  async removeItem(cartId, productId) {
    return await CartItem.destroy({
      where: { cartId, productId }
    });
  }

  async updateItemQuantity(cartId, productId, quantity) {
    return await CartItem.update(
      { quantity },
      { where: { cartId, productId } }
    );
  }

  async clearCart(cartId) {
    return await CartItem.destroy({
      where: { cartId }
    });
  }
}

module.exports = new CartRepository();