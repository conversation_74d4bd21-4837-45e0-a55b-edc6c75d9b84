const express = require('express');
const farmerController = require('../controllers/farmerController');
const { protect } = require('../../middleware/auth');
const {authenticateToken} = require('../middleware/auth');
const { validateFarmer } = require('../validators/farmerValidators');
const { asyncHandler } = require('../../middleware/errorHandler');
const { User, FarmerProfile } = require('../../data/models');

const router = express.Router();

// Existing routes
router.get('/', farmerController.listFarmers);
router.get('/:id', farmerController.getFarmerById);
router.get('/:id/products', farmerController.getFarmerProducts);
router.get('/:id/reviews', farmerController.getFarmerReviews);
router.post('/profile', authenticateToken, validateFarmer, farmerController.updateFarmerProfile);
router.post('/certification', authenticateToken, farmerController.addCertification);
router.get('/dashboard', authenticateToken, farmerController.getFarmerDashboard);

// New profile management routes
// @desc    Update farmer profile (new endpoint)
// @route   PUT /api/farmers/profile
// @access  Private (Farmer)
const updateFarmerProfileNew = asyncHandler(async (req, res) => {
  const {
    farmName,
    farmDescription,
    farmSize,
    farmLocation,
    specializations,
    certifications,
    establishedYear,
    farmType,
    businessPhone,
    whatsappNumber,
    deliveryRadius,
    deliveryFee,
    acceptsPickup,
    operatingHours
  } = req.body;

  // Find or create farmer profile
  let farmerProfile = await FarmerProfile.findOne({
    where: { userId: req.user.id }
  });

  const profileData = {
    userId: req.user.id,
    farmName,
    farmDescription,
    farmSize: farmSize ? parseFloat(farmSize) : null,
    farmLocation,
    specializations: Array.isArray(specializations) ? specializations : [],
    certifications: Array.isArray(certifications) ? certifications : [],
    establishedYear: establishedYear ? parseInt(establishedYear) : null,
    farmType: farmType || 'INDIVIDUAL',
    businessPhone,
    whatsappNumber,
    deliveryRadius: deliveryRadius ? parseFloat(deliveryRadius) : null,
    deliveryFee: deliveryFee ? parseFloat(deliveryFee) : null,
    acceptsPickup: acceptsPickup !== undefined ? acceptsPickup : true,
    operatingHours: operatingHours || {}
  };

  if (farmerProfile) {
    // Update existing profile
    await farmerProfile.update(profileData);
  } else {
    // Create new profile
    farmerProfile = await FarmerProfile.create(profileData);
  }

  // Update user's farmer profile status
  await User.update(
    { profileCompleted: true },
    { where: { id: req.user.id } }
  );

  res.json({
    success: true,
    data: farmerProfile,
    message: 'Farmer profile updated successfully'
  });
});

// @desc    Get farmer sales analytics
// @route   GET /api/farmers/sales-analytics
// @access  Private (Farmer)
const getFarmerSalesAnalytics = asyncHandler(async (req, res) => {
  const { period = '30' } = req.query;
  const farmerId = req.user.id;

  // Calculate date range
  const daysAgo = parseInt(period);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - daysAgo);

  const { Product, Order, OrderItem } = require('../../data/models');

  // Get farmer's products
  const products = await Product.findAll({
    where: { farmerId }
  });

  const productIds = products.map(p => p.id);

  // Get orders containing farmer's products
  const orders = await Order.findAll({
    where: {
      createdAt: { [require('sequelize').Op.gte]: startDate },
      status: { [require('sequelize').Op.in]: ['DELIVERED', 'SHIPPED', 'PROCESSING'] }
    },
    include: [
      {
        model: OrderItem,
        where: { productId: productIds },
        include: [{ model: Product }]
      },
      {
        model: User,
        attributes: ['firstName', 'lastName']
      }
    ]
  });

  // Calculate analytics
  let totalRevenue = 0;
  let totalProductsSold = 0;
  const productSales = {};

  orders.forEach(order => {
    order.OrderItems.forEach(item => {
      if (productIds.includes(item.productId)) {
        const revenue = item.price * item.quantity;
        totalRevenue += revenue;
        totalProductsSold += item.quantity;

        if (!productSales[item.productId]) {
          productSales[item.productId] = {
            id: item.productId,
            name: item.Product.name,
            totalSold: 0,
            revenue: 0,
            stock: item.Product.stock
          };
        }
        productSales[item.productId].totalSold += item.quantity;
        productSales[item.productId].revenue += revenue;
      }
    });
  });

  // Top selling products
  const topSellingProducts = Object.values(productSales)
    .sort((a, b) => b.totalSold - a.totalSold)
    .slice(0, 10);

  // Recent orders
  const recentOrders = orders.slice(0, 10).map(order => ({
    id: order.id,
    orderNumber: order.orderNumber,
    customerName: `${order.User.firstName} ${order.User.lastName}`,
    total: order.totalAmount,
    status: order.status,
    createdAt: order.createdAt,
    items: order.OrderItems.filter(item => productIds.includes(item.productId)).map(item => ({
      productName: item.Product.name,
      quantity: item.quantity,
      price: item.price
    }))
  }));

  // Sales by month (simplified)
  const salesByMonth = [];
  for (let i = 0; i < Math.min(6, Math.ceil(daysAgo / 30)); i++) {
    const monthStart = new Date();
    monthStart.setMonth(monthStart.getMonth() - i);
    monthStart.setDate(1);

    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthEnd.getMonth() + 1);

    const monthOrders = orders.filter(order =>
      order.createdAt >= monthStart && order.createdAt < monthEnd
    );

    const monthRevenue = monthOrders.reduce((sum, order) =>
      sum + order.OrderItems.reduce((itemSum, item) =>
        productIds.includes(item.productId) ? itemSum + (item.price * item.quantity) : itemSum, 0
      ), 0
    );

    salesByMonth.unshift({
      month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      revenue: monthRevenue,
      orders: monthOrders.length
    });
  }

  const analytics = {
    totalRevenue,
    totalOrders: orders.length,
    totalProductsSold,
    averageOrderValue: orders.length > 0 ? totalRevenue / orders.length : 0,
    topSellingProducts,
    recentOrders,
    salesByMonth
  };

  res.json({
    success: true,
    data: analytics
  });
});

// Add new routes
router.put('/profile', protect, updateFarmerProfileNew);
router.get('/sales-analytics', protect, getFarmerSalesAnalytics);

module.exports = router;