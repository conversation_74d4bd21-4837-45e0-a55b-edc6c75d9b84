const express = require('express');
const farmerController = require('../controllers/farmerController');
const { protect } = require('../../middleware/auth');
const {authenticateToken} = require('../middleware/auth');
const { validateFarmer } = require('../validators/farmerValidators');
const { asyncHandler } = require('../../middleware/errorHandler');
const { User, FarmerProfile } = require('../../data/models');

const router = express.Router();

// Existing routes
router.get('/', farmerController.listFarmers);
router.get('/:id', farmerController.getFarmerById);
router.get('/:id/products', farmerController.getFarmerProducts);
router.get('/:id/reviews', farmerController.getFarmerReviews);
router.post('/profile', authenticateToken, validateFarmer, farmerController.updateFarmerProfile);
router.post('/certification', authenticateToken, farmerController.addCertification);
router.get('/dashboard', authenticateToken, farmerController.getFarmerDashboard);

// New profile management routes
// @desc    Update farmer profile (new endpoint)
// @route   PUT /api/farmers/profile
// @access  Private (Farmer)
const updateFarmerProfileNew = asyncHandler(async (req, res) => {
  const {
    farmName,
    farmDescription,
    farmSize,
    farmLocation,
    specializations,
    certifications,
    establishedYear,
    farmType,
    businessPhone,
    whatsappNumber,
    deliveryRadius,
    deliveryFee,
    acceptsPickup,
    operatingHours
  } = req.body;

  // Find or create farmer profile
  let farmerProfile = await FarmerProfile.findOne({
    where: { userId: req.user.id }
  });

  const profileData = {
    userId: req.user.id,
    farmName,
    farmDescription,
    farmSize: farmSize ? parseFloat(farmSize) : null,
    farmLocation,
    specializations: Array.isArray(specializations) ? specializations : [],
    certifications: Array.isArray(certifications) ? certifications : [],
    establishedYear: establishedYear ? parseInt(establishedYear) : null,
    farmType: farmType || 'INDIVIDUAL',
    businessPhone,
    whatsappNumber,
    deliveryRadius: deliveryRadius ? parseFloat(deliveryRadius) : null,
    deliveryFee: deliveryFee ? parseFloat(deliveryFee) : null,
    acceptsPickup: acceptsPickup !== undefined ? acceptsPickup : true,
    operatingHours: operatingHours || {}
  };

  if (farmerProfile) {
    // Update existing profile
    await farmerProfile.update(profileData);
  } else {
    // Create new profile
    farmerProfile = await FarmerProfile.create(profileData);
  }

  // Update user's farmer profile status
  await User.update(
    { profileCompleted: true },
    { where: { id: req.user.id } }
  );

  res.json({
    success: true,
    data: farmerProfile,
    message: 'Farmer profile updated successfully'
  });
});

// Add new routes
router.put('/profile', protect, updateFarmerProfileNew);

module.exports = router;