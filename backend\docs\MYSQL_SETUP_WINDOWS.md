# MySQL Setup Guide for Windows

This guide will help you install and configure MySQL on Windows for the Cameroon Farm Connect Hub project.

## Quick Installation Steps

### Option 1: MySQL Installer (Recommended)

1. **Download MySQL Installer**
   - Go to: https://dev.mysql.com/downloads/installer/
   - Download "mysql-installer-web-community-8.0.xx.x.msi"

2. **Install MySQL**
   - Run the installer as Administrator
   - Choose "Developer Default" setup type
   - Click "Execute" to download and install components
   - Configure MySQL Server:
     - Config Type: Development Computer
     - Port: 3306 (default)
     - Root Password: `Amizonech1+` (same as your .env file)
     - Create a user account (optional)
   - Complete the installation

3. **Verify Installation**
   - Open Command Prompt as Administrator
   - Run: `mysql -u root -p`
   - Enter password: `Amizonech1+`
   - You should see MySQL prompt: `mysql>`

### Option 2: XAMPP (Easier Alternative)

1. **Download XAMPP**
   - Go to: https://www.apachefriends.org/download.html
   - Download XAMPP for Windows

2. **Install XAMPP**
   - Run installer as Administrator
   - Select MySQL component
   - Install to default location

3. **Start MySQL**
   - Open XAMPP Control Panel
   - Click "Start" next to MySQL
   - MySQL should show "Running" status

4. **Set Root Password**
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Go to User accounts → root → Edit privileges
   - Set password to: `Amizonech1+`

## Create Database

### Using MySQL Command Line

```sql
-- Connect to MySQL
mysql -u root -p

-- Create database
CREATE DATABASE cameroon_farm_connect;

-- Verify database creation
SHOW DATABASES;

-- Exit MySQL
EXIT;
```

### Using phpMyAdmin (if using XAMPP)

1. Open http://localhost/phpmyadmin
2. Click "New" in the left sidebar
3. Database name: `cameroon_farm_connect`
4. Click "Create"

## Test Connection

Create a test file to verify your connection:

```javascript
// test-connection.js
const mysql = require('mysql2');

const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'Amizonech1+',
  database: 'cameroon_farm_connect'
});

connection.connect((err) => {
  if (err) {
    console.error('❌ Connection failed:', err.message);
  } else {
    console.log('✅ Connected to MySQL successfully!');
  }
  connection.end();
});
```

Run: `node test-connection.js`

## Troubleshooting

### Common Issues

1. **"Access denied for user 'root'@'localhost'"**
   - Reset root password using MySQL installer
   - Or use XAMPP and set password via phpMyAdmin

2. **"Can't connect to MySQL server"**
   - Ensure MySQL service is running
   - Check Windows Services (services.msc)
   - Look for "MySQL80" service

3. **Port 3306 already in use**
   - Stop other MySQL instances
   - Or change port in MySQL configuration

### Reset MySQL Root Password

1. Stop MySQL service
2. Open Command Prompt as Administrator
3. Navigate to MySQL bin directory:
   ```cmd
   cd "C:\Program Files\MySQL\MySQL Server 8.0\bin"
   ```
4. Start MySQL without password:
   ```cmd
   mysqld --skip-grant-tables
   ```
5. Open another Command Prompt and connect:
   ```cmd
   mysql -u root
   ```
6. Reset password:
   ```sql
   USE mysql;
   ALTER USER 'root'@'localhost' IDENTIFIED BY 'Amizonech1+';
   FLUSH PRIVILEGES;
   EXIT;
   ```
7. Restart MySQL service normally

## Next Steps

After MySQL is installed and running:

1. **Verify your .env file** (should already be correct)
2. **Install dependencies**: `npm install`
3. **Start the server**: `npm run dev`

Your backend should now connect successfully to MySQL!

## Alternative: Use SQLite for Development

If MySQL installation is problematic, I can help you switch to SQLite which requires no installation and works out of the box.
