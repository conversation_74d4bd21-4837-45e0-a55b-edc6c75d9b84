
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from "recharts";
import { TrendingUp, Users, ShoppingCart, DollarSign, MapPin, Calendar, Award, Target } from "lucide-react";

const AnalyticsDashboard = () => {
  const salesData = [
    { month: "Jan", sales: 12000, orders: 85, farmers: 45 },
    { month: "Feb", sales: 15000, orders: 92, farmers: 48 },
    { month: "Mar", sales: 18000, orders: 110, farmers: 52 },
    { month: "Apr", sales: 22000, orders: 125, farmers: 58 },
    { month: "May", sales: 25000, orders: 140, farmers: 62 },
    { month: "Jun", sales: 28000, orders: 165, farmers: 68 }
  ];

  const categoryData = [
    { name: "Cocoa", value: 35, sales: 150000, color: "#8B4513" },
    { name: "Coffee", value: 25, sales: 120000, color: "#6B4423" },
    { name: "Fruits", value: 20, sales: 80000, color: "#FF6B6B" },
    { name: "Vegetables", value: 15, sales: 60000, color: "#4ECDC4" },
    { name: "Others", value: 5, sales: 20000, color: "#95A5A6" }
  ];

  const regionData = [
    { region: "Southwest", farmers: 45, products: 120, sales: 180000 },
    { region: "Northwest", farmers: 38, products: 95, sales: 145000 },
    { region: "Littoral", farmers: 32, products: 85, sales: 125000 },
    { region: "Centre", farmers: 28, products: 70, sales: 98000 },
    { region: "West", farmers: 25, products: 65, sales: 85000 },
    { region: "Others", farmers: 42, products: 80, sales: 110000 }
  ];

  const topFarmers = [
    { name: "Paul Mbe", region: "Southwest", sales: 45000, products: 12, rating: 4.9 },
    { name: "Marie Fon", region: "Northwest", sales: 38000, products: 8, rating: 4.8 },
    { name: "Jean Douala", region: "Littoral", sales: 32000, products: 15, rating: 4.7 },
    { name: "Grace Ngozi", region: "East", sales: 28000, products: 10, rating: 4.6 },
    { name: "Samuel Tabi", region: "Southwest", sales: 25000, products: 6, rating: 4.8 }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="enhanced-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sales</p>
                <p className="text-3xl font-bold text-green-600">2.8M FCFA</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +12.5% from last month
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="enhanced-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Farmers</p>
                <p className="text-3xl font-bold text-blue-600">248</p>
                <p className="text-xs text-blue-600 flex items-center mt-1">
                  <Users className="w-3 h-3 mr-1" />
                  +18 new this month
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="enhanced-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <p className="text-3xl font-bold text-purple-600">1,847</p>
                <p className="text-xs text-purple-600 flex items-center mt-1">
                  <ShoppingCart className="w-3 h-3 mr-1" />
                  +8.2% from last month
                </p>
              </div>
              <ShoppingCart className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="enhanced-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg. Rating</p>
                <p className="text-3xl font-bold text-yellow-600">4.7</p>
                <p className="text-xs text-yellow-600 flex items-center mt-1">
                  <Award className="w-3 h-3 mr-1" />
                  98% satisfaction
                </p>
              </div>
              <Award className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg">
          <TabsTrigger value="overview" className="rounded-xl">Overview</TabsTrigger>
          <TabsTrigger value="categories" className="rounded-xl">Categories</TabsTrigger>
          <TabsTrigger value="regions" className="rounded-xl">Regions</TabsTrigger>
          <TabsTrigger value="farmers" className="rounded-xl">Top Farmers</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="enhanced-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-primary" />
                  Sales Trend (6 Months)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={salesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => [`${value} FCFA`, name]} />
                    <Line type="monotone" dataKey="sales" stroke="#10B981" strokeWidth={3} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="enhanced-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="w-5 h-5 text-primary" />
                  Orders & Farmers Growth
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={salesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="orders" fill="#3B82F6" name="Orders" />
                    <Bar dataKey="farmers" fill="#8B5CF6" name="Farmers" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="enhanced-card">
              <CardHeader>
                <CardTitle>Product Categories Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={categoryData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="enhanced-card">
              <CardHeader>
                <CardTitle>Category Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {categoryData.map((category, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: category.color }}
                      ></div>
                      <span className="font-medium">{category.name}</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{category.sales.toLocaleString()} FCFA</p>
                      <p className="text-sm text-muted-foreground">{category.value}% of total</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="regions" className="space-y-6">
          <Card className="enhanced-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5 text-primary" />
                Regional Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {regionData.map((region, index) => (
                  <Card key={index} className="p-4">
                    <div className="space-y-2">
                      <h4 className="font-semibold text-lg">{region.region}</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Farmers:</span>
                          <span className="font-medium">{region.farmers}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Products:</span>
                          <span className="font-medium">{region.products}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Sales:</span>
                          <span className="font-medium text-green-600">{region.sales.toLocaleString()} FCFA</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="farmers" className="space-y-6">
          <Card className="enhanced-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5 text-primary" />
                Top Performing Farmers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topFarmers.map((farmer, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center justify-center w-10 h-10 bg-primary text-primary-foreground rounded-full font-bold">
                        #{index + 1}
                      </div>
                      <div>
                        <h4 className="font-semibold">{farmer.name}</h4>
                        <p className="text-sm text-muted-foreground flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {farmer.region}
                        </p>
                      </div>
                    </div>
                    <div className="text-right space-y-1">
                      <p className="font-semibold text-green-600">{farmer.sales.toLocaleString()} FCFA</p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{farmer.products} products</span>
                        <Badge variant="secondary" className="text-xs">
                          ⭐ {farmer.rating}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex gap-4 justify-center">
        <Button className="gradient-agricultural text-white">
          <Target className="w-4 h-4 mr-2" />
          Set Sales Goals
        </Button>
        <Button variant="outline">
          <Calendar className="w-4 h-4 mr-2" />
          Generate Report
        </Button>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
