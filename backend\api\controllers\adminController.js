const adminService = require('../../business/services/adminService');

exports.getDashboard = async (req, res, next) => {
  try {
    const dashboard = await adminService.getDashboard();
    res.json(dashboard);
  } catch (error) {
    next(error);
  }
};

exports.updateListingQuality = async (req, res, next) => {
  try {
    const updatedListing = await adminService.updateListingQuality(req.params.id, req.body.qualityScore);
    res.json(updatedListing);
  } catch (error) {
    next(error);
  }
};

exports.getLowQualityListings = async (req, res, next) => {
  try {
    const listings = await adminService.getLowQualityListings();
    res.json(listings);
  } catch (error) {
    next(error);
  }
};

exports.approveVendor = async (req, res, next) => {
  try {
    const vendor = await adminService.approveVendor(req.params.id);
    res.json(vendor);
  } catch (error) {
    next(error);
  }
};