import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight } from 'lucide-react';

interface CategoryCardProps {
  id?: string;
  name: string;
  description?: string;
  image: string;
  itemCount?: number;
  onClick?: (categoryId: string) => void;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  id,
  name,
  description,
  image,
  itemCount,
  onClick,
  className = '',
  variant = 'default'
}) => {
  const handleClick = () => {
    if (onClick && id) {
      onClick(id);
    }
  };

  if (variant === 'compact') {
    return (
      <Card 
        className={`group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-0 bg-white rounded-xl overflow-hidden ${className}`}
        onClick={handleClick}
      >
        <div className="flex items-center p-4">
          <div className="w-16 h-16 rounded-full overflow-hidden mr-4 flex-shrink-0">
            <img
              src={image || '/placeholder.svg'}
              alt={name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-base font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
              {name}
            </h3>
            {itemCount && (
              <p className="text-sm text-gray-500">
                {itemCount} items
              </p>
            )}
          </div>
          <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-green-600 transition-colors" />
        </div>
      </Card>
    );
  }

  if (variant === 'featured') {
    return (
      <Card 
        className={`group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-0 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl overflow-hidden ${className}`}
        onClick={handleClick}
      >
        <div className="relative p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-16 h-16 rounded-2xl overflow-hidden bg-white shadow-lg">
              <img
                src={image || '/placeholder.svg'}
                alt={name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            {itemCount && (
              <Badge className="bg-green-600 text-white font-semibold">
                {itemCount} items
              </Badge>
            )}
          </div>
          
          <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">
            {name}
          </h3>
          
          {description && (
            <p className="text-sm text-gray-600 mb-4 line-clamp-2">
              {description}
            </p>
          )}
          
          <Button 
            variant="ghost" 
            className="p-0 h-auto text-green-600 hover:text-green-700 font-semibold group-hover:translate-x-1 transition-all duration-200"
          >
            Explore <ArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </Card>
    );
  }

  // Default variant
  return (
    <Card 
      className={`group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-0 bg-white rounded-2xl overflow-hidden ${className}`}
      onClick={handleClick}
    >
      <div className="relative overflow-hidden">
        {/* Category Image */}
        <div className="relative bg-gray-50 aspect-square">
          <img
            src={image || '/placeholder.svg'}
            alt={name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />
          
          {/* Hover overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300" />
          
          {/* Item count badge */}
          {itemCount && (
            <Badge className="absolute top-3 right-3 bg-white/95 text-gray-900 font-semibold shadow-lg">
              {itemCount} items
            </Badge>
          )}
        </div>
      </div>

      <CardContent className="p-4">
        {/* Category Title */}
        <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-200">
          {name}
        </h3>

        {/* Description */}
        {description && (
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
            {description}
          </p>
        )}

        {/* Explore Button */}
        <Button 
          className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2.5 rounded-lg transition-all duration-200 hover:shadow-lg group-hover:scale-105"
        >
          Explore <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </CardContent>
    </Card>
  );
};

export default CategoryCard;
