// const express = require('express');
// const router = express.Router();

// // Import route modules
// const authRoutes = require('./auth');
// const userRoutes = require('./users');
// const productRoutes = require('./products');
// const categoryRoutes = require('./categories');
// const orderRoutes = require('./orders');
// const cartRoutes = require('./cart');
// const reviewRoutes = require('./reviews');
// const vendorRoutes = require('./vendors');
// const adminRoutes = require('./admin');

// // Define API routes
// router.use('/auth', authRoutes);
// router.use('/users', userRoutes);
// router.use('/products', productRoutes);
// router.use('/categories', categoryRoutes);
// router.use('/orders', orderRoutes);
// router.use('/cart', cartRoutes);
// router.use('/reviews', reviewRoutes);
// router.use('/vendors', vendorRoutes);
// router.use('/admin', adminRoutes);

// module.exports = router;