// Check database for products
const { Product, Category, Farmer, User } = require('./data/models');

async function checkDatabase() {
  console.log('🔍 Checking Database for Products...\n');
  
  try {
    // Check products count
    const productCount = await Product.count();
    console.log('Total products in database:', productCount);
    
    if (productCount > 0) {
      console.log('✅ Products exist in database');
      
      // Get first few products
      const products = await Product.findAll({
        limit: 5,
        include: [
          {
            model: Category,
            attributes: ['id', 'name']
          },
          {
            model: Farmer,
            include: [
              {
                model: User,
                attributes: ['firstName', 'lastName']
              }
            ]
          }
        ]
      });
      
      console.log('\nFirst 5 products:');
      products.forEach((product, index) => {
        console.log(`${index + 1}. ${product.name} - ${product.price} FCFA`);
        console.log(`   Category: ${product.Category?.name || 'No category'}`);
        console.log(`   Farmer: ${product.Farmer?.User?.firstName || 'Unknown'} ${product.Farmer?.User?.lastName || ''}`);
        console.log(`   Status: ${product.status}`);
        console.log('');
      });
      
    } else {
      console.log('❌ No products found in database');
    }
    
    // Check categories
    const categoryCount = await Category.count();
    console.log('Total categories in database:', categoryCount);
    
    // Check farmers
    const farmerCount = await Farmer.count();
    console.log('Total farmers in database:', farmerCount);
    
    // Check users
    const userCount = await User.count();
    console.log('Total users in database:', userCount);
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  }
  
  process.exit(0);
}

checkDatabase();
