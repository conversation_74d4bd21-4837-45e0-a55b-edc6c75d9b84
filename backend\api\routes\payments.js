const express = require('express');
const paymentController = require('../controllers/paymentController');
const {authenticateToken} = require('../middleware/auth');

const router = express.Router();

// Process payment
router.post('/process', authenticateToken, paymentController.processPayment);

// Refund payment
router.post('/refund/:orderId', authenticateToken, paymentController.refundPayment);

// Get payment methods
router.get('/methods', paymentController.getPaymentMethods);

// Verify payment status
router.get('/status/:orderId', authenticateToken, paymentController.getPaymentStatus);

// Stripe webhook
router.post('/webhook', paymentController.handleWebhook);

// Mobile money payment
router.post('/mobile-money', authenticateToken, paymentController.processMobileMoneyPayment);

// Cash on delivery
router.post('/cash-on-delivery', authenticateToken, paymentController.processCashOnDelivery);

module.exports = router;