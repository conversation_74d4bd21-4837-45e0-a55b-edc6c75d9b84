
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 1s ease-out;
}

/* Line clamp utility */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced design system for Cameroon Agricultural Platform */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;

    --secondary: 142 30% 90%;
    --secondary-foreground: 142 76% 20%;

    --muted: 142 10% 95%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 35 100% 85%;
    --accent-foreground: 35 100% 25%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 76% 36%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 142 76% 45%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-br from-green-50 via-white to-amber-50 text-foreground font-sans;
  }
}

/* Enhanced Agricultural Gradients */
.gradient-agricultural {
  background: linear-gradient(135deg, #065f46 0%, #10b981 40%, #34d399 100%);
  box-shadow: 0 10px 40px rgba(16, 185, 129, 0.3);
}

.gradient-earth {
  background: linear-gradient(135deg, #92400e 0%, #d97706 40%, #f59e0b 100%);
  box-shadow: 0 10px 40px rgba(217, 119, 6, 0.3);
}

.gradient-sky {
  background: linear-gradient(135deg, #0c4a6e 0%, #0284c7 40%, #38bdf8 100%);
  box-shadow: 0 10px 40px rgba(2, 132, 199, 0.3);
}

.gradient-fresh {
  background: linear-gradient(135deg, #166534 0%, #22c55e 40%, #86efac 100%);
}

/* Enhanced Card Styles */
.enhanced-card {
  @apply bg-white/80 backdrop-blur-sm border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1;
}

.glass-card {
  @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-lg;
}

/* Professional Service Cards */
.service-card {
  @apply bg-gradient-to-br from-white to-green-50 border-2 border-green-100 hover:border-green-300 transition-all duration-300 rounded-xl p-6;
}

/* Freshness Indicators */
.freshness-excellent {
  @apply bg-green-100 text-green-800 border-green-200;
}

.freshness-good {
  @apply bg-yellow-100 text-yellow-800 border-yellow-200;
}

.freshness-warning {
  @apply bg-orange-100 text-orange-800 border-orange-200;
}

/* Offline Mode Styles */
.offline-mode {
  @apply bg-gray-100 border-gray-300 text-gray-600;
}

/* Animation Enhancements */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Payment Method Styles */
.payment-card {
  @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-4 hover:scale-105 transition-transform;
}

.mobile-money {
  @apply bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-lg p-4;
}
