const userService = require('../services/userService');

class UserManager {
  async createUser(userData) {
    // Additional validation or business logic can be added here
    return await userService.createUser(userData);
  }

  async getUserById(id) {
    return await userService.getUserById(id);
  }

  async updateUser(id, userData) {
    // Add any cross-cutting concerns or additional logic here
    return await userService.updateUser(id, userData);
  }

  async deleteUser(id) {
    // Add any additional checks or logic before deletion
    return await userService.deleteUser(id);
  }

  async getAllUsers(options) {
    // Add any filtering or pagination logic here
    return await userService.getAllUsers(options);
  }

  async changeUserPassword(userId, oldPassword, newPassword) {
    return await userService.changePassword(userId, oldPassword, newPassword);
  }
}

module.exports = new UserManager();