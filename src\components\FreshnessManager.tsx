import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Thermometer, Droplets, Wind, Calendar, AlertTriangle, Clock } from "lucide-react";

interface FreshnessData {
  productName: string;
  harvestDate: string;
  shelfLife: number;
  currentCondition: "excellent" | "good" | "warning" | "critical";
  temperature: number;
  humidity: number;
  storageMethod: string;
  preservationTips: string[];
}

interface FreshnessManagerProps {
  products: FreshnessData[];
}

const FreshnessManager = ({ products }: FreshnessManagerProps) => {
  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case "excellent": return "freshness-excellent";
      case "good": return "bg-green-100 text-green-700";
      case "warning": return "freshness-warning";
      case "critical": return "bg-red-100 text-red-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getConditionIcon = (condition: string) => {
    switch (condition) {
      case "critical": return <AlertTriangle className="w-4 h-4" />;
      default: return <Thermometer className="w-4 h-4" />;
    }
  };

  return (
    <Card className="enhanced-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Droplets className="w-5 h-5 text-blue-500" />
          Freshness Management System
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {products.map((product, index) => (
          <div key={index} className="border rounded-lg p-4 space-y-3">
            <div className="flex justify-between items-start">
              <h4 className="font-semibold">{product.productName}</h4>
              <Badge className={`${getConditionBadge(product.currentCondition)} flex items-center gap-1`}>
                {getConditionIcon(product.currentCondition)}
                {product.currentCondition.toUpperCase()}
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <span>Harvested: {product.harvestDate}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span>Shelf Life: {product.shelfLife} days</span>
              </div>
              <div className="flex items-center gap-2">
                <Thermometer className="w-4 h-4 text-muted-foreground" />
                <span>{product.temperature}°C</span>
              </div>
              <div className="flex items-center gap-2">
                <Droplets className="w-4 h-4 text-muted-foreground" />
                <span>{product.humidity}% humidity</span>
              </div>
            </div>

            <div className="bg-muted/30 p-3 rounded-lg">
              <p className="text-sm font-medium mb-2">Storage Method:</p>
              <p className="text-sm text-muted-foreground">{product.storageMethod}</p>
            </div>

            <div>
              <p className="text-sm font-medium mb-2">Preservation Tips:</p>
              <ul className="text-sm text-muted-foreground space-y-1">
                {product.preservationTips.map((tip, tipIndex) => (
                  <li key={tipIndex} className="flex items-start gap-2">
                    <span className="text-primary">•</span>
                    {tip}
                  </li>
                ))}
              </ul>
            </div>

            <Button size="sm" variant="outline" className="w-full">
              Get Professional Advice
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default FreshnessManager;
