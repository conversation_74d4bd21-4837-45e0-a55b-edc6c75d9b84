const { Product, Category } = require('../models');

class ProductRepository {
  async create(productData) {
    return await Product.create(productData);
  }

  async findById(id) {
    return await Product.findByPk(id, {
      include: [{ model: Category, as: 'category' }]
    });
  }

  async findAll(options = {}) {
    return await Product.findAll({
      ...options,
      include: [{ model: Category, as: 'category' }]
    });
  }

  async update(id, productData) {
    const [updatedRowsCount, updatedProducts] = await Product.update(productData, {
      where: { id },
      returning: true
    });
    return updatedProducts[0];
  }

  async delete(id) {
    return await Product.destroy({ where: { id } });
  }

  async findByCategory(categoryId) {
    return await Product.findAll({
      where: { categoryId },
      include: [{ model: Category, as: 'category' }]
    });
  }

  async search(query) {
    const { Op } = require('sequelize');
    return await Product.findAll({
      where: {
        [Op.or]: [
          { name: { [Op.iLike]: `%${query}%` } },
          { description: { [Op.iLike]: `%${query}%` } }
        ]
      },
      include: [{ model: Category, as: 'category' }]
    });
  }
}

module.exports = new ProductRepository();