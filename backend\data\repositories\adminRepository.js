const { Vendor, ProductListing, Order } = require('../models');
const { Op } = require('sequelize');

class AdminRepository {
  async getVendorCount() {
    return await Vendor.count();
  }

  async getProductCount() {
    return await ProductListing.count();
  }

  async getOrderCount() {
    return await Order.count();
  }

  async getTotalRevenue() {
    return await Order.sum('totalAmount');
  }

  async updateListingQuality(listingId, qualityScore) {
    const listing = await ProductListing.findByPk(listingId);
    if (!listing) throw new Error('Listing not found');
    return await listing.update({ qualityScore });
  }

  async getLowQualityListings() {
    return await ProductListing.findAll({
      where: { qualityScore: { [Op.lt]: 3 } },
      include: [{ model: Vendor, attributes: ['companyName'] }]
    });
  }

  async approveVendor(vendorId) {
    const vendor = await Vendor.findByPk(vendorId);
    if (!vendor) throw new Error('Vendor not found');
    return await vendor.update({ isApproved: true });
  }
}

module.exports = new AdminRepository();