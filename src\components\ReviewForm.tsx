import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Star, Upload, X } from 'lucide-react';
import authService from '@/services/authService';
import { toast } from 'sonner';

interface ReviewFormProps {
  productId: string;
  productName: string;
  orderId?: string;
  onClose: () => void;
  onSuccess?: () => void;
}

const ReviewForm: React.FC<ReviewFormProps> = ({
  productId,
  productName,
  orderId,
  onClose,
  onSuccess
}) => {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [title, setTitle] = useState('');
  const [comment, setComment] = useState('');
  const [images, setImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);

  const queryClient = useQueryClient();

  // Debug: Check if user is logged in
  React.useEffect(() => {
    const token = localStorage.getItem('accessToken');
    const user = localStorage.getItem('user');
    console.log('🔍 Review Form - Auth check:');
    console.log('  Token exists:', !!token);
    console.log('  User exists:', !!user);
    console.log('  Product ID:', productId);
    console.log('  Product Name:', productName);
  }, [productId, productName]);

  // Submit review mutation
  const submitReviewMutation = useMutation({
    mutationFn: async (reviewData: FormData) => {
      console.log('🔍 Submitting review for product:', productId);
      console.log('🔍 FormData contents:');
      for (let [key, value] of reviewData.entries()) {
        console.log(`  ${key}:`, value);
      }

      const response = await authService.authenticatedFetch('http://localhost:3001/api/reviews', {
        method: 'POST',
        body: reviewData,
      });

      console.log('🔍 Response status:', response.status);
      const result = await response.json();
      console.log('🔍 Response data:', result);

      return result;
    },
    onSuccess: (data) => {
      console.log('✅ Review submission successful:', data);
      if (data.success) {
        toast.success('Review submitted successfully!');

        // Safely invalidate queries
        try {
          queryClient.invalidateQueries({ queryKey: ['product-reviews', productId] });
          queryClient.invalidateQueries({ queryKey: ['products'] });
          // Don't invalidate individual product queries to avoid the undefined error
        } catch (error) {
          console.warn('Warning: Could not invalidate some queries:', error);
        }

        onSuccess?.();
        onClose();
      } else {
        console.error('❌ Review submission failed:', data.message);
        toast.error(data.message || 'Failed to submit review');
      }
    },
    onError: (error) => {
      console.error('❌ Review submission error:', error);
      toast.error('Failed to submit review. Please try again.');
    },
  });

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length + images.length > 5) {
      toast.error('Maximum 5 images allowed');
      return;
    }

    setImages(prev => [...prev, ...files]);

    // Create preview URLs
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviewUrls(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    console.log('🚀 Starting review submission...');
    console.log('📝 Form validation:');
    console.log('  Rating:', rating);
    console.log('  Comment length:', comment.trim().length);
    console.log('  Product ID:', productId);

    if (rating === 0) {
      console.log('❌ Validation failed: No rating selected');
      toast.error('Please select a rating');
      return;
    }

    if (!comment.trim()) {
      console.log('❌ Validation failed: No comment provided');
      toast.error('Please write a review comment');
      return;
    }

    if (comment.trim().length < 10) {
      console.log('❌ Validation failed: Comment too short');
      toast.error('Comment must be at least 10 characters long');
      return;
    }

    console.log('✅ Form validation passed, creating FormData...');

    const formData = new FormData();
    formData.append('productId', productId);
    formData.append('rating', rating.toString());
    formData.append('title', title);
    formData.append('comment', comment);

    if (orderId) {
      formData.append('orderId', orderId);
    }

    // Add images
    images.forEach(image => {
      formData.append('images', image);
    });

    console.log('📤 Submitting review...');
    submitReviewMutation.mutate(formData);
  };

  const renderStars = () => {
    return Array.from({ length: 5 }, (_, i) => {
      const starValue = i + 1;
      return (
        <button
          key={i}
          type="button"
          className={`text-2xl transition-colors ${
            starValue <= (hoveredRating || rating)
              ? 'text-yellow-400'
              : 'text-gray-300'
          }`}
          onMouseEnter={() => setHoveredRating(starValue)}
          onMouseLeave={() => setHoveredRating(0)}
          onClick={() => setRating(starValue)}
        >
          <Star className="w-8 h-8 fill-current" />
        </button>
      );
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Write a Review</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
          <p className="text-sm text-gray-600">Share your experience with {productName}</p>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Rating */}
            <div>
              <Label className="text-base font-medium">Rating *</Label>
              <div className="flex items-center gap-1 mt-2">
                {renderStars()}
                <span className="ml-2 text-sm text-gray-600">
                  {rating > 0 && (
                    <>
                      {rating} star{rating !== 1 ? 's' : ''}
                      {rating === 1 && ' - Poor'}
                      {rating === 2 && ' - Fair'}
                      {rating === 3 && ' - Good'}
                      {rating === 4 && ' - Very Good'}
                      {rating === 5 && ' - Excellent'}
                    </>
                  )}
                </span>
              </div>
            </div>

            {/* Review Title */}
            <div>
              <Label htmlFor="title">Review Title (Optional)</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Summarize your review in a few words"
                className="mt-1"
              />
            </div>

            {/* Review Comment */}
            <div>
              <Label htmlFor="comment">Your Review *</Label>
              <Textarea
                id="comment"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder="Tell others about your experience with this product..."
                rows={4}
                className="mt-1"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Minimum 10 characters ({comment.length}/10)
              </p>
            </div>

            {/* Image Upload */}
            <div>
              <Label>Add Photos (Optional)</Label>
              <div className="mt-2">
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Images
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Maximum 5 images, up to 5MB each
                </p>
              </div>

              {/* Image Previews */}
              {imagePreviewUrls.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-3">
                  {imagePreviewUrls.map((url, index) => (
                    <div key={index} className="relative">
                      <img
                        src={url}
                        alt={`Preview ${index + 1}`}
                        className="w-20 h-20 object-cover rounded-md border"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={rating === 0 || comment.length < 10 || submitReviewMutation.isPending}
                className="bg-green-600 hover:bg-green-700"
              >
                {submitReviewMutation.isPending ? 'Submitting...' : 'Submit Review'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReviewForm;
