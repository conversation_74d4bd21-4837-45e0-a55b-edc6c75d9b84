-- Recreate Cameroon Farm Connect Hub Database
-- Run this in Laragon MySQL terminal

-- Drop existing database if it exists
DROP DATABASE IF EXISTS cameroon_farm_connect;

-- Create new database
CREATE DATABASE cameroon_farm_connect;
USE cameroon_farm_connect;

-- Create Users table
CREATE TABLE users (
  id VARCHAR(255) PRIMARY KEY,
  firstName VARCHAR(255) NOT NULL,
  lastName VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  role ENUM('CUSTOMER', 'FARMER', 'ADMIN') DEFAULT 'CUSTOMER',
  status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') DEFAULT 'ACTIVE',
  emailVerified BOOLEAN DEFAULT FALSE,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create Categories table
CREATE TABLE categories (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  isActive BOOLEAN DEFAULT TRUE,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create Farmers table
CREATE TABLE farmers (
  id VARCHAR(255) PRIMARY KEY,
  userId VARCHAR(255) NOT NULL,
  farmName VARCHAR(255) NOT NULL,
  farmLocation VARCHAR(255),
  verified BOOLEAN DEFAULT FALSE,
  rating DECIMAL(3,2) DEFAULT 0.00,
  totalSales INT DEFAULT 0,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
);

-- Create Products table
CREATE TABLE products (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  unit VARCHAR(50) NOT NULL,
  stock INT DEFAULT 0,
  minOrder INT DEFAULT 1,
  images JSON,
  status ENUM('ACTIVE', 'INACTIVE', 'OUT_OF_STOCK') DEFAULT 'ACTIVE',
  farmerId VARCHAR(255) NOT NULL,
  categoryId VARCHAR(255) NOT NULL,
  isOrganic BOOLEAN DEFAULT FALSE,
  isFairTrade BOOLEAN DEFAULT FALSE,
  rating DECIMAL(3,2) DEFAULT 0.00,
  totalReviews INT DEFAULT 0,
  totalSold INT DEFAULT 0,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (farmerId) REFERENCES farmers(id) ON DELETE CASCADE,
  FOREIGN KEY (categoryId) REFERENCES categories(id) ON DELETE CASCADE
);

-- Create Carts table
CREATE TABLE carts (
  id VARCHAR(255) PRIMARY KEY,
  userId VARCHAR(255) NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
);

-- Create CartItems table
CREATE TABLE cartitems (
  id VARCHAR(255) PRIMARY KEY,
  cartId VARCHAR(255) NOT NULL,
  productId VARCHAR(255) NOT NULL,
  quantity INT NOT NULL DEFAULT 1,
  price DECIMAL(10,2) NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (cartId) REFERENCES carts(id) ON DELETE CASCADE,
  FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
);

-- Create Wishlists table
CREATE TABLE wishlists (
  id VARCHAR(255) PRIMARY KEY,
  userId VARCHAR(255) NOT NULL,
  productId VARCHAR(255) NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_product (userId, productId)
);

-- Create Orders table
CREATE TABLE orders (
  id VARCHAR(255) PRIMARY KEY,
  orderNumber VARCHAR(255) UNIQUE NOT NULL,
  userId VARCHAR(255) NOT NULL,
  status ENUM('PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED') DEFAULT 'PENDING',
  totalAmount DECIMAL(10,2) NOT NULL,
  shippingAddress JSON NOT NULL,
  paymentMethod ENUM('cod', 'mobile_money', 'bank_transfer') DEFAULT 'cod',
  paymentStatus ENUM('PENDING', 'PAID', 'FAILED', 'REFUNDED') DEFAULT 'PENDING',
  phoneNumber VARCHAR(20),
  notes TEXT,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
);

-- Create OrderItems table
CREATE TABLE orderitems (
  id VARCHAR(255) PRIMARY KEY,
  orderId VARCHAR(255) NOT NULL,
  productId VARCHAR(255) NOT NULL,
  quantity INT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  farmerId VARCHAR(255) NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (orderId) REFERENCES orders(id) ON DELETE CASCADE,
  FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE,
  FOREIGN KEY (farmerId) REFERENCES farmers(id) ON DELETE CASCADE
);

-- Insert Categories (7 core agricultural categories)
INSERT INTO categories (id, name, description, isActive, createdAt, updatedAt) VALUES
('cat-cereals', 'Cereals & Grains', 'Maize, rice, millet, sorghum, wheat', TRUE, NOW(), NOW()),
('cat-roots', 'Root Crops & Tubers', 'Cassava, yam, sweet potato, Irish potato, cocoyam, plantain', TRUE, NOW(), NOW()),
('cat-fruits', 'Fruits', 'Banana, pineapple, mango, papaya, avocado, citrus fruits, bush mango', TRUE, NOW(), NOW()),
('cat-vegetables', 'Vegetables', 'Tomato, pepper, onion, garlic, carrot, okra, cucumber, spinach, lettuce, cabbage', TRUE, NOW(), NOW()),
('cat-legumes', 'Legumes & Beans', 'Groundnuts, cowpea, beans, soybeans, bambara groundnut', TRUE, NOW(), NOW()),
('cat-cash', 'Cash Crops', 'Cocoa, coffee, cotton, palm oil', TRUE, NOW(), NOW()),
('cat-spices', 'Spices & Herbs', 'Ginger, pepper, basil, lemongrass, mint, garlic', TRUE, NOW(), NOW());

-- Insert Sample User
INSERT INTO users (id, firstName, lastName, email, password, role, status, emailVerified, createdAt, updatedAt) VALUES
('farmer-user-001', 'John', 'Farmer', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'FARMER', 'ACTIVE', TRUE, NOW(), NOW());

-- Insert Sample Farmer
INSERT INTO farmers (id, userId, farmName, farmLocation, verified, rating, totalSales, createdAt, updatedAt) VALUES
('farmer-001', 'farmer-user-001', 'Green Valley Farm', 'Douala, Cameroon', TRUE, 4.5, 0, NOW(), NOW());

-- Database recreation completed successfully!
SELECT 'Database recreated successfully!' as status;
