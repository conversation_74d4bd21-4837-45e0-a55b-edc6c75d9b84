const express = require('express');
const { Wishlist, Product, User, Category } = require('../data/models');
const { protect } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const router = express.Router();

// @desc    Get user's wishlist
// @route   GET /api/wishlist
// @access  Private
const getWishlist = asyncHandler(async (req, res) => {
  const wishlistItems = await Wishlist.findAll({
    where: { UserId: req.user.id },
    include: [
      {
        model: Product,
        include: [
          { model: Category },
          { model: User, as: 'Farmer', attributes: ['id', 'username', 'email'] }
        ]
      }
    ],
    order: [['createdAt', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      wishlist: wishlistItems,
      count: wishlistItems.length
    }
  });
});

// @desc    Add item to wishlist
// @route   POST /api/wishlist/add
// @access  Private
const addToWishlist = asyncHandler(async (req, res) => {
  const { productId } = req.body;

  if (!productId) {
    return res.status(400).json({
      success: false,
      message: 'Product ID is required'
    });
  }

  // Check if product exists
  const product = await Product.findByPk(productId);
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  // Check if item already exists in wishlist
  const existingItem = await Wishlist.findOne({
    where: {
      UserId: req.user.id,
      ProductId: productId
    }
  });

  if (existingItem) {
    return res.status(400).json({
      success: false,
      message: 'Product already in wishlist'
    });
  }

  // Add to wishlist
  const wishlistItem = await Wishlist.create({
    UserId: req.user.id,
    ProductId: productId
  });

  // Get the created item with product details
  const createdItem = await Wishlist.findByPk(wishlistItem.id, {
    include: [
      {
        model: Product,
        include: [
          { model: Category },
          { model: User, as: 'Farmer', attributes: ['id', 'username', 'email'] }
        ]
      }
    ]
  });

  res.status(201).json({
    success: true,
    message: 'Product added to wishlist successfully',
    data: {
      wishlistItem: createdItem
    }
  });
});

// @desc    Remove item from wishlist
// @route   DELETE /api/wishlist/remove/:productId
// @access  Private
const removeFromWishlist = asyncHandler(async (req, res) => {
  const { productId } = req.params;

  // Find and delete wishlist item
  const wishlistItem = await Wishlist.findOne({
    where: {
      UserId: req.user.id,
      ProductId: productId
    }
  });

  if (!wishlistItem) {
    return res.status(404).json({
      success: false,
      message: 'Product not found in wishlist'
    });
  }

  await wishlistItem.destroy();

  res.json({
    success: true,
    message: 'Product removed from wishlist successfully'
  });
});

// @desc    Clear entire wishlist
// @route   DELETE /api/wishlist/clear
// @access  Private
const clearWishlist = asyncHandler(async (req, res) => {
  await Wishlist.destroy({
    where: { UserId: req.user.id }
  });

  res.json({
    success: true,
    message: 'Wishlist cleared successfully'
  });
});

// @desc    Check if product is in wishlist
// @route   GET /api/wishlist/check/:productId
// @access  Private
const checkWishlist = asyncHandler(async (req, res) => {
  const { productId } = req.params;

  const wishlistItem = await Wishlist.findOne({
    where: {
      UserId: req.user.id,
      ProductId: productId
    }
  });

  res.json({
    success: true,
    data: {
      inWishlist: !!wishlistItem
    }
  });
});

// @desc    Move wishlist items to cart
// @route   POST /api/wishlist/move-to-cart
// @access  Private
const moveToCart = asyncHandler(async (req, res) => {
  const { productIds } = req.body; // Array of product IDs to move

  if (!productIds || !Array.isArray(productIds)) {
    return res.status(400).json({
      success: false,
      message: 'Product IDs array is required'
    });
  }

  const { Cart, CartItem } = require('../data/models');

  // Get or create cart
  let cart = await Cart.findOne({
    where: { UserId: req.user.id }
  });

  if (!cart) {
    cart = await Cart.create({
      UserId: req.user.id,
      total: 0
    });
  }

  const results = [];
  const errors = [];

  for (const productId of productIds) {
    try {
      // Check if product exists in wishlist
      const wishlistItem = await Wishlist.findOne({
        where: {
          UserId: req.user.id,
          ProductId: productId
        },
        include: [{ model: Product }]
      });

      if (!wishlistItem) {
        errors.push(`Product ${productId} not found in wishlist`);
        continue;
      }

      // Check stock
      if (wishlistItem.Product.stock < 1) {
        errors.push(`Product ${wishlistItem.Product.name} is out of stock`);
        continue;
      }

      // Check if already in cart
      let cartItem = await CartItem.findOne({
        where: {
          CartId: cart.id,
          ProductId: productId
        }
      });

      if (cartItem) {
        // Update quantity
        cartItem.quantity += 1;
        await cartItem.save();
      } else {
        // Create new cart item
        cartItem = await CartItem.create({
          CartId: cart.id,
          ProductId: productId,
          quantity: 1,
          price: wishlistItem.Product.price
        });
      }

      // Remove from wishlist
      await wishlistItem.destroy();

      results.push({
        productId,
        productName: wishlistItem.Product.name,
        moved: true
      });

    } catch (error) {
      errors.push(`Error moving product ${productId}: ${error.message}`);
    }
  }

  res.json({
    success: true,
    message: `${results.length} items moved to cart`,
    data: {
      moved: results,
      errors: errors.length > 0 ? errors : undefined
    }
  });
});

// Routes
router.get('/', protect, getWishlist);
router.post('/add', protect, addToWishlist);
router.delete('/remove/:productId', protect, removeFromWishlist);
router.delete('/clear', protect, clearWishlist);
router.get('/check/:productId', protect, checkWishlist);
router.post('/move-to-cart', protect, moveToCart);

module.exports = router;
