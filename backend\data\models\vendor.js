const { DataTypes } = require('sequelize');
const {sequelize} = require('../../config/database');

const Vendor = sequelize.define('Vendor', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
  },
 
  description: {
    type: DataTypes.TEXT,
  },
  isApproved: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
});

Vendor.associate = (models) => {
  Vendor.belongsTo(models.User);
  Vendor.hasMany(models.ProductListing);
};

module.exports = Vendor;