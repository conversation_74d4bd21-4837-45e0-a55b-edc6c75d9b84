// Debug reference record errors
const mysql = require('mysql2/promise');

async function debugReferenceError() {
  console.log('🔍 Debugging Reference Record Issues...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    // Check if basic records exist
    console.log('📊 Checking Database Records:');
    
    // 1. Check Users
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    console.log(`   Users: ${users[0].count} records`);
    
    // 2. Check Farmers
    const [farmers] = await connection.execute('SELECT COUNT(*) as count FROM farmers');
    console.log(`   Farmers: ${farmers[0].count} records`);
    
    // 3. Check Categories
    const [categories] = await connection.execute('SELECT COUNT(*) as count FROM categories');
    console.log(`   Categories: ${categories[0].count} records`);
    
    // 4. Check Products
    const [products] = await connection.execute('SELECT COUNT(*) as count FROM products');
    console.log(`   Products: ${products[0].count} records`);
    
    // 5. Check Carts
    const [carts] = await connection.execute('SELECT COUNT(*) as count FROM carts');
    console.log(`   Carts: ${carts[0].count} records`);
    
    // Check for orphaned records
    console.log('\n🔗 Checking Foreign Key Relationships:');
    
    // Products without valid farmers
    const [orphanProducts] = await connection.execute(`
      SELECT p.id, p.name, p.farmerId 
      FROM products p 
      LEFT JOIN farmers f ON p.farmerId = f.id 
      WHERE f.id IS NULL
      LIMIT 5
    `);
    
    if (orphanProducts.length > 0) {
      console.log('❌ Products without valid farmers:');
      orphanProducts.forEach(p => {
        console.log(`   - ${p.name} (farmerId: ${p.farmerId})`);
      });
    } else {
      console.log('✅ All products have valid farmers');
    }
    
    // Products without valid categories
    const [orphanCategories] = await connection.execute(`
      SELECT p.id, p.name, p.categoryId 
      FROM products p 
      LEFT JOIN categories c ON p.categoryId = c.id 
      WHERE c.id IS NULL
      LIMIT 5
    `);
    
    if (orphanCategories.length > 0) {
      console.log('❌ Products without valid categories:');
      orphanCategories.forEach(p => {
        console.log(`   - ${p.name} (categoryId: ${p.categoryId})`);
      });
    } else {
      console.log('✅ All products have valid categories');
    }
    
    // Carts without valid users
    const [orphanCarts] = await connection.execute(`
      SELECT c.id, c.userId 
      FROM carts c 
      LEFT JOIN users u ON c.userId = u.id 
      WHERE u.id IS NULL
      LIMIT 5
    `);
    
    if (orphanCarts.length > 0) {
      console.log('❌ Carts without valid users:');
      orphanCarts.forEach(c => {
        console.log(`   - Cart ${c.id} (userId: ${c.userId})`);
      });
    } else {
      console.log('✅ All carts have valid users');
    }
    
    // Check sample data
    console.log('\n📋 Sample Valid Records:');
    
    // Sample users
    const [sampleUsers] = await connection.execute('SELECT id, firstName, lastName, email FROM users LIMIT 3');
    console.log('👥 Sample Users:');
    sampleUsers.forEach(u => {
      console.log(`   - ${u.firstName} ${u.lastName} (ID: ${u.id})`);
    });
    
    // Sample products
    const [sampleProducts] = await connection.execute('SELECT id, name, farmerId, categoryId FROM products LIMIT 3');
    console.log('\n📦 Sample Products:');
    sampleProducts.forEach(p => {
      console.log(`   - ${p.name} (ID: ${p.id}, Farmer: ${p.farmerId}, Category: ${p.categoryId})`);
    });
    
    // Sample categories
    const [sampleCategories] = await connection.execute('SELECT id, name FROM categories LIMIT 3');
    console.log('\n📁 Sample Categories:');
    sampleCategories.forEach(c => {
      console.log(`   - ${c.name} (ID: ${c.id})`);
    });
    
    await connection.end();
    console.log('\n✅ Reference check completed!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugReferenceError();
