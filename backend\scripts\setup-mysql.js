#!/usr/bin/env node

/**
 * MySQL Setup Script
 * This script helps set up MySQL for the Cameroon Farm Connect Hub
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Cameroon Farm Connect Hub - MySQL Setup');
console.log('==========================================\n');

// Check if Docker is available
function checkDocker() {
  try {
    execSync('docker --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

// Check if MySQL is running locally
function checkMySQL() {
  try {
    // Try to connect to MySQL
    execSync('mysql --version', { stdio: 'ignore' });
    return { installed: true };
  } catch (error) {
    return { installed: false };
  }
}

// Create .env file from template
function createEnvFile() {
  const envExamplePath = path.join(__dirname, '..', '.env.example');
  const envPath = path.join(__dirname, '..', '.env');
  
  if (!fs.existsSync(envPath)) {
    if (fs.existsSync(envExamplePath)) {
      fs.copyFileSync(envExamplePath, envPath);
      console.log('✅ Created .env file from template');
      console.log('⚠️  Please update the DATABASE_URL and other settings in .env file');
    } else {
      console.log('❌ .env.example file not found');
    }
  } else {
    console.log('ℹ️  .env file already exists');
  }
}

// Setup MySQL with Docker
function setupDockerMySQL() {
  console.log('🐳 Setting up MySQL with Docker...\n');
  
  const containerName = 'cameroon_farm_mysql';
  const password = 'CameroonFarm123!';
  const database = 'cameroon_farm_connect';
  
  try {
    // Check if container already exists
    try {
      execSync(`docker inspect ${containerName}`, { stdio: 'ignore' });
      console.log('ℹ️  MySQL container already exists');
      
      // Start the container if it's not running
      execSync(`docker start ${containerName}`, { stdio: 'ignore' });
      console.log('✅ MySQL container started');
    } catch (error) {
      // Container doesn't exist, create it
      console.log('📦 Creating new MySQL container...');
      
      const dockerCommand = `docker run --name ${containerName} ` +
                           `-e MYSQL_ROOT_PASSWORD=${password} ` +
                           `-e MYSQL_DATABASE=${database} ` +
                           `-p 3306:3306 ` +
                           `-d mysql:8.0`;
      
      execSync(dockerCommand, { stdio: 'inherit' });
      console.log('✅ MySQL container created and started');
      
      // Wait a moment for MySQL to initialize
      console.log('⏳ Waiting for MySQL to initialize...');
      setTimeout(() => {
        console.log('✅ MySQL should be ready now');
      }, 10000);
    }
    
    console.log('\n📋 Connection Details:');
    console.log(`   Host: localhost`);
    console.log(`   Port: 3306`);
    console.log(`   Username: root`);
    console.log(`   Password: ${password}`);
    console.log(`   Database: ${database}`);
    
    console.log('\n🔗 Connection String:');
    console.log(`   DATABASE_URL="mysql://root:${password}@localhost:3306/${database}"`);
    
    return true;
  } catch (error) {
    console.log('❌ Failed to setup MySQL with Docker:', error.message);
    return false;
  }
}

// Function to provide MySQL installation guidance
function provideMySQLGuidance() {
  console.log('\n📖 MySQL Installation Guide:');
  console.log('=============================');
  console.log('1. Download MySQL from:');
  console.log('   https://dev.mysql.com/downloads/mysql/');
  console.log('2. Choose MySQL Community Server');
  console.log('3. Install MySQL with these settings:');
  console.log('   - Root password: Set a strong password');
  console.log('   - Port: 3306 (default)');
  console.log('   - Start MySQL as a service');
  console.log('4. Create database "cameroon_farm_connect"');
  console.log('5. Optional: Install MySQL Workbench for GUI management');
  console.log('\n📚 Alternative: Use XAMPP/WAMP/MAMP for easy setup');
}

// Main setup function
function main() {
  console.log('🔍 Checking system requirements...\n');
  
  // Create .env file
  createEnvFile();
  
  // Check for existing MySQL
  const mysqlStatus = checkMySQL();
  if (mysqlStatus.installed) {
    console.log('✅ MySQL is installed locally');
    console.log('🎉 You can use your existing MySQL installation');
    console.log('📋 Make sure to:');
    console.log('   1. Create database "cameroon_farm_connect"');
    console.log('   2. Update DATABASE_URL in .env file');
    console.log('   3. Ensure MySQL service is running');
    
    console.log('\n🔗 Example Connection String:');
    console.log('   DATABASE_URL="mysql://root:your_password@localhost:3306/cameroon_farm_connect"');
  } else {
    console.log('ℹ️  MySQL not detected locally');
    
    console.log('\n🎯 Recommended: Install MySQL or use Docker');
    
    // Check for Docker as alternative
    if (checkDocker()) {
      console.log('\n✅ Docker is available');
      
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      console.log('\nChoose your setup method:');
      console.log('1. Docker MySQL (Recommended - Easy setup)');
      console.log('2. Install MySQL locally');
      
      rl.question('Enter your choice (1 or 2): ', (answer) => {
        if (answer === '1') {
          setupDockerMySQL();
        } else {
          provideMySQLGuidance();
        }
        rl.close();
      });
    } else {
      console.log('❌ Docker is not available');
      provideMySQLGuidance();
    }
  }
  
  console.log('\n📚 Next Steps:');
  console.log('   1. Update .env file with your database connection');
  console.log('   2. Run: npm install');
  console.log('   3. Run: npm run db:generate (for Prisma)');
  console.log('   4. Run: npm run migrate (for Prisma)');
  console.log('   5. Run: npm run seed (optional)');
  console.log('   6. Run: npm run dev');
  
  console.log('\n📖 For detailed setup instructions, see:');
  console.log('   - docs/MYSQL_SETUP.md');
  console.log('   - README.md');
}

// Run the setup
main();
