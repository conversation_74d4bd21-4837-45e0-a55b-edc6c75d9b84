const { Category } = require('../../data/models');
const categoryRepository = require('../../data/repositories/categoryRepository');

class CategoryService {
  async createCategory(categoryData) {
    return await categoryRepository.create(categoryData);
  }

  async getCategoryById(id) {
    return await categoryRepository.findById(id);
  }

  async updateCategory(id, categoryData) {
    return await categoryRepository.update(id, categoryData);
  }

  async deleteCategory(id) {
    return await categoryRepository.delete(id);
  }

  async getAllCategories() {
    return await categoryRepository.findAll();
  }

  async getCategoryWithProducts(id) {
    return await Category.findByPk(id, {
      include: ['products']
    });
  }
}

module.exports = new CategoryService();