// Import necessary modules
const { Product, Category, User, Farmer } = require('../../data/models');
const { Op } = require('sequelize');
const { deleteFiles } = require('../../middleware/upload');

// Get all products
exports.getAllProducts = async (req, res, next) => {
  try {
    const { category, search, sort, limit = 20, offset = 0, status = 'ACTIVE' } = req.query;

    // Build where clause for filtering
    let whereClause = { status };
    let orderClause = [];

    // Category filtering
    if (category) {
      whereClause.categoryId = category;
    }

    // Search filtering
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // Sorting
    switch (sort) {
      case 'price_asc':
        orderClause = [['price', 'ASC']];
        break;
      case 'price_desc':
        orderClause = [['price', 'DESC']];
        break;
      case 'newest':
        orderClause = [['createdAt', 'DESC']];
        break;
      case 'rating':
        orderClause = [['rating', 'DESC']];
        break;
      case 'popular':
        orderClause = [['totalSold', 'DESC']];
        break;
      default:
        orderClause = [['name', 'ASC']];
    }

    // Find all products with filters
    const products = await Product.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Category,
          attributes: ['id', 'name', 'description']
        },
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id', 'firstName', 'lastName', 'email']
            }
          ]
        }
      ],
      order: orderClause,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Return products with pagination info
    res.json({
      success: true,
      data: {
        products: products.rows,
        pagination: {
          total: products.count,
          limit: parseInt(limit),
          offset: parseInt(offset),
          pages: Math.ceil(products.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get a specific product by ID
exports.getProductById = async (req, res, next) => {
  try {
    // Find the product by id, including its category and farmer info
    const product = await Product.findByPk(req.params.id, {
      include: [
        {
          model: Category,
          attributes: ['id', 'name', 'description']
        },
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
            }
          ]
        }
      ]
    });

    // If product not found, return 404
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Return the product
    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    next(error);
  }
};

// Create a new product
exports.createProduct = async (req, res, next) => {
  try {
    const {
      name,
      description,
      price,
      unit,
      stock,
      minOrder,
      categoryId,
      harvestDate,
      expiryDate,
      nutritionInfo,
      storageInfo,
      isOrganic,
      isFairTrade
    } = req.body;

    // Validate required fields
    if (!name || !description || !price || !categoryId) {
      return res.status(400).json({
        success: false,
        message: 'Name, description, price, and category are required'
      });
    }

    // Check if category exists
    const category = await Category.findByPk(categoryId);
    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Get farmer profile for the authenticated user
    const farmer = await Farmer.findOne({ where: { userId: req.user.id } });
    if (!farmer) {
      return res.status(400).json({
        success: false,
        message: 'Only farmers can create products. Please complete your farmer profile first.'
      });
    }

    // Handle uploaded images
    let images = [];
    if (req.uploadedImages && req.uploadedImages.length > 0) {
      images = req.uploadedImages.map(img => img.path);
    }

    // Create a new product
    const newProduct = await Product.create({
      name,
      description,
      price: parseFloat(price),
      unit: unit || 'kg',
      stock: parseInt(stock) || 0,
      minOrder: parseInt(minOrder) || 1,
      categoryId,
      farmerId: farmer.id,
      harvestDate: harvestDate ? new Date(harvestDate) : null,
      expiryDate: expiryDate ? new Date(expiryDate) : null,
      nutritionInfo: nutritionInfo || null,
      storageInfo: storageInfo || null,
      isOrganic: Boolean(isOrganic),
      isFairTrade: Boolean(isFairTrade),
      status: 'ACTIVE',
      images: images
    });

    // Fetch the created product with includes
    const productWithDetails = await Product.findByPk(newProduct.id, {
      include: [
        {
          model: Category,
          attributes: ['id', 'name', 'description']
        },
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id', 'firstName', 'lastName', 'email']
            }
          ]
        }
      ]
    });

    // Return the created product
    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: productWithDetails
    });
  } catch (error) {
    next(error);
  }
};

// Update a product
exports.updateProduct = async (req, res, next) => {
  try {
    // Find the product by id with farmer info
    const product = await Product.findByPk(req.params.id, {
      include: [
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id']
            }
          ]
        }
      ]
    });

    // If product not found, return 404
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if the user owns this product (only farmers can update their own products)
    if (product.Farmer.User.id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own products'
      });
    }

    const {
      name,
      description,
      price,
      unit,
      stock,
      minOrder,
      categoryId,
      harvestDate,
      expiryDate,
      nutritionInfo,
      storageInfo,
      isOrganic,
      isFairTrade,
      status
    } = req.body;

    // Validate category if provided
    if (categoryId) {
      const category = await Category.findByPk(categoryId);
      if (!category) {
        return res.status(400).json({
          success: false,
          message: 'Category not found'
        });
      }
    }

    // Update the product
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (price !== undefined) updateData.price = parseFloat(price);
    if (unit !== undefined) updateData.unit = unit;
    if (stock !== undefined) updateData.stock = parseInt(stock);
    if (minOrder !== undefined) updateData.minOrder = parseInt(minOrder);
    if (categoryId !== undefined) updateData.categoryId = categoryId;
    if (harvestDate !== undefined) updateData.harvestDate = harvestDate ? new Date(harvestDate) : null;
    if (expiryDate !== undefined) updateData.expiryDate = expiryDate ? new Date(expiryDate) : null;
    if (nutritionInfo !== undefined) updateData.nutritionInfo = nutritionInfo;
    if (storageInfo !== undefined) updateData.storageInfo = storageInfo;
    if (isOrganic !== undefined) updateData.isOrganic = Boolean(isOrganic);
    if (isFairTrade !== undefined) updateData.isFairTrade = Boolean(isFairTrade);
    if (status !== undefined) updateData.status = status;

    await product.update(updateData);

    // Fetch updated product with all details
    const updatedProduct = await Product.findByPk(product.id, {
      include: [
        {
          model: Category,
          attributes: ['id', 'name', 'description']
        },
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id', 'firstName', 'lastName', 'email']
            }
          ]
        }
      ]
    });

    // Return the updated product
    res.json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
  } catch (error) {
    next(error);
  }
};

// Delete a product
exports.deleteProduct = async (req, res, next) => {
  try {
    // Find the product by id with farmer info
    const product = await Product.findByPk(req.params.id, {
      include: [
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id']
            }
          ]
        }
      ]
    });

    // If product not found, return 404
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if the user owns this product (only farmers can delete their own products)
    if (product.Farmer.User.id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own products'
      });
    }

    // Delete associated images
    if (product.images && product.images.length > 0) {
      deleteFiles(product.images);
    }

    // Delete the product
    await product.destroy();

    // Return success message
    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Get products by farmer
exports.getProductsByFarmer = async (req, res, next) => {
  try {
    const { farmerId } = req.params;
    const { limit = 20, offset = 0, status = 'ACTIVE' } = req.query;

    // Find farmer
    const farmer = await Farmer.findByPk(farmerId, {
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    if (!farmer) {
      return res.status(404).json({
        success: false,
        message: 'Farmer not found'
      });
    }

    // Get farmer's products
    const products = await Product.findAndCountAll({
      where: {
        farmerId,
        status
      },
      include: [
        {
          model: Category,
          attributes: ['id', 'name', 'description']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        farmer: farmer,
        products: products.rows,
        pagination: {
          total: products.count,
          limit: parseInt(limit),
          offset: parseInt(offset),
          pages: Math.ceil(products.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Upload images to existing product
exports.uploadProductImages = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find the product
    const product = await Product.findByPk(id, {
      include: [
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id']
            }
          ]
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check ownership
    if (product.Farmer.User.id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only upload images to your own products'
      });
    }

    // Check if images were uploaded
    if (!req.uploadedImages || req.uploadedImages.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No images uploaded'
      });
    }

    // Add new images to existing ones
    const currentImages = product.images || [];
    const newImages = req.uploadedImages.map(img => img.path);
    const allImages = [...currentImages, ...newImages];

    // Limit to maximum 5 images
    if (allImages.length > 5) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 5 images allowed per product'
      });
    }

    // Update product with new images
    await product.update({ images: allImages });

    res.json({
      success: true,
      message: 'Images uploaded successfully',
      data: {
        images: allImages,
        newImages: newImages
      }
    });
  } catch (error) {
    next(error);
  }
};

// Delete specific image from product
exports.deleteProductImage = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { imagePath } = req.body;

    // Find the product
    const product = await Product.findByPk(id, {
      include: [
        {
          model: Farmer,
          include: [
            {
              model: User,
              attributes: ['id']
            }
          ]
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check ownership
    if (product.Farmer.User.id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete images from your own products'
      });
    }

    // Check if image exists in product
    const currentImages = product.images || [];
    if (!currentImages.includes(imagePath)) {
      return res.status(404).json({
        success: false,
        message: 'Image not found in product'
      });
    }

    // Remove image from array
    const updatedImages = currentImages.filter(img => img !== imagePath);

    // Delete physical file
    deleteFiles([imagePath]);

    // Update product
    await product.update({ images: updatedImages });

    res.json({
      success: true,
      message: 'Image deleted successfully',
      data: {
        images: updatedImages
      }
    });
  } catch (error) {
    next(error);
  }
};
