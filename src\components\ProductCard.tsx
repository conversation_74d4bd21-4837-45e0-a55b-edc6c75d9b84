
import { useState } from "react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Star, ShoppingCart, Loader2, Heart, Eye, GitCompare, MessageSquare } from "lucide-react";
import { toast } from "sonner";
import ReviewForm from "./ReviewForm";

interface ProductCardProps {
  id?: string;
  name: string;
  image: string;
  price: string;
  originalPrice?: string;
  region: string;
  farmer: string;
  rating: number;
  reviewCount?: number;
  category: string;
  inStock: boolean;
  isOnSale?: boolean;
  isHot?: boolean;
  isNew?: boolean;
  discount?: number;
  onAddToCart?: (productId: string) => void;
  onAddToWishlist?: (productId: string) => void;
  onCompare?: (productId: string) => void;
  onViewProduct?: (productId: string) => void;
}

const ProductCard = ({
  id,
  name,
  image,
  price,
  originalPrice,
  region,
  farmer,
  rating,
  reviewCount,
  category,
  inStock,
  isOnSale,
  isHot,
  isNew,
  discount,
  onAddToCart,
  onAddToWishlist,
  onCompare,
  onViewProduct
}: ProductCardProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!id) {
      toast.error("Product ID not available");
      return;
    }

    setIsLoading(true);
    try {
      if (onAddToCart) {
        await onAddToCart(id);
      } else {
        toast.error("Please login to add items to cart");
      }
    } catch (error) {
      console.error("Failed to add to cart:", error);
      toast.error("Failed to add item to cart");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToWishlist = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!id) {
      toast.error("Product ID not available");
      return;
    }

    try {
      if (onAddToWishlist) {
        await onAddToWishlist(id);
      } else {
        toast.error("Please login to add items to wishlist");
      }
    } catch (error) {
      console.error("Failed to add to wishlist:", error);
      toast.error("Failed to add item to wishlist");
    }
  };

  const handleCompare = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onCompare && id) {
      onCompare(id);
      toast.success(`${name} added to compare!`);
    }
  };

  const handleViewProduct = () => {
    if (onViewProduct && id) {
      onViewProduct(id);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const discountPercentage = originalPrice && parseFloat(originalPrice.replace('$', '')) > parseFloat(price.replace('$', ''))
    ? Math.round(((parseFloat(originalPrice.replace('$', '')) - parseFloat(price.replace('$', ''))) / parseFloat(originalPrice.replace('$', ''))) * 100)
    : discount;
  return (
    <Card
      className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-0 bg-white rounded-2xl overflow-hidden"
      onClick={handleViewProduct}
    >
      <div className="relative overflow-hidden">
        {/* Product Image */}
        <div className="relative bg-gray-50 aspect-square">
          <img
            src={image}
            alt={name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />

          {/* Hover overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />
        </div>

        {/* Status Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-1">
          {isHot && (
            <Badge className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              Hot
            </Badge>
          )}
          {isOnSale && discountPercentage && (
            <Badge className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              -{discountPercentage}%
            </Badge>
          )}
          {isNew && (
            <Badge className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              New
            </Badge>
          )}
          {!inStock && (
            <Badge className="bg-gray-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              Sold Out
            </Badge>
          )}
        </div>

        {/* Action buttons - Nest style */}
        <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
          <Button
            size="sm"
            variant="secondary"
            className="w-10 h-10 p-0 bg-white/95 hover:bg-white shadow-lg rounded-full border-0 hover:scale-110 transition-all duration-200"
            onClick={handleAddToWishlist}
            title="Add to Wishlist"
          >
            <Heart className="w-4 h-4 text-gray-600 hover:text-red-500" />
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="w-10 h-10 p-0 bg-white/95 hover:bg-white shadow-lg rounded-full border-0 hover:scale-110 transition-all duration-200"
            onClick={handleCompare}
            title="Compare"
          >
            <GitCompare className="w-4 h-4 text-gray-600 hover:text-blue-500" />
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="w-10 h-10 p-0 bg-white/95 hover:bg-white shadow-lg rounded-full border-0 hover:scale-110 transition-all duration-200"
            onClick={(e) => {
              e.stopPropagation();
              setShowReviewForm(true);
            }}
            title="Write Review"
          >
            <MessageSquare className="w-4 h-4 text-gray-600 hover:text-green-500" />
          </Button>
        </div>
      </div>

      <CardContent className="p-4">
        {/* Category Link */}
        <div className="mb-2">
          <span className="text-xs text-green-600 font-semibold hover:text-green-700 cursor-pointer">
            {category}
          </span>
        </div>

        {/* Product Title */}
        <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-green-600 transition-colors duration-200 leading-tight">
          {name}
        </h3>

        {/* Rating */}
        <div className="flex items-center gap-1 mb-2">
          <div className="flex">
            {renderStars(rating)}
          </div>
          <span className="text-xs text-gray-500">
            ({reviewCount || 0})
          </span>
        </div>

        {/* Location & Farmer */}
        <div className="flex items-center gap-2 mb-2 text-muted-foreground">
          <MapPin className="w-3 h-3" />
          <span className="text-xs">{region}</span>
        </div>
        <p className="text-xs text-gray-500 mb-3">
          By <span className="text-green-600 font-medium hover:text-green-700 cursor-pointer">{farmer}</span>
        </p>

        {/* Price Section */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <span className="text-lg font-bold text-green-600">
              {price}
            </span>
            {originalPrice && originalPrice !== price && (
              <span className="text-sm text-gray-400 line-through">
                {originalPrice}
              </span>
            )}
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        {/* Add to Cart Button - Nest Style */}
        <Button
          className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2.5 rounded-lg transition-all duration-200 hover:shadow-lg disabled:bg-gray-300 disabled:cursor-not-allowed"
          disabled={!inStock || isLoading}
          onClick={inStock ? handleAddToCart : undefined}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : inStock ? (
            <>
              <ShoppingCart className="w-4 h-4 mr-2" />
              Add
            </>
          ) : (
            "Out of Stock"
          )}
        </Button>
      </CardFooter>

      {/* Review Form Modal */}
      {showReviewForm && id && (
        <ReviewForm
          productId={id}
          productName={name}
          onClose={() => setShowReviewForm(false)}
          onSuccess={() => {
            toast.success('Review submitted successfully!');
            setShowReviewForm(false);
          }}
        />
      )}
    </Card>
  );
};

export default ProductCard;


