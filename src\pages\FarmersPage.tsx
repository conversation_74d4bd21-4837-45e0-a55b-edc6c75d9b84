import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import {
  MapPin,
  Star,
  Users,
  Leaf,
  Award,
  Phone,
  Mail
} from 'lucide-react';

// API function to fetch farmers
const API_BASE = 'http://localhost:3001/api';

const fetchFarmers = async () => {
  const response = await fetch(`${API_BASE}/users?role=farmer`);
  if (!response.ok) throw new Error('Failed to fetch farmers');
  return response.json();
};

const FarmersPage: React.FC = () => {
  const { data: farmers = [], isLoading } = useQuery({
    queryKey: ['farmers'],
    queryFn: fetchFarmers,
    staleTime: 5 * 60 * 1000,
  });

  // Mock farmer data with additional details
  const farmersWithDetails = farmers.map((farmer: any, index: number) => ({
    ...farmer,
    location: ['Douala, Littoral', 'Yaoundé, Centre', 'Bamenda, Northwest', 'Bafoussam, West'][index % 4],
    specialties: [
      ['Cocoa', 'Coffee', 'Plantains'],
      ['Vegetables', 'Fruits', 'Herbs'],
      ['Grains', 'Legumes', 'Spices'],
      ['Root Vegetables', 'Leafy Greens']
    ][index % 4],
    rating: (4.2 + Math.random() * 0.8).toFixed(1),
    experience: Math.floor(Math.random() * 15) + 5,
    image: `https://images.unsplash.com/photo-${1500000000000 + index * 1000000}?w=300&h=300&fit=crop&q=80`
  }));

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Farmers</h1>
          <p className="text-xl text-green-100 max-w-2xl">
            Meet the dedicated farmers across Cameroon who bring you the freshest, highest-quality produce.
          </p>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-green-600">{farmers.length}+</div>
              <div className="text-gray-600">Verified Farmers</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600">10</div>
              <div className="text-gray-600">Regions Covered</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600">50+</div>
              <div className="text-gray-600">Product Categories</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600">98%</div>
              <div className="text-gray-600">Customer Satisfaction</div>
            </div>
          </div>
        </div>
      </div>

      {/* Farmers Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gray-200 h-64 rounded-lg mb-4"></div>
                <div className="bg-gray-200 h-4 rounded mb-2"></div>
                <div className="bg-gray-200 h-4 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {farmersWithDetails.map((farmer: any) => (
              <Card key={farmer.id} className="group hover:scale-105 transition-all duration-300 hover:shadow-xl border-0 overflow-hidden">
                <div className="relative h-48 bg-gradient-to-br from-green-400 to-green-600">
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold">{farmer.username}</h3>
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span className="text-sm">{farmer.location}</span>
                    </div>
                  </div>
                  <Badge className="absolute top-4 right-4 bg-yellow-500 text-white">
                    <Star className="w-3 h-3 mr-1" />
                    {farmer.rating}
                  </Badge>
                </div>
                
                <CardContent className="p-6">
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">Experience</span>
                      <span className="font-semibold">{farmer.experience} years</span>
                    </div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">Email</span>
                      <span className="text-sm text-blue-600">{farmer.email}</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold mb-2">Specialties</h4>
                    <div className="flex flex-wrap gap-2">
                      {farmer.specialties.map((specialty: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Mail className="w-4 h-4 mr-2" />
                      Contact
                    </Button>
                    <Button size="sm" className="flex-1 bg-green-600 hover:bg-green-700">
                      <Leaf className="w-4 h-4 mr-2" />
                      View Products
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default FarmersPage;
