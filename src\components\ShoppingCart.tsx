
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ShoppingCart, Plus, Minus, Trash2, MapPin } from "lucide-react";

interface CartItem {
  id: string;
  name: string;
  image: string;
  price: number;
  quantity: number;
  farmer: string;
  region: string;
  unit: string;
}

interface ShoppingCartProps {
  items: CartItem[];
  onUpdateQuantity: (id: string, quantity: number) => void;
  onRemoveItem: (id: string) => void;
}

const ShoppingCartComponent = ({ items, onUpdateQuantity, onRemoveItem }: ShoppingCartProps) => {
  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = subtotal > 10000 ? 0 : 1500; // Free delivery above 10,000 FCFA
  const total = subtotal + deliveryFee;

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="w-5 h-5" />
          Shopping Cart ({items.length})
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {items.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <ShoppingCart className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>Your cart is empty</p>
          </div>
        ) : (
          <>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {items.map((item) => (
                <div key={item.id} className="flex items-center gap-3 p-2 border rounded-lg">
                  <img 
                    src={item.image} 
                    alt={item.name}
                    className="w-12 h-12 rounded object-cover"
                  />
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm truncate">{item.name}</h4>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <MapPin className="w-3 h-3" />
                      <span>{item.region}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">by {item.farmer}</p>
                    <p className="font-semibold text-sm">{item.price} FCFA/{item.unit}</p>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button 
                      size="icon" 
                      variant="outline" 
                      className="h-6 w-6"
                      onClick={() => onUpdateQuantity(item.id, Math.max(0, item.quantity - 1))}
                    >
                      <Minus className="w-3 h-3" />
                    </Button>
                    <span className="w-8 text-center text-sm">{item.quantity}</span>
                    <Button 
                      size="icon" 
                      variant="outline" 
                      className="h-6 w-6"
                      onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                    >
                      <Plus className="w-3 h-3" />
                    </Button>
                    <Button 
                      size="icon" 
                      variant="destructive" 
                      className="h-6 w-6 ml-2"
                      onClick={() => onRemoveItem(item.id)}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{subtotal.toLocaleString()} FCFA</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Delivery:</span>
                <span className={deliveryFee === 0 ? "text-green-600" : ""}>
                  {deliveryFee === 0 ? "FREE" : `${deliveryFee.toLocaleString()} FCFA`}
                </span>
              </div>
              {deliveryFee === 0 && (
                <Badge variant="secondary" className="w-full justify-center text-xs">
                  Free delivery on orders above 10,000 FCFA
                </Badge>
              )}
              <Separator />
              <div className="flex justify-between font-semibold">
                <span>Total:</span>
                <span>{total.toLocaleString()} FCFA</span>
              </div>
            </div>
            
            <Button className="w-full" size="lg">
              Proceed to Checkout
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ShoppingCartComponent;
