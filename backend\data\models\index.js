const { getSequelize } = require('../../config/database');

// Import all models
const User = require('./user');
const Category = require('./category');
const Product = require('./product');
const Cart = require('./cart');
const CartItem = require('./cartItem');
const Wishlist = require('./wishlist');
const Order = require('./order');
const OrderItem = require('./orderItem');
const Payment = require('./payment');
const Delivery = require('./delivery');
const Review = require('./review');
const Notification = require('./notification');
const Farmer = require('./farmer');

// Define associations
const defineAssociations = () => {
  // User associations
  User.hasMany(Cart, { foreignKey: 'userId', onDelete: 'CASCADE' });
  User.hasMany(Wishlist, { foreignKey: 'userId', onDelete: 'CASCADE' });
  User.hasMany(Order, { foreignKey: 'userId', onDelete: 'CASCADE' });
  User.hasMany(Review, { foreignKey: 'userId', onDelete: 'CASCADE' });
  User.hasMany(Notification, { foreignKey: 'userId', onDelete: 'CASCADE' });
  User.hasOne(Farmer, { foreignKey: 'userId', onDelete: 'CASCADE' });

  // Farmer associations
  Farmer.belongsTo(User, { foreignKey: 'userId' });
  Farmer.hasMany(Product, { foreignKey: 'farmerId', onDelete: 'CASCADE' });

  // Category associations
  Category.hasMany(Product, { foreignKey: 'categoryId', onDelete: 'RESTRICT' });
  Category.belongsTo(Category, { as: 'parent', foreignKey: 'parentId' });
  Category.hasMany(Category, { as: 'children', foreignKey: 'parentId' });

  // Product associations
  Product.belongsTo(Farmer, { foreignKey: 'farmerId' });
  Product.belongsTo(Category, { foreignKey: 'categoryId' });
  Product.hasMany(CartItem, { foreignKey: 'productId', onDelete: 'CASCADE' });
  Product.hasMany(Wishlist, { foreignKey: 'productId', onDelete: 'CASCADE' });
  Product.hasMany(OrderItem, { foreignKey: 'productId', onDelete: 'RESTRICT' });
  Product.hasMany(Review, { foreignKey: 'productId', onDelete: 'CASCADE' });

  // Cart associations
  Cart.belongsTo(User, { foreignKey: 'userId' });
  Cart.hasMany(CartItem, { foreignKey: 'cartId', onDelete: 'CASCADE' });

  // CartItem associations
  CartItem.belongsTo(Cart, { foreignKey: 'cartId' });
  CartItem.belongsTo(Product, { foreignKey: 'productId' });

  // Wishlist associations
  Wishlist.belongsTo(User, { foreignKey: 'userId' });
  Wishlist.belongsTo(Product, { foreignKey: 'productId' });

  // Order associations
  Order.belongsTo(User, { foreignKey: 'userId', as: 'customer' });
  Order.hasMany(OrderItem, { foreignKey: 'orderId', onDelete: 'CASCADE' });
  Order.hasOne(Payment, { foreignKey: 'orderId', onDelete: 'CASCADE' });
  Order.hasOne(Delivery, { foreignKey: 'orderId', onDelete: 'CASCADE' });

  // OrderItem associations
  OrderItem.belongsTo(Order, { foreignKey: 'orderId' });
  OrderItem.belongsTo(Product, { foreignKey: 'productId' });

  // Payment associations
  Payment.belongsTo(Order, { foreignKey: 'orderId' });

  // Delivery associations
  Delivery.belongsTo(Order, { foreignKey: 'orderId' });
  Delivery.belongsTo(User, { as: 'deliveryAgent', foreignKey: 'deliveryAgentId' });

  // Review associations
  Review.belongsTo(User, { foreignKey: 'userId' });
  Review.belongsTo(Product, { foreignKey: 'productId' });
  Review.belongsTo(Order, { foreignKey: 'orderId' });

  // Notification associations
  Notification.belongsTo(User, { foreignKey: 'userId' });

  console.log('✅ Model associations defined');
};

// Seed initial data
const seedInitialData = async () => {
  try {
    // Create core crop categories for Cameroon (skip duplicates)
    const categories = await Category.bulkCreate([
      // Main food crops
      { name: 'Cereals & Grains', description: 'Maize, rice, millet, sorghum, wheat' },
      { name: 'Root Crops & Tubers', description: 'Cassava, yam, sweet potato, Irish potato, cocoyam, plantain' },
      { name: 'Fruits', description: 'Banana, pineapple, mango, papaya, avocado, citrus fruits, bush mango' },

      // Vegetables (combined all vegetables into one category)
      { name: 'Vegetables', description: 'Tomato, pepper, onion, garlic, carrot, okra, cucumber, spinach, lettuce, cabbage' },

      // Legumes and proteins
      { name: 'Legumes & Beans', description: 'Groundnuts, cowpea, beans, soybeans, bambara groundnut' },

      // Cash crops
      { name: 'Cash Crops', description: 'Cocoa, coffee, cotton, palm oil' },

      // Seasonings and herbs
      { name: 'Spices & Herbs', description: 'Ginger, pepper, basil, lemongrass, mint, garlic' }
    ], {
      ignoreDuplicates: true
    });

    console.log('✅ Initial categories created');

    // Create sample farmer and products (skip if already exists)
    let sampleUser = await User.findOne({ where: { email: '<EMAIL>' } });

    if (!sampleUser) {
      sampleUser = await User.create({
        firstName: 'Sample',
        lastName: 'Farmer',
        email: '<EMAIL>',
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', // password123
        role: 'FARMER',
        status: 'ACTIVE',
        emailVerified: true
      });
    }

    let sampleFarmer = await Farmer.findOne({ where: { userId: sampleUser.id } });

    if (!sampleFarmer) {
      sampleFarmer = await Farmer.create({
        userId: sampleUser.id,
        farmName: 'Green Valley Farm',
        farmLocation: 'Douala, Cameroon',
        verified: true,
        rating: 4.5
      });
    }

    // Create simple sample products for core categories
    const products = [
      // Cereals & Grains (categories[0])
      {
        name: 'Yellow Maize (Corn)',
        description: 'High-quality yellow corn, locally grown and dried',
        price: 400,
        unit: 'kg',
        stock: 500,
        farmerId: sampleFarmer.id,
        categoryId: categories[0].id,
        images: ['https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Premium White Rice',
        description: 'High quality lowland white rice, locally grown',
        price: 800,
        unit: 'kg',
        stock: 200,
        farmerId: sampleFarmer.id,
        categoryId: categories[0].id,
        images: ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Root Crops & Tubers (categories[1])
      {
        name: 'Fresh Cassava',
        description: 'Sweet cassava variety, freshly harvested tubers',
        price: 300,
        unit: 'kg',
        stock: 300,
        farmerId: sampleFarmer.id,
        categoryId: categories[1].id,
        images: ['https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Sweet Yam',
        description: 'Fresh yam tubers, excellent for cooking',
        price: 450,
        unit: 'kg',
        stock: 200,
        farmerId: sampleFarmer.id,
        categoryId: categories[1].id,
        images: ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Root Crops & Tubers
      {
        name: 'Fresh Cassava',
        description: 'Sweet cassava variety, freshly harvested tubers',
        price: 300,
        unit: 'kg',
        stock: 300,
        farmerId: sampleFarmer.id,
        categoryId: categories[1].id,
        images: ['https://images.unsplash.com/photo-1583258292688-d0213dc5a3a8?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'White Yam',
        description: 'Premium white yam tubers, perfect for pounding',
        price: 700,
        unit: 'kg',
        stock: 200,
        farmerId: sampleFarmer.id,
        categoryId: categories[1].id,
        images: ['https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Orange Sweet Potato',
        description: 'Vitamin A rich orange sweet potatoes',
        price: 400,
        unit: 'kg',
        stock: 250,
        farmerId: sampleFarmer.id,
        categoryId: categories[1].id,
        images: ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Irish Potato',
        description: 'Fresh Irish potatoes from highland farms',
        price: 600,
        unit: 'kg',
        stock: 180,
        farmerId: sampleFarmer.id,
        categoryId: categories[1].id,
        images: ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Cooking Plantain',
        description: 'Green cooking plantains, perfect for frying',
        price: 350,
        unit: 'bunch',
        stock: 100,
        farmerId: sampleFarmer.id,
        categoryId: categories[1].id,
        images: ['https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Major Cash Crops
      {
        name: 'Premium Cocoa Beans',
        description: 'Fine flavor cocoa beans, sun-dried and fermented',
        price: 2500,
        unit: 'kg',
        stock: 100,
        farmerId: sampleFarmer.id,
        categoryId: categories[2].id,
        images: ['https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Arabica Coffee Beans',
        description: 'High-altitude Arabica coffee beans, specialty grade',
        price: 3000,
        unit: 'kg',
        stock: 80,
        farmerId: sampleFarmer.id,
        categoryId: categories[2].id,
        images: ['https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Crude Palm Oil',
        description: 'Fresh crude palm oil, locally processed',
        price: 1800,
        unit: 'liter',
        stock: 150,
        farmerId: sampleFarmer.id,
        categoryId: categories[2].id,
        images: ['https://images.unsplash.com/photo-1605034313761-73ea4a0cfbf3?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Tropical Fruits
      {
        name: 'Sweet Bananas',
        description: 'Ripe yellow bananas, perfect for eating',
        price: 300,
        unit: 'bunch',
        stock: 150,
        farmerId: sampleFarmer.id,
        categoryId: categories[3].id,
        images: ['https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Fresh Pineapples',
        description: 'Sweet smooth cayenne pineapples, locally grown',
        price: 800,
        unit: 'piece',
        stock: 80,
        farmerId: sampleFarmer.id,
        categoryId: categories[3].id,
        images: ['https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Ripe Mangoes',
        description: 'Juicy local mango variety, tree-ripened',
        price: 600,
        unit: 'kg',
        stock: 120,
        farmerId: sampleFarmer.id,
        categoryId: categories[3].id,
        images: ['https://images.unsplash.com/photo-1553279768-865429fa0078?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Fresh Papayas',
        description: 'Sweet orange papayas, rich in vitamins',
        price: 400,
        unit: 'kg',
        stock: 100,
        farmerId: sampleFarmer.id,
        categoryId: categories[3].id,
        images: ['https://images.unsplash.com/photo-1617112848923-cc2234396a8d?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Hass Avocados',
        description: 'Premium Hass avocados, creamy and nutritious',
        price: 1000,
        unit: 'kg',
        stock: 60,
        farmerId: sampleFarmer.id,
        categoryId: categories[3].id,
        images: ['https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Sweet Oranges',
        description: 'Juicy sweet oranges, high in vitamin C',
        price: 500,
        unit: 'kg',
        stock: 200,
        farmerId: sampleFarmer.id,
        categoryId: categories[3].id,
        images: ['https://images.unsplash.com/photo-1547514701-42782101795e?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Indigenous/Wild Fruits
      {
        name: 'Bush Mango (Irvingia)',
        description: 'Traditional bush mango seeds and fruit',
        price: 1500,
        unit: 'kg',
        stock: 40,
        farmerId: sampleFarmer.id,
        categoryId: categories[4].id,
        images: ['https://images.unsplash.com/photo-1553279768-865429fa0078?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'African Pear (Safou)',
        description: 'Nutritious African pear, butter fruit',
        price: 800,
        unit: 'kg',
        stock: 60,
        farmerId: sampleFarmer.id,
        categoryId: categories[4].id,
        images: ['https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Leafy Greens
      {
        name: 'Fresh Spinach',
        description: 'Tender green spinach leaves, locally grown',
        price: 400,
        unit: 'kg',
        stock: 80,
        farmerId: sampleFarmer.id,
        categoryId: categories[5].id,
        images: ['https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Bitter Leaf (Vernonia)',
        description: 'Traditional bitter leaf for soup and medicine',
        price: 300,
        unit: 'bundle',
        stock: 100,
        farmerId: sampleFarmer.id,
        categoryId: categories[5].id,
        images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Water Leaf',
        description: 'Fresh water leaf for traditional soups',
        price: 250,
        unit: 'bundle',
        stock: 120,
        farmerId: sampleFarmer.id,
        categoryId: categories[5].id,
        images: ['https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Pumpkin Leaves (Ugu)',
        description: 'Fluted pumpkin leaves, rich in nutrients',
        price: 350,
        unit: 'bundle',
        stock: 90,
        farmerId: sampleFarmer.id,
        categoryId: categories[5].id,
        images: ['https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Fruit Vegetables
      {
        name: 'Fresh Tomatoes',
        description: 'Organic red tomatoes, freshly harvested',
        price: 500,
        unit: 'kg',
        stock: 200,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id,
        images: ['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80'],
        isOrganic: true,
        status: 'ACTIVE'
      },
      {
        name: 'Hot Pepper (Scotch Bonnet)',
        description: 'Spicy scotch bonnet peppers, very hot',
        price: 1200,
        unit: 'kg',
        stock: 50,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id,
        images: ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Garden Egg (African Eggplant)',
        description: 'Traditional white garden eggs',
        price: 400,
        unit: 'kg',
        stock: 100,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id,
        images: ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Fresh Okra',
        description: 'Tender okra pods, perfect for soup',
        price: 600,
        unit: 'kg',
        stock: 80,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id,
        images: ['https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Other Vegetables
      {
        name: 'Red Onions',
        description: 'Fresh red bulb onions, locally grown',
        price: 700,
        unit: 'kg',
        stock: 150,
        farmerId: sampleFarmer.id,
        categoryId: categories[3].id, // Vegetables category
        images: ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Fresh Carrots',
        description: 'Orange carrots, sweet and crunchy',
        price: 800,
        unit: 'kg',
        stock: 100,
        farmerId: sampleFarmer.id,
        categoryId: categories[3].id, // Vegetables category
        images: ['https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Legumes & Pulses
      {
        name: 'Groundnuts (Peanuts)',
        description: 'Spanish variety groundnuts, freshly harvested',
        price: 900,
        unit: 'kg',
        stock: 200,
        farmerId: sampleFarmer.id,
        categoryId: categories[4].id, // Legumes & Beans category
        images: ['https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Black-eyed Peas (Cowpea)',
        description: 'Traditional cowpea variety, protein-rich',
        price: 800,
        unit: 'kg',
        stock: 150,
        farmerId: sampleFarmer.id,
        categoryId: categories[4].id, // Legumes & Beans category
        images: ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Soybeans',
        description: 'High-protein soybeans for oil and food',
        price: 1000,
        unit: 'kg',
        stock: 120,
        farmerId: sampleFarmer.id,
        categoryId: categories[4].id, // Legumes & Beans category
        images: ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Spices & Seasonings
      {
        name: 'Fresh Ginger',
        description: 'Yellow ginger variety, aromatic and spicy',
        price: 1500,
        unit: 'kg',
        stock: 80,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1599639832862-bd92c6e10b5b?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Turmeric Root',
        description: 'Fresh turmeric root, anti-inflammatory spice',
        price: 2000,
        unit: 'kg',
        stock: 60,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1599639832862-bd92c6e10b5b?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Cameroon Pepper',
        description: 'Traditional Piper guineense, aromatic spice',
        price: 3000,
        unit: 'kg',
        stock: 30,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Herbs & Aromatics
      {
        name: 'Fresh Basil',
        description: 'Sweet basil leaves, aromatic herb',
        price: 800,
        unit: 'bundle',
        stock: 50,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Lemongrass',
        description: 'Fresh lemongrass stalks for tea and cooking',
        price: 600,
        unit: 'bundle',
        stock: 70,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Medicinal & Aromatic Plants
      {
        name: 'Moringa Leaves',
        description: 'Fresh moringa leaves, superfood with high nutrients',
        price: 1000,
        unit: 'kg',
        stock: 60,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Aloe Vera',
        description: 'Fresh aloe vera leaves for medicinal use',
        price: 500,
        unit: 'piece',
        stock: 100,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },

      // Beekeeping Products
      {
        name: 'Pure Forest Honey',
        description: 'Raw forest honey, unprocessed and natural',
        price: 2500,
        unit: 'liter',
        stock: 40,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      },
      {
        name: 'Natural Beeswax',
        description: 'Pure beeswax for cosmetics and crafts',
        price: 3000,
        unit: 'kg',
        stock: 20,
        farmerId: sampleFarmer.id,
        categoryId: categories[6].id, // Spices & Herbs category
        images: ['https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400&h=300&fit=crop&q=80'],
        status: 'ACTIVE'
      }
    ];

    await Product.bulkCreate(products, { ignoreDuplicates: true });
    console.log('✅ Comprehensive products created with', products.length, 'items across', categories.length, 'categories');

  } catch (error) {
    console.error('❌ Error seeding initial data:', error);
  }
};

// Initialize all models
const initializeModels = async () => {
  try {
    const sequelize = getSequelize();
    defineAssociations();

    // Sync all models without force to preserve existing data
    await sequelize.sync({ force: false, alter: true });
    console.log('✅ All models synchronized with database');

    // Seed initial data
    await seedInitialData();

    return {
      User,
      Category,
      Product,
      Cart,
      CartItem,
      Wishlist,
      Order,
      OrderItem,
      Payment,
      Delivery,
      Review,
      Notification,
      Farmer,
      sequelize
    };
  } catch (error) {
    console.error('❌ Error initializing models:', error);
    throw error;
  }
};

module.exports = {
  User,
  Category,
  Product,
  Cart,
  CartItem,
  Wishlist,
  Order,
  OrderItem,
  Payment,
  Delivery,
  Review,
  Notification,
  Farmer,
  initializeModels
};