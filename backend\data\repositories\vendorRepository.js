const { Vendor, ProductListing } = require('../models');

class VendorRepository {
  async create(vendorData) {
    return await Vendor.create(vendorData);
  }

  async findByUserId(userId) {
    return await Vendor.findOne({ where: { UserId: userId } });
  }

  async update(vendorId, updateData) {
    const vendor = await Vendor.findByPk(vendorId);
    if (!vendor) throw new Error('Vendor not found');
    return await vendor.update(updateData);
  }

  async createProductListing(listingData) {
    return await ProductListing.create(listingData);
  }

  async findListingById(listingId, vendorId) {
    return await ProductListing.findOne({ where: { id: listingId, VendorId: vendorId } });
  }

  async updateListing(listingId, updateData) {
    const listing = await ProductListing.findByPk(listingId);
    if (!listing) throw new Error('Listing not found');
    return await listing.update(updateData);
  }

  async deleteListing(listingId) {
    const listing = await ProductListing.findByPk(listingId);
    if (!listing) throw new Error('Listing not found');
    await listing.destroy();
  }

  async getVendorListings(vendorId) {
    return await ProductListing.findAll({ where: { VendorId: vendorId } });
  }
}

module.exports = new VendorRepository();