// Debug user session and cart/wishlist
const mysql = require('mysql2/promise');

async function debugUserSession() {
  console.log('🔍 Debugging User Session and Cart/Wishlist...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    // Check users
    console.log('👥 Checking Users:');
    const [users] = await connection.execute('SELECT id, firstName, lastName, email, role FROM users ORDER BY createdAt DESC LIMIT 5');
    users.forEach(user => {
      console.log(`   - ${user.firstName} ${user.lastName} (${user.email}) - ${user.role}`);
    });
    
    // Check carts
    console.log('\n🛒 Checking Carts:');
    const [carts] = await connection.execute(`
      SELECT c.id, c.userId, u.firstName, u.lastName, 
             COUNT(ci.id) as itemCount,
             SUM(ci.quantity * ci.price) as total
      FROM carts c
      LEFT JOIN users u ON c.userId = u.id
      LEFT JOIN cart_items ci ON c.id = ci.cartId
      GROUP BY c.id, c.userId, u.firstName, u.lastName
      ORDER BY c.createdAt DESC
    `);
    
    if (carts.length > 0) {
      carts.forEach(cart => {
        console.log(`   - Cart ${cart.id}: ${cart.firstName} ${cart.lastName} (${cart.itemCount} items, ${cart.total || 0} FCFA)`);
      });
    } else {
      console.log('   No carts found');
    }
    
    // Check wishlists
    console.log('\n❤️ Checking Wishlists:');
    const [wishlists] = await connection.execute(`
      SELECT w.id, w.userId, u.firstName, u.lastName, p.name as productName
      FROM wishlists w
      LEFT JOIN users u ON w.userId = u.id
      LEFT JOIN products p ON w.productId = p.id
      ORDER BY w.createdAt DESC
    `);
    
    if (wishlists.length > 0) {
      wishlists.forEach(wishlist => {
        console.log(`   - Wishlist ${wishlist.id}: ${wishlist.firstName} ${wishlist.lastName} - ${wishlist.productName}`);
      });
    } else {
      console.log('   No wishlist items found');
    }
    
    // Check cart items
    console.log('\n📦 Checking Cart Items:');
    const [cartItems] = await connection.execute(`
      SELECT ci.id, ci.cartId, ci.productId, ci.quantity, ci.price, p.name as productName
      FROM cart_items ci
      LEFT JOIN products p ON ci.productId = p.id
      ORDER BY ci.createdAt DESC
      LIMIT 10
    `);
    
    if (cartItems.length > 0) {
      cartItems.forEach(item => {
        console.log(`   - Item ${item.id}: ${item.productName} (${item.quantity}x ${item.price} FCFA) in cart ${item.cartId}`);
      });
    } else {
      console.log('   No cart items found');
    }
    
    // Check products
    console.log('\n📦 Sample Products:');
    const [products] = await connection.execute('SELECT id, name, price, stock FROM products LIMIT 3');
    products.forEach(product => {
      console.log(`   - ${product.name} (ID: ${product.id}) - ${product.price} FCFA (Stock: ${product.stock})`);
    });
    
    await connection.end();
    console.log('\n✅ Debug completed!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugUserSession();
