
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Phone, Radio, MessageSquare, MapPin, Users } from "lucide-react";

const OfflineAccess = () => {
  const offlineServices = [
    {
      title: "SMS Ordering System",
      description: "Order products via SMS using simple codes",
      code: "Send: ORDER COCOA 5KG to 237XXXX",
      icon: <MessageSquare className="w-5 h-5" />
    },
    {
      title: "USSD Service",
      description: "Dial *XXX# for menu-based ordering",
      code: "*789*1*2# - Browse Vegetables",
      icon: <Phone className="w-5 h-5" />
    },
    {
      title: "Radio Announcements",
      description: "Daily price updates on local radio",
      code: "Tune to 95.5 FM at 6 PM daily",
      icon: <Radio className="w-5 h-5" />
    },
    {
      title: "Community Agents",
      description: "Local representatives in rural areas",
      code: "Find your nearest agent",
      icon: <Users className="w-5 h-5" />
    }
  ];

  return (
    <Card className="enhanced-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Radio className="w-5 h-5 text-green-600" />
          Offline Access Solutions
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Connecting rural communities without internet access
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {offlineServices.map((service, index) => (
          <div key={index} className="service-card">
            <div className="flex items-start gap-3">
              <div className="bg-green-100 p-2 rounded-lg">
                {service.icon}
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-green-800">{service.title}</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  {service.description}
                </p>
                <div className="bg-white/50 p-2 rounded text-sm font-mono">
                  {service.code}
                </div>
              </div>
            </div>
          </div>
        ))}

        <div className="border-t pt-4">
          <h4 className="font-semibold mb-3">Community Network Coverage</h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <MapPin className="w-5 h-5 mx-auto mb-1 text-green-600" />
              <div className="font-semibold">150+</div>
              <div className="text-xs text-muted-foreground">Rural Agents</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <Phone className="w-5 h-5 mx-auto mb-1 text-blue-600" />
              <div className="font-semibold">98%</div>
              <div className="text-xs text-muted-foreground">SMS Coverage</div>
            </div>
          </div>
        </div>

        <Button className="w-full gradient-agricultural text-white">
          Find Local Agent
        </Button>
      </CardContent>
    </Card>
  );
};

export default OfflineAccess;
