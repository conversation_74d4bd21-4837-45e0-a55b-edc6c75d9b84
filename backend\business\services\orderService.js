const { Order, OrderItem, Product, ProductListing, Vendor } = require('../../data/models');
const orderRepository = require('../../data/repositories/orderRepository');

class OrderService {
  async createOrder(userId, orderData) {
    const order = await Order.create({ UserId: userId, status: 'pending' });
    let totalAmount = 0;
    let totalCommission = 0;

    for (const item of orderData.items) {
      const listing = await ProductListing.findByPk(item.productListingId);
      if (!listing) throw new Error(`Product listing not found: ${item.productListingId}`);
      if (listing.quantity < item.quantity) throw new Error(`Insufficient quantity for product listing: ${item.productListingId}`);
      
      const itemTotal = listing.price * item.quantity;
      const itemCommission = itemTotal * 0.10; // 10% commission
      
      await OrderItem.create({
        OrderId: order.id,
        ProductListingId: listing.id,
        quantity: item.quantity,
        price: listing.price,
        commission: itemCommission
      });
      
      totalAmount += itemTotal;
      totalCommission += itemCommission;
      
      // Update inventory
      await listing.update({ quantity: listing.quantity - item.quantity });
    }
    
    await order.update({ totalAmount, totalCommission });
    return this.getOrderById(order.id);
  }

  async getOrderById(id) {
    return await orderRepository.findById(id);
  }

  async updateOrderStatus(id, status) {
    return await orderRepository.update(id, { status });
  }

  async getUserOrders(userId) {
    return await Order.findAll({
      where: { userId },
      include: [{ model: OrderItem, include: [Product] }]
    });
  }

  async cancelOrder(id) {
    const order = await this.getOrderById(id);
    if (order.status !== 'pending') {
      throw new Error('Only pending orders can be cancelled');
    }
    return await this.updateOrderStatus(id, 'cancelled');
  }
}

module.exports = new OrderService();