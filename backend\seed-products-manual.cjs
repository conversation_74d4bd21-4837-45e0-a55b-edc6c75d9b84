// Manual product seeding
const { User, Farmer, Product, Category } = require('./data/models');
const bcrypt = require('bcryptjs');

async function seedProductsManual() {
  console.log('🌱 Manual Product Seeding...\n');
  
  try {
    // Step 1: Create sample farmer user
    console.log('1. Creating sample farmer user...');
    
    let sampleUser = await User.findOne({ where: { email: '<EMAIL>' } });
    
    if (!sampleUser) {
      const hashedPassword = await bcrypt.hash('password123', 12);
      sampleUser = await User.create({
        firstName: 'Sample',
        lastName: 'Farmer',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'FARMER',
        status: 'ACTIVE',
        emailVerified: true
      });
      console.log('   ✅ Sample user created');
    } else {
      console.log('   ✅ Sample user already exists');
    }
    
    // Step 2: Create farmer profile
    console.log('2. Creating farmer profile...');
    
    let sampleFarmer = await Farmer.findOne({ where: { userId: sampleUser.id } });
    
    if (!sampleFarmer) {
      sampleFarmer = await Farmer.create({
        userId: sampleUser.id,
        farmName: 'Green Valley Farm',
        farmLocation: 'Douala, Cameroon',
        verified: true,
        rating: 4.5
      });
      console.log('   ✅ Farmer profile created');
    } else {
      console.log('   ✅ Farmer profile already exists');
    }
    
    // Step 3: Get categories
    console.log('3. Getting categories...');
    const categories = await Category.findAll();
    console.log(`   ✅ Found ${categories.length} categories`);
    
    if (categories.length === 0) {
      console.log('   ❌ No categories found - run seeding first');
      return;
    }
    
    // Step 4: Create sample products
    console.log('4. Creating sample products...');
    
    const sampleProducts = [
      {
        name: 'Fresh Tomatoes',
        description: 'Organic red tomatoes, freshly harvested',
        price: 500,
        unit: 'kg',
        stock: 200,
        categoryName: 'Fruit Vegetables',
        isOrganic: true
      },
      {
        name: 'Yellow Maize (Corn)',
        description: 'High-quality yellow corn, locally grown and dried',
        price: 400,
        unit: 'kg',
        stock: 500,
        categoryName: 'Cereals & Grains'
      },
      {
        name: 'Fresh Cassava',
        description: 'Sweet cassava variety, freshly harvested tubers',
        price: 300,
        unit: 'kg',
        stock: 300,
        categoryName: 'Root Crops & Tubers'
      },
      {
        name: 'Sweet Bananas',
        description: 'Ripe yellow bananas, perfect for eating',
        price: 300,
        unit: 'bunch',
        stock: 150,
        categoryName: 'Tropical Fruits'
      },
      {
        name: 'Fresh Spinach',
        description: 'Tender green spinach leaves, locally grown',
        price: 400,
        unit: 'kg',
        stock: 80,
        categoryName: 'Leafy Greens'
      },
      {
        name: 'Groundnuts (Peanuts)',
        description: 'Spanish variety groundnuts, freshly harvested',
        price: 900,
        unit: 'kg',
        stock: 200,
        categoryName: 'Legumes & Pulses'
      },
      {
        name: 'Fresh Ginger',
        description: 'Yellow ginger variety, aromatic and spicy',
        price: 1500,
        unit: 'kg',
        stock: 80,
        categoryName: 'Spices & Seasonings'
      },
      {
        name: 'Red Onions',
        description: 'Fresh red bulb onions, locally grown',
        price: 700,
        unit: 'kg',
        stock: 150,
        categoryName: 'Other Vegetables'
      }
    ];
    
    let createdCount = 0;
    
    for (const productData of sampleProducts) {
      // Find category by name
      const category = categories.find(cat => cat.name === productData.categoryName);
      
      if (!category) {
        console.log(`   ⚠️ Category "${productData.categoryName}" not found, skipping ${productData.name}`);
        continue;
      }
      
      // Check if product already exists
      const existingProduct = await Product.findOne({
        where: { name: productData.name, farmerId: sampleFarmer.id }
      });
      
      if (!existingProduct) {
        await Product.create({
          name: productData.name,
          description: productData.description,
          price: productData.price,
          unit: productData.unit,
          stock: productData.stock,
          farmerId: sampleFarmer.id,
          categoryId: category.id,
          status: 'ACTIVE',
          isOrganic: productData.isOrganic || false,
          isFairTrade: false,
          rating: 0,
          totalReviews: 0,
          totalSold: 0,
          images: ['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80']
        });
        createdCount++;
        console.log(`   ✅ Created: ${productData.name}`);
      } else {
        console.log(`   ⚠️ Already exists: ${productData.name}`);
      }
    }
    
    console.log(`\n🎉 Manual seeding completed!`);
    console.log(`   Created ${createdCount} new products`);
    console.log(`   Total products now: ${await Product.count()}`);
    
  } catch (error) {
    console.error('❌ Manual seeding failed:', error.message);
    console.error(error);
  }
  
  process.exit(0);
}

seedProductsManual();
