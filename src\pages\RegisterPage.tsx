import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Leaf, User, Mail, Lock, Eye, EyeOff, MapPin, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import authService from '@/services/authService';

const RegisterPage: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: '',
    farmName: '',
    farmLocation: ''
  });
  const navigate = useNavigate();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password || !formData.role) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.role === 'FARMER' && (!formData.farmName || !formData.farmLocation)) {
      toast.error('Farm name and location are required for farmers');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match!');
      return;
    }

    if (formData.password.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    setIsLoading(true);

    try {
      const response = await authService.register({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        password: formData.password,
        role: formData.role as 'CUSTOMER' | 'FARMER',
        farmName: formData.farmName,
        farmLocation: formData.farmLocation
      });

      if (response.success) {
        toast.success(`Welcome to Agri Connect, ${response.data.user.firstName}!`);

        // Trigger auth change event for navbar
        window.dispatchEvent(new Event('authChange'));

        // Redirect to appropriate dashboard based on user role
        const user = response.data.user;
        switch (user.role) {
          case 'ADMIN':
            navigate('/dashboard/admin');
            break;
          case 'FARMER':
            navigate('/dashboard/farmer');
            break;
          case 'CUSTOMER':
          default:
            navigate('/');
            break;
        }
      } else {
        toast.error(response.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-white to-emerald-50"></div>
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2316a34a' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex">
        {/* Left Side - Branding */}
        <div className="hidden lg:flex lg:w-2/5 bg-gradient-to-br from-green-600 to-emerald-700 p-12 flex-col justify-center">
          <div className="max-w-md">
            <div className="flex items-center mb-8">
              <Leaf className="w-12 h-12 text-white mr-4" />
              <h1 className="text-3xl font-bold text-white">Agri Connect</h1>
            </div>
            <h2 className="text-4xl font-bold text-white mb-6 leading-tight">
              Join our farming community
            </h2>
            <p className="text-green-100 text-lg mb-8">
              Whether you're a farmer looking to sell your produce or a customer seeking fresh, local products, we've got you covered.
            </p>
            <div className="space-y-4">
              <div className="flex items-center text-green-100">
                <div className="w-2 h-2 bg-green-300 rounded-full mr-3"></div>
                <span>Connect directly with local farmers</span>
              </div>
              <div className="flex items-center text-green-100">
                <div className="w-2 h-2 bg-green-300 rounded-full mr-3"></div>
                <span>Access to fresh, organic produce</span>
              </div>
              <div className="flex items-center text-green-100">
                <div className="w-2 h-2 bg-green-300 rounded-full mr-3"></div>
                <span>Support sustainable agriculture</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Register Form */}
        <div className="w-full lg:w-3/5 flex items-center justify-center p-8">
          <Card className="w-full max-w-lg border-0 shadow-xl bg-white/95 backdrop-blur-sm">
            <CardHeader className="text-center pb-6">
              <div className="flex justify-center mb-4 lg:hidden">
                <Leaf className="w-10 h-10 text-green-600" />
              </div>
              <CardTitle className="text-2xl font-bold text-gray-900">Create your account</CardTitle>
              <p className="text-gray-600 mt-2">Join Agri Connect and start your journey</p>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName" className="text-gray-700 font-medium">First Name</Label>
                    <div className="relative mt-2">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <Input
                        id="firstName"
                        name="firstName"
                        type="text"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        placeholder="First name"
                        required
                        className="pl-10 h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="lastName" className="text-gray-700 font-medium">Last Name</Label>
                    <div className="relative mt-2">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <Input
                        id="lastName"
                        name="lastName"
                        type="text"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        placeholder="Last name"
                        required
                        className="pl-10 h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="email" className="text-gray-700 font-medium">Email Address</Label>
                  <div className="relative mt-2">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      required
                      className="pl-10 h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="phone" className="text-gray-700 font-medium">Phone Number (Optional)</Label>
                  <div className="relative mt-2">
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="+237 6XX XXX XXX"
                      className="h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="role" className="text-gray-700 font-medium">I am a...</Label>
                  <div className="mt-2">
                    <Select onValueChange={(value) => handleSelectChange('role', value)}>
                      <SelectTrigger className="h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white">
                        <SelectValue placeholder="Select your role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CUSTOMER">Customer (Buy products)</SelectItem>
                        <SelectItem value="FARMER">Farmer (Sell products)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Farmer-specific fields */}
                {formData.role === 'FARMER' && (
                  <>
                    <div>
                      <Label htmlFor="farmName" className="text-gray-700 font-medium">Farm Name</Label>
                      <div className="relative mt-2">
                        <Input
                          id="farmName"
                          name="farmName"
                          type="text"
                          value={formData.farmName}
                          onChange={handleInputChange}
                          placeholder="Enter your farm name"
                          required
                          className="h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="farmLocation" className="text-gray-700 font-medium">Farm Location</Label>
                      <div className="relative mt-2">
                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <Input
                          id="farmLocation"
                          name="farmLocation"
                          type="text"
                          value={formData.farmLocation}
                          onChange={handleInputChange}
                          placeholder="City, Region"
                          required
                          className="pl-10 h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white"
                        />
                      </div>
                    </div>
                  </>
                )}



                <div>
                  <Label htmlFor="password" className="text-gray-700 font-medium">Password</Label>
                  <div className="relative mt-2">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Create a strong password"
                      required
                      className="pl-10 pr-12 h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="confirmPassword" className="text-gray-700 font-medium">Confirm Password</Label>
                  <div className="relative mt-2">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      placeholder="Confirm your password"
                      required
                      className="pl-10 pr-12 h-12 border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500 bg-white"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                <div className="flex items-start">
                  <input type="checkbox" required className="mt-1 rounded border-gray-300 text-green-600 focus:ring-green-500" />
                  <span className="ml-3 text-sm text-gray-600">
                    I agree to the{' '}
                    <Link to="/terms" className="text-green-600 hover:text-green-700 font-semibold">
                      Terms of Service
                    </Link>{' '}
                    and{' '}
                    <Link to="/privacy" className="text-green-600 hover:text-green-700 font-semibold">
                      Privacy Policy
                    </Link>
                  </span>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold h-12 rounded-lg transition-all duration-200 hover:shadow-lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    'Create Account'
                  )}
                </Button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-gray-600">
                  Already have an account?{' '}
                  <Link to="/login" className="text-green-600 hover:text-green-700 font-semibold">
                    Sign in here
                  </Link>
                </p>
              </div>

            <div className="mt-6 text-center">
              <Link to="/" className="text-sm text-gray-500 hover:text-gray-700">
                ← Back to Home
              </Link>
            </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
