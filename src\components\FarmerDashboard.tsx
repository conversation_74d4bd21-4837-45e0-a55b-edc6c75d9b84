import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { 
  BarChart, 
  LineChart, 
  ShoppingBag, 
  Package, 
  TrendingUp, 
  Star, 
  Calendar, 
  Truck 
} from 'lucide-react';
import { analyticsAPI } from '@/services/api';
import ProductCard from '@/components/ProductCard';
import OrderCard from '@/components/OrderCard';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface FarmerDashboardProps {
  farmerId: string;
}

const FarmerDashboard = ({ farmerId }: FarmerDashboardProps) => {
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  
  // Fetch farmer dashboard data
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery({
    queryKey: ['farmerDashboard', farmerId],
    queryFn: () => analyticsAPI.getFarmerDashboard(farmerId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Fetch sales data for the selected period
  const { data: salesData, isLoading: salesLoading } = useQuery({
    queryKey: ['farmerSales', farmerId, selectedPeriod],
    queryFn: () => analyticsAPI.getFarmerSalesData(farmerId, selectedPeriod),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Fetch product performance data
  const { data: productData, isLoading: productLoading } = useQuery({
    queryKey: ['farmerProducts', farmerId],
    queryFn: () => analyticsAPI.getFarmerProductAnalytics(farmerId),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
  
  if (dashboardLoading) {
    return <div className="p-8 text-center">Loading dashboard data...</div>;
  }
  
  const dashboard = dashboardData?.data || {
    totalSales: 0,
    productCount: 0,
    recentOrders: [],
    topProducts: []
  };
  
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Farmer Dashboard</h1>
      
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <ShoppingBag className="w-8 h-8 text-primary" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Total Sales</p>
                <p className="text-2xl font-bold">{dashboard.totalSales.toLocaleString()} FCFA</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Package className="w-8 h-8 text-orange-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Products</p>
                <p className="text-2xl font-bold">{dashboard.productCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Conversion Rate</p>
                <p className="text-2xl font-bold">{productData?.data?.conversionRate || '0'}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Star className="w-8 h-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Avg. Rating</p>
                <p className="text-2xl font-bold">{productData?.data?.averageRating || '0'}/5</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Tabs for different sections */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          {/* Sales Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="w-5 h-5" />
                Sales Overview
              </CardTitle>
              <div className="flex gap-2">
                <Button 
                  variant={selectedPeriod === '7d' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setSelectedPeriod('7d')}
                >
                  7 Days
                </Button>
                <Button 
                  variant={selectedPeriod === '30d' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setSelectedPeriod('30d')}
                >
                  30 Days
                </Button>
                <Button 
                  variant={selectedPeriod === '90d' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setSelectedPeriod('90d')}
                >
                  90 Days
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {salesLoading ? (
                <div className="h-80 flex items-center justify-center">
                  Loading sales data...
                </div>
              ) : (
                <div className="h-80">
                  {/* Sales chart would go here - using a placeholder */}
                  <div className="h-full flex items-center justify-center border rounded-md">
                    Sales chart visualization would render here
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Top Products */}
          <Card>
            <CardHeader>
              <CardTitle>Top Products</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboard.topProducts.map((product) => (
                  <div key={product.id} className="flex items-center gap-4 p-4 border rounded-lg">
                    <img 
                      src={product.images?.[0] || "/placeholder.svg"} 
                      alt={product.name}
                      className="w-16 h-16 object-cover rounded-md"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium">{product.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {product.price.toLocaleString()} FCFA/{product.unit}
                      </p>
                      <Progress value={product.salesPercentage || 0} className="h-2 mt-2" />
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{product.totalSold || 0} sold</p>
                      <p className="text-sm text-muted-foreground">
                        {product.totalRevenue?.toLocaleString() || 0} FCFA
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="products" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Your Products</h2>
            <Button>Add New Product</Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {productData?.data?.products?.map((product) => (
              <ProductCard
                key={product.id}
                id={product.id}
                name={product.name}
                image={product.images?.[0] || "/placeholder.svg"}
                price={`${product.price.toLocaleString()} FCFA/${product.unit}`}
                region={product.region || "Cameroon"}
                farmer="You"
                rating={product.rating || 4.5}
                category={product.category?.name || "General"}
                inStock={product.stock > 0}
                editable={true}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Recent Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboard.recentOrders.map((order) => (
                  <OrderCard
                    key={order.id}
                    id={order.id}
                    date={new Date(order.createdAt).toLocaleDateString()}
                    status={order.status}
                    total={order.totalAmount}
                    items={order.orderItems.length}
                    customerName={order.user?.name || "Customer"}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="w-5 h-5" />
                Delivery Schedule
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Delivery schedule would go here */}
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Today's Deliveries</h3>
                      <p className="text-sm text-muted-foreground">3 orders to be delivered</p>
                    </div>
                    <Badge>Scheduled</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Product Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                {/* Product performance chart would go here */}
                <div className="h-full flex items-center justify-center border rounded-md">
                  Product performance visualization would render here
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Customer Demographics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                {/* Customer demographics chart would go here */}
                <div className="h-full flex items-center justify-center border rounded-md">
                  Customer demographics visualization would render here
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FarmerDashboard;