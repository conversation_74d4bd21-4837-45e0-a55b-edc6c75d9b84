
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Menu, 
  Search, 
  ShoppingCart, 
  User, 
  Heart, 
  Package, 
  Bell,
  Leaf,
  Home,
  Users,
  Store
} from "lucide-react";
import { Input } from "@/components/ui/input";

interface NavigationProps {
  cartItemCount?: number;
  isAuthenticated?: boolean;
  userName?: string;
  userAvatar?: string;
}

const Navigation = ({ 
  cartItemCount = 0, 
  isAuthenticated = false, 
  userName = "Guest",
  userAvatar 
}: NavigationProps) => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const navigationItems = [
    { name: "Home", icon: Home, href: "/" },
    { name: "Products", icon: Store, href: "/products" },
    { name: "Farmers", icon: Users, href: "/farmers" },
    { name: "About", icon: Leaf, href: "/about" },
  ];

  return (
    <nav className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center gap-2">
            <Leaf className="w-8 h-8 text-primary" />
            <span className="font-bold text-xl text-primary">AgriConnect</span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <Button key={item.name} variant="ghost" className="flex items-center gap-2">
                <item.icon className="w-4 h-4" />
                {item.name}
              </Button>
            ))}
          </div>

          {/* Search Bar */}
          <div className="hidden lg:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input 
                placeholder="Search products, farmers..." 
                className="pl-10"
              />
            </div>
          </div>

          {/* Right Actions */}
          <div className="flex items-center gap-2">
            {/* Mobile Search */}
            <Button 
              variant="ghost" 
              size="icon" 
              className="lg:hidden"
              onClick={() => setIsSearchOpen(!isSearchOpen)}
            >
              <Search className="w-5 h-5" />
            </Button>

            {/* Notifications */}
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="w-5 h-5" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs">
                3
              </Badge>
            </Button>

            {/* Wishlist */}
            <Button variant="ghost" size="icon" className="relative">
              <Heart className="w-5 h-5" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs">
                5
              </Badge>
            </Button>

            {/* Shopping Cart */}
            <Button variant="ghost" size="icon" className="relative">
              <ShoppingCart className="w-5 h-5" />
              {cartItemCount > 0 && (
                <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs">
                  {cartItemCount}
                </Badge>
              )}
            </Button>

            {/* User Profile */}
            {isAuthenticated ? (
              <div className="flex items-center gap-2">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={userAvatar} alt={userName} />
                  <AvatarFallback>{userName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                </Avatar>
                <span className="hidden md:block text-sm font-medium">{userName}</span>
              </div>
            ) : (
              <Button size="sm">
                <User className="w-4 h-4 mr-2" />
                Sign In
              </Button>
            )}

            {/* Mobile Menu */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="w-5 h-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <div className="flex flex-col gap-4 mt-8">
                  {navigationItems.map((item) => (
                    <Button key={item.name} variant="ghost" className="justify-start gap-2">
                      <item.icon className="w-4 h-4" />
                      {item.name}
                    </Button>
                  ))}
                  <hr />
                  <Button variant="ghost" className="justify-start gap-2">
                    <Package className="w-4 h-4" />
                    My Orders
                  </Button>
                  <Button variant="ghost" className="justify-start gap-2">
                    <User className="w-4 h-4" />
                    Profile
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Mobile Search Bar */}
        {isSearchOpen && (
          <div className="lg:hidden py-4 border-t">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input 
                placeholder="Search products, farmers..." 
                className="pl-10"
              />
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
