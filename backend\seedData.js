const { initializeModels } = require('./data/models');

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    const { Category, Product, User } = await initializeModels();

    // Create categories
    const categories = [
      { name: 'Tropical Fruits', description: 'Fresh tropical fruits from Cameroon farms' },
      { name: 'Vegetables', description: 'Fresh vegetables and leafy greens' },
      { name: 'Grains & Cereals', description: 'Rice, corn, millet and other grains' },
      { name: 'Cocoa & Coffee', description: 'Premium cocoa beans and coffee' },
      { name: 'Spices & Herbs', description: 'Traditional spices and medicinal herbs' },
      { name: 'Root Vegetables', description: 'Cassava, yams, sweet potatoes' },
      { name: 'Legumes & Beans', description: 'Beans, groundnuts and legumes' },
      { name: 'Leafy Greens', description: 'Spinach, lettuce and green vegetables' },
      { name: 'Nuts & Seeds', description: 'Various nuts and seeds' },
      { name: 'Citrus Fruits', description: 'Oranges, lemons and citrus varieties' }
    ];

    console.log('📂 Creating categories...');
    const createdCategories = [];
    for (const categoryData of categories) {
      const [category] = await Category.findOrCreate({
        where: { name: categoryData.name },
        defaults: categoryData
      });
      createdCategories.push(category);
      console.log(`✅ Category: ${category.name}`);
    }

    // Create a default farmer user if none exists
    const [farmer] = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        username: 'Farm Connect',
        email: '<EMAIL>',
        password: '$2b$10$dummy.hash.for.seeding.purposes.only',
        role: 'farmer'
      }
    });

    // Create products
    const products = [
      // Tropical Fruits
      { name: 'Fresh Mangoes', description: 'Sweet and juicy mangoes from local farms', price: 1500, stock: 50, CategoryId: createdCategories[0].id, UserId: farmer.id },
      { name: 'Ripe Pineapples', description: 'Golden pineapples with sweet flavor', price: 2000, stock: 30, CategoryId: createdCategories[0].id, UserId: farmer.id },
      { name: 'Fresh Papayas', description: 'Organic papayas rich in vitamins', price: 1200, stock: 40, CategoryId: createdCategories[0].id, UserId: farmer.id },
      { name: 'Ripe Bananas', description: 'Sweet bananas perfect for eating', price: 800, stock: 100, CategoryId: createdCategories[0].id, UserId: farmer.id },
      { name: 'Fresh Avocados', description: 'Creamy avocados rich in healthy fats', price: 2500, stock: 25, CategoryId: createdCategories[0].id, UserId: farmer.id },
      { name: 'Passion Fruits', description: 'Aromatic passion fruits with intense flavor', price: 3000, stock: 20, CategoryId: createdCategories[0].id, UserId: farmer.id },

      // Vegetables
      { name: 'Fresh Tomatoes', description: 'Red ripe tomatoes for cooking', price: 1000, stock: 60, CategoryId: createdCategories[1].id, UserId: farmer.id },
      { name: 'Green Peppers', description: 'Fresh bell peppers', price: 1500, stock: 40, CategoryId: createdCategories[1].id, UserId: farmer.id },
      { name: 'Fresh Onions', description: 'White onions for cooking', price: 800, stock: 80, CategoryId: createdCategories[1].id, UserId: farmer.id },
      { name: 'Carrots', description: 'Orange carrots rich in beta-carotene', price: 1200, stock: 50, CategoryId: createdCategories[1].id, UserId: farmer.id },
      { name: 'Fresh Cucumbers', description: 'Crisp cucumbers for salads', price: 900, stock: 45, CategoryId: createdCategories[1].id, UserId: farmer.id },
      { name: 'Green Beans', description: 'Fresh green beans', price: 1800, stock: 35, CategoryId: createdCategories[1].id, UserId: farmer.id },

      // Grains & Cereals
      { name: 'White Rice', description: 'Premium quality white rice', price: 2500, stock: 100, CategoryId: createdCategories[2].id, UserId: farmer.id },
      { name: 'Yellow Corn', description: 'Dried yellow corn kernels', price: 1800, stock: 80, CategoryId: createdCategories[2].id, UserId: farmer.id },
      { name: 'Millet Grains', description: 'Nutritious millet for porridge', price: 2200, stock: 60, CategoryId: createdCategories[2].id, UserId: farmer.id },
      { name: 'Sorghum', description: 'Drought-resistant sorghum grains', price: 2000, stock: 50, CategoryId: createdCategories[2].id, UserId: farmer.id },

      // Cocoa & Coffee
      { name: 'Premium Cocoa Beans', description: 'High-quality cocoa beans from Cameroon', price: 5000, stock: 30, CategoryId: createdCategories[3].id, UserId: farmer.id },
      { name: 'Arabica Coffee Beans', description: 'Premium Arabica coffee beans', price: 4500, stock: 25, CategoryId: createdCategories[3].id, UserId: farmer.id },
      { name: 'Robusta Coffee Beans', description: 'Strong Robusta coffee beans', price: 3500, stock: 40, CategoryId: createdCategories[3].id, UserId: farmer.id },
      { name: 'Cocoa Powder', description: 'Pure cocoa powder for baking', price: 3000, stock: 20, CategoryId: createdCategories[3].id, UserId: farmer.id },

      // Spices & Herbs
      { name: 'Black Pepper', description: 'Freshly ground black pepper', price: 4000, stock: 15, CategoryId: createdCategories[4].id, UserId: farmer.id },
      { name: 'Ginger Root', description: 'Fresh ginger root for cooking', price: 2500, stock: 30, CategoryId: createdCategories[4].id, UserId: farmer.id },
      { name: 'Turmeric Powder', description: 'Golden turmeric powder', price: 3500, stock: 20, CategoryId: createdCategories[4].id, UserId: farmer.id },
      { name: 'Dried Chili Peppers', description: 'Hot chili peppers for spicing', price: 3000, stock: 25, CategoryId: createdCategories[4].id, UserId: farmer.id },

      // Root Vegetables
      { name: 'Fresh Cassava', description: 'Starchy cassava roots', price: 1500, stock: 70, CategoryId: createdCategories[5].id, UserId: farmer.id },
      { name: 'Sweet Potatoes', description: 'Orange sweet potatoes', price: 1800, stock: 50, CategoryId: createdCategories[5].id, UserId: farmer.id },
      { name: 'Yam Tubers', description: 'Large yam tubers for cooking', price: 2200, stock: 40, CategoryId: createdCategories[5].id, UserId: farmer.id },
      { name: 'Irish Potatoes', description: 'White potatoes for various dishes', price: 2000, stock: 60, CategoryId: createdCategories[5].id, UserId: farmer.id },

      // Legumes & Beans
      { name: 'Black Beans', description: 'Protein-rich black beans', price: 2800, stock: 45, CategoryId: createdCategories[6].id, UserId: farmer.id },
      { name: 'Red Kidney Beans', description: 'Red kidney beans for stews', price: 3000, stock: 40, CategoryId: createdCategories[6].id, UserId: farmer.id },
      { name: 'Fresh Groundnuts', description: 'Raw groundnuts for processing', price: 2500, stock: 55, CategoryId: createdCategories[6].id, UserId: farmer.id },
      { name: 'Cowpeas', description: 'Black-eyed peas for cooking', price: 2200, stock: 50, CategoryId: createdCategories[6].id, UserId: farmer.id },

      // Leafy Greens
      { name: 'Fresh Spinach', description: 'Iron-rich spinach leaves', price: 1500, stock: 30, CategoryId: createdCategories[7].id, UserId: farmer.id },
      { name: 'Lettuce', description: 'Crisp lettuce for salads', price: 1200, stock: 35, CategoryId: createdCategories[7].id, UserId: farmer.id },
      { name: 'Kale', description: 'Nutritious kale leaves', price: 1800, stock: 25, CategoryId: createdCategories[7].id, UserId: farmer.id },
      { name: 'Cabbage', description: 'Fresh cabbage heads', price: 1000, stock: 40, CategoryId: createdCategories[7].id, UserId: farmer.id },

      // Nuts & Seeds
      { name: 'Palm Nuts', description: 'Fresh palm nuts for oil extraction', price: 2000, stock: 60, CategoryId: createdCategories[8].id, UserId: farmer.id },
      { name: 'Sesame Seeds', description: 'Tiny sesame seeds for cooking', price: 4000, stock: 20, CategoryId: createdCategories[8].id, UserId: farmer.id },
      { name: 'Sunflower Seeds', description: 'Roasted sunflower seeds', price: 3500, stock: 25, CategoryId: createdCategories[8].id, UserId: farmer.id },

      // Citrus Fruits
      { name: 'Sweet Oranges', description: 'Juicy sweet oranges', price: 1200, stock: 80, CategoryId: createdCategories[9].id, UserId: farmer.id },
      { name: 'Fresh Lemons', description: 'Sour lemons for cooking', price: 1500, stock: 50, CategoryId: createdCategories[9].id, UserId: farmer.id },
      { name: 'Grapefruits', description: 'Large pink grapefruits', price: 1800, stock: 30, CategoryId: createdCategories[9].id, UserId: farmer.id },
      { name: 'Limes', description: 'Small green limes', price: 1400, stock: 40, CategoryId: createdCategories[9].id, UserId: farmer.id }
    ];

    console.log('🛍️ Creating products...');
    for (const productData of products) {
      const [product] = await Product.findOrCreate({
        where: { name: productData.name },
        defaults: productData
      });
      console.log(`✅ Product: ${product.name} - ${product.price} FCFA`);
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log(`📊 Created ${createdCategories.length} categories and ${products.length} products`);
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
}

// Run the seeding
if (require.main === module) {
  seedDatabase().then(() => {
    console.log('✅ Seeding process finished');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  });
}

module.exports = { seedDatabase };
