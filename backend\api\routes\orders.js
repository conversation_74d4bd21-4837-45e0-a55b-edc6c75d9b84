// Import necessary modules
const express = require('express');
const { Order, OrderItem, Product, User, Category, Cart, CartItem } = require('../../data/models');
const { protect } = require('../../middleware/auth');
const { asyncHandler } = require('../../middleware/errorHandler');

// Create a new router
const router = express.Router();

// @desc    Create new order from cart
// @route   POST /api/orders/create
// @access  Private
const createOrder = asyncHandler(async (req, res) => {
  const {
    shippingAddress,
    paymentMethod = 'mobile_money',
    phoneNumber,
    notes
  } = req.body;

  if (!shippingAddress || !phoneNumber) {
    return res.status(400).json({
      success: false,
      message: 'Shipping address and phone number are required'
    });
  }

  // Get user's cart
  const cart = await Cart.findOne({
    where: { userId: req.user.id },
    include: [
      {
        model: CartItem,
        include: [{ model: Product }]
      }
    ]
  });

  if (!cart || !cart.CartItems || cart.CartItems.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Cart is empty'
    });
  }

  // Calculate totals
  let subtotal = 0;
  for (const item of cart.CartItems) {
    if (!item.Product) {
      return res.status(400).json({
        success: false,
        message: 'Invalid product in cart'
      });
    }
    subtotal += parseFloat(item.price) * item.quantity;
  }

  const shipping = subtotal > 10000 ? 0 : 500;
  const tax = Math.round(subtotal * 0.05);
  const total = subtotal + shipping + tax;

  // Generate order number
  const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

  // Create order
  const order = await Order.create({
    orderNumber,
    userId: req.user.id,
    status: 'PENDING',
    totalAmount: total,
    shippingFee: shipping,
    tax,
    shippingAddress,
    notes
  });

  // Create delivery record and assign to delivery agent
  const { Delivery, User } = require('../../data/models');

  // Find available delivery agent (simple round-robin assignment)
  const deliveryAgents = await User.findAll({
    where: { role: 'DELIVERY_AGENT', status: 'ACTIVE' },
    order: [['createdAt', 'ASC']]
  });

  let assignedAgent = null;
  if (deliveryAgents.length > 0) {
    // Simple assignment to first available agent (can be improved with load balancing)
    assignedAgent = deliveryAgents[0];
  }

  // Generate tracking number
  const trackingNumber = `TRK-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

  // Create delivery record
  await Delivery.create({
    orderId: order.id,
    deliveryAgentId: assignedAgent?.id || null,
    status: assignedAgent ? 'ASSIGNED' : 'PENDING',
    trackingNumber,
    estimatedDelivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
    deliveryFee: shipping,
    deliveryAddress: shippingAddress
  });

  // Create order items
  for (const cartItem of cart.CartItems) {
    await OrderItem.create({
      orderId: order.id,
      productId: cartItem.productId,
      quantity: cartItem.quantity,
      price: cartItem.price,
      total: parseFloat(cartItem.price) * cartItem.quantity
    });
  }

  // Clear cart
  await CartItem.destroy({ where: { cartId: cart.id } });
  await cart.update({ total: 0 });

  res.status(201).json({
    success: true,
    message: 'Order created successfully',
    data: {
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        totalAmount: order.totalAmount,
        status: order.status
      }
    }
  });
});

// @desc    Get all orders for user
// @route   GET /api/orders
// @access  Private
const getOrders = asyncHandler(async (req, res) => {
  const orders = await Order.findAll({
    where: { userId: req.user.id },
    include: [
      {
        model: OrderItem,
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'images']
          }
        ]
      }
    ],
    order: [['createdAt', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      orders
    }
  });
});

// Define routes
router.post('/create', protect, createOrder);
router.get('/', protect, getOrders);

// Export the router
module.exports = router;