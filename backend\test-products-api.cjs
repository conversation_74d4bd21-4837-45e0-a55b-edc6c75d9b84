// Test products API
const axios = require('axios');

async function testProductsAPI() {
  console.log('🧪 Testing Products API...\n');
  
  try {
    console.log('Testing: GET /api/products');
    const response = await axios.get('http://localhost:3001/api/products');
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running on port 3001');
      console.log('💡 Please start the server with: node server.js');
    } else {
      console.log('❌ API Error:', error.response?.data || error.message);
    }
  }
}

testProductsAPI();
