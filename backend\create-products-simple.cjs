// Simple approach to create 8 products per category
const mysql = require('mysql2/promise');

async function createProductsSimple() {
  console.log('🌾 Creating 8 Products Per Category (Simple Approach)...\n');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to database');
    
    // Step 1: Ensure farmer exists
    console.log('1. Creating sample farmer...');
    const userId = 'farmer-user-001';
    const farmerId = 'farmer-001';
    
    try {
      await connection.execute(`
        INSERT INTO users (id, firstName, lastName, email, password, role, status, emailVerified, createdAt, updatedAt)
        VALUES (?, 'John', 'Farmer', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'FARMER', 'ACTIVE', true, NOW(), NOW())
      `, [userId]);
      console.log('   ✅ User created');
    } catch (e) {
      console.log('   ✅ User already exists');
    }
    
    try {
      await connection.execute(`
        INSERT INTO farmers (id, userId, farmName, farmLocation, verified, rating, totalSales, createdAt, updatedAt)
        VALUES (?, ?, 'Green Valley Farm', 'Douala, Cameroon', true, 4.5, 0, NOW(), NOW())
      `, [farmerId, userId]);
      console.log('   ✅ Farmer created');
    } catch (e) {
      console.log('   ✅ Farmer already exists');
    }
    
    // Step 2: Get categories
    const [categories] = await connection.execute('SELECT id, name FROM categories ORDER BY name');
    console.log(`\n2. Found ${categories.length} categories`);
    
    // Step 3: Create products for each category
    console.log('\n3. Creating products...');
    
    const allProducts = [
      // Cash Crops (8 products)
      ['Cocoa Beans', 'Premium cocoa beans, sun-dried', 2000, 'kg', 100],
      ['Coffee Beans', 'Arabica coffee beans, mountain grown', 3000, 'kg', 80],
      ['Raw Cotton', 'High-quality cotton fiber', 1500, 'kg', 120],
      ['Palm Oil', 'Fresh red palm oil', 1200, 'liter', 150],
      ['Palm Kernels', 'Fresh palm kernels', 800, 'kg', 200],
      ['Rubber Latex', 'Fresh rubber latex', 1000, 'liter', 100],
      ['Cocoa Pods', 'Fresh cocoa pods', 500, 'kg', 180],
      ['Coffee Cherries', 'Fresh coffee cherries', 1800, 'kg', 90],
      
      // Cereals & Grains (8 products)
      ['Yellow Maize', 'High-quality yellow corn', 400, 'kg', 500],
      ['White Rice', 'Premium white rice', 800, 'kg', 200],
      ['Red Sorghum', 'Traditional sorghum', 550, 'kg', 300],
      ['Pearl Millet', 'Nutritious millet grains', 600, 'kg', 250],
      ['Wheat Flour', 'Fresh ground wheat', 700, 'kg', 150],
      ['Brown Rice', 'Organic brown rice', 900, 'kg', 180],
      ['White Maize', 'Premium white corn', 450, 'kg', 400],
      ['Fonio Grain', 'Ancient superfood grain', 1200, 'kg', 100],
      
      // Fruits (8 products)
      ['Sweet Bananas', 'Ripe yellow bananas', 250, 'bunch', 200],
      ['Fresh Pineapple', 'Sweet tropical pineapples', 600, 'piece', 150],
      ['Ripe Mangoes', 'Sweet Julie mangoes', 400, 'kg', 180],
      ['Fresh Papaya', 'Ripe papayas', 350, 'piece', 120],
      ['Avocados', 'Fresh avocados', 800, 'kg', 100],
      ['Bush Mango', 'Wild bush mango', 1000, 'kg', 80],
      ['Oranges', 'Sweet Valencia oranges', 300, 'kg', 250],
      ['Lemons', 'Fresh lemons', 500, 'kg', 150],
      
      // Legumes & Beans (8 products)
      ['Groundnuts', 'Spanish variety peanuts', 900, 'kg', 300],
      ['Black-eyed Peas', 'Fresh cowpeas', 600, 'kg', 250],
      ['Red Kidney Beans', 'Premium kidney beans', 800, 'kg', 200],
      ['Soybeans', 'High-protein soybeans', 700, 'kg', 280],
      ['White Beans', 'Fresh white beans', 650, 'kg', 220],
      ['Bambara Nuts', 'Traditional bambara nuts', 1000, 'kg', 150],
      ['Green Beans', 'Fresh green beans', 500, 'kg', 180],
      ['Lima Beans', 'Large lima beans', 750, 'kg', 160],
      
      // Root Crops & Tubers (8 products)
      ['Fresh Cassava', 'Sweet cassava tubers', 300, 'kg', 500],
      ['Sweet Yam', 'Fresh yam tubers', 450, 'kg', 300],
      ['Irish Potatoes', 'Fresh Irish potatoes', 600, 'kg', 250],
      ['Sweet Potatoes', 'Orange sweet potatoes', 400, 'kg', 350],
      ['Cocoyam', 'Fresh cocoyam tubers', 500, 'kg', 200],
      ['Ripe Plantains', 'Sweet ripe plantains', 350, 'bunch', 180],
      ['Green Plantains', 'Unripe plantains', 300, 'bunch', 220],
      ['Water Yam', 'Large water yam', 550, 'kg', 150],
      
      // Spices & Herbs (8 products)
      ['Fresh Ginger', 'Yellow ginger variety', 1500, 'kg', 120],
      ['Hot Pepper', 'Fresh hot peppers', 800, 'kg', 100],
      ['Fresh Garlic', 'Local garlic bulbs', 1200, 'kg', 80],
      ['Fresh Basil', 'Sweet basil leaves', 600, 'kg', 60],
      ['Lemongrass', 'Fresh lemongrass stalks', 400, 'kg', 90],
      ['Fresh Mint', 'Spearmint leaves', 500, 'kg', 70],
      ['Turmeric Root', 'Fresh turmeric roots', 1000, 'kg', 85],
      ['Scotch Bonnet', 'Very hot peppers', 1500, 'kg', 50],
      
      // Vegetables (8 products)
      ['Fresh Tomatoes', 'Organic red tomatoes', 500, 'kg', 300],
      ['Fresh Spinach', 'Tender spinach leaves', 400, 'kg', 150],
      ['Red Onions', 'Fresh red onions', 700, 'kg', 200],
      ['Green Cabbage', 'Fresh cabbage heads', 300, 'head', 180],
      ['Fresh Carrots', 'Orange carrots', 450, 'kg', 160],
      ['Green Peppers', 'Fresh bell peppers', 600, 'kg', 140],
      ['Fresh Okra', 'Tender okra pods', 350, 'kg', 120],
      ['Cucumber', 'Fresh cucumbers', 400, 'kg', 100]
    ];
    
    // Map products to categories (8 products each)
    let productIndex = 0;
    let totalCreated = 0;
    
    for (const category of categories) {
      console.log(`\n   Creating products for: ${category.name}`);
      
      for (let i = 0; i < 8; i++) {
        if (productIndex >= allProducts.length) break;
        
        const [name, description, price, unit, stock] = allProducts[productIndex];
        const productId = `prod-${category.id}-${i + 1}`;
        
        try {
          await connection.execute(`
            INSERT INTO products (
              id, name, description, price, unit, stock, minOrder, images, status, 
              farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, 
              totalSold, createdAt, updatedAt
            ) VALUES (?, ?, ?, ?, ?, ?, 1, ?, 'ACTIVE', ?, ?, false, false, 0, 0, 0, NOW(), NOW())
          `, [
            productId, name, description, price, unit, stock,
            JSON.stringify(['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80']),
            farmerId, category.id
          ]);
          
          totalCreated++;
          console.log(`     ✅ ${i + 1}. ${name} - ${price} FCFA/${unit}`);
          
        } catch (error) {
          console.log(`     ❌ Failed: ${name} - ${error.message}`);
        }
        
        productIndex++;
      }
    }
    
    // Final check
    const [finalCount] = await connection.execute('SELECT COUNT(*) as count FROM products');
    console.log(`\n🎉 Product creation completed!`);
    console.log(`   Products created: ${totalCreated}`);
    console.log(`   Total in database: ${finalCount[0].count}`);
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Creation failed:', error.message);
  }
  
  process.exit(0);
}

createProductsSimple();
