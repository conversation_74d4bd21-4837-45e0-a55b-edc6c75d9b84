const express = require('express');
const router = express.Router();
const { protect } = require('../../middleware/auth');
const { User, Farmer } = require('../../data/models');
const { asyncHandler } = require('../../middleware/errorHandler');

// Get all users (admin only)
router.get('/', protect, asyncHandler(async (req, res) => {
  const users = await User.findAll({
    attributes: { exclude: ['password'] },
    include: [{ model: Farmer, required: false }]
  });

  res.json({
    success: true,
    data: { users }
  });
}));

// Get user by ID
router.get('/:id', protect, asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.params.id, {
    attributes: { exclude: ['password'] },
    include: [{ model: Farmer, required: false }]
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  res.json({
    success: true,
    data: { user }
  });
}));

// Update user personal information
router.put('/profile', protect, asyncHandler(async (req, res) => {
  const { firstName, lastName, email, phone, avatar } = req.body;

  // Check if email is already taken by another user
  if (email && email !== req.user.email) {
    const { Op } = require('sequelize');
    const existingUser = await User.findOne({
      where: { email, id: { [Op.ne]: req.user.id } }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Email is already taken by another user'
      });
    }
  }

  // Update user information
  const updateData = {};
  if (firstName) updateData.firstName = firstName;
  if (lastName) updateData.lastName = lastName;
  if (email) updateData.email = email;
  if (phone) updateData.phone = phone;
  if (avatar) updateData.avatar = avatar;

  await User.update(updateData, {
    where: { id: req.user.id }
  });

  // Get updated user
  const updatedUser = await User.findByPk(req.user.id, {
    attributes: { exclude: ['password'] }
  });

  res.json({
    success: true,
    data: updatedUser,
    message: 'Personal information updated successfully'
  });
}));

module.exports = router;