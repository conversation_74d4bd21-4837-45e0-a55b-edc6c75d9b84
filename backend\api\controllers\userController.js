const bcrypt = require('bcryptjs');

exports.getProfile = async (req, res, next) => {
  try {
    const user = req.user

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    next(error);
  }
};

exports.updateProfile = async (req, res, next) => {
  try {
    const user = await User.findByPk(req.user.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.username = req.body.username || user.username;
    user.email = req.body.email || user.email;

    if (req.body.password) {
      user.password = await bcrypt.hash(req.body.password, config.bcryptSaltRounds);
    }

    await user.save();

    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    });
  } catch (error) {
    next(error);
  }
};

exports.deleteAccount = async (req, res, next) => {
  try {
    const user = await User.findByPk(req.user.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    await user.destroy();

    res.json({ message: 'User account deleted successfully' });
  } catch (error) {
    next(error);
  }
};

exports.upgradeToVendor = async (req, res, next) => {
  try {
    

    // Check if the user is already a vendor
    if (req.user.role === 'vendor') {
      return res.status(400).json({ message: 'User is already a vendor' });
    }

    // Update the user's role to vendor
   req.user.role = 'vendor';

    // Save the updated user
    await req.user.save();

    res.json({
      message: 'Successfully upgraded to vendor',
      user: {
        id: req.user.id,
        username: req.user.username,
        email: req.user.email,
        role: req.user.role
      }
    });
  } catch (error) {
    next(error);
  }
};

// Admin Controller (preserved from original code)
exports.getDashboard = (req, res) => {
  res.json({ message: 'Admin dashboard data' });
};

exports.getAllUsers = (req, res) => {
  // Implement logic to fetch all users
  res.json({ message: 'List of all users' });
};

// Vendor Controller (preserved from original code)
exports.getDashboard = (req, res) => {
  res.json({ message: 'Vendor dashboard data' });
};

exports.createProduct = (req, res) => {
  // Implement logic to create a new product
  res.json({ message: 'Product created successfully' });
};