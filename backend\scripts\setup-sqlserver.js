#!/usr/bin/env node

/**
 * MySQL Setup Script
 * This script helps set up MySQL for the Cameroon Farm Connect Hub
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Cameroon Farm Connect Hub - SQL Server Setup');
console.log('================================================\n');

// Check if Docker is available
function checkDocker() {
  try {
    execSync('docker --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

// Check if SQL Server is running locally
function checkSQLServer() {
  try {
    // Try to connect to SQL Server Express first
    const { execSync } = require('child_process');
    try {
      execSync('sqlcmd -S localhost\\SQLEXPRESS -Q "SELECT 1" -W', { stdio: 'ignore' });
      return { running: true, instance: 'SQLEXPRESS', type: 'SQL Server Express' };
    } catch (error) {
      // Try default instance
      try {
        execSync('sqlcmd -S localhost -Q "SELECT 1" -W', { stdio: 'ignore' });
        return { running: true, instance: 'DEFAULT', type: 'SQL Server' };
      } catch (error) {
        return { running: false };
      }
    }
  } catch (error) {
    return { running: false };
  }
}

// Create .env file from template
function createEnvFile() {
  const envExamplePath = path.join(__dirname, '..', '.env.example');
  const envPath = path.join(__dirname, '..', '.env');
  
  if (!fs.existsSync(envPath)) {
    if (fs.existsSync(envExamplePath)) {
      fs.copyFileSync(envExamplePath, envPath);
      console.log('✅ Created .env file from template');
      console.log('⚠️  Please update the DATABASE_URL and other settings in .env file');
    } else {
      console.log('❌ .env.example file not found');
    }
  } else {
    console.log('ℹ️  .env file already exists');
  }
}

// Setup SQL Server with Docker
function setupDockerSQLServer() {
  console.log('🐳 Setting up SQL Server with Docker...\n');
  
  const containerName = 'cameroon_farm_sqlserver';
  const password = 'CameroonFarm123!';
  
  try {
    // Check if container already exists
    try {
      execSync(`docker inspect ${containerName}`, { stdio: 'ignore' });
      console.log('ℹ️  SQL Server container already exists');
      
      // Start the container if it's not running
      execSync(`docker start ${containerName}`, { stdio: 'ignore' });
      console.log('✅ SQL Server container started');
    } catch (error) {
      // Container doesn't exist, create it
      console.log('📦 Creating new SQL Server container...');
      
      const dockerCommand = `docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=${password}" ` +
                           `-p 1433:1433 --name ${containerName} ` +
                           `-d mcr.microsoft.com/mssql/server:2022-latest`;
      
      execSync(dockerCommand, { stdio: 'inherit' });
      console.log('✅ SQL Server container created and started');
    }
    
    console.log('\n📋 Connection Details:');
    console.log(`   Host: localhost`);
    console.log(`   Port: 1433`);
    console.log(`   Username: sa`);
    console.log(`   Password: ${password}`);
    console.log(`   Database: master (create cameroon_farm_connect database)`);
    
    console.log('\n🔗 Connection String:');
    console.log(`   DATABASE_URL="sqlserver://localhost:1433;database=cameroon_farm_connect;user=sa;password=${password};trustServerCertificate=true;encrypt=true"`);
    
    return true;
  } catch (error) {
    console.log('❌ Failed to setup SQL Server with Docker:', error.message);
    return false;
  }
}

// Function to provide SQL Server Express installation guidance
function provideSQLServerExpressGuidance() {
  console.log('\n📖 SQL Server Express Installation Guide:');
  console.log('==========================================');
  console.log('1. Download SQL Server Express from:');
  console.log('   https://www.microsoft.com/en-us/sql-server/sql-server-downloads');
  console.log('2. Choose "Basic" installation');
  console.log('3. Accept license terms and install');
  console.log('4. Download and install SQL Server Management Studio (SSMS)');
  console.log('5. Configure SQL Server Express:');
  console.log('   - Enable Mixed Mode Authentication');
  console.log('   - Set SA password');
  console.log('   - Enable TCP/IP protocol');
  console.log('   - Start SQL Server Browser service');
  console.log('\n📚 Detailed instructions: docs/SQL_SERVER_EXPRESS_SETUP.md');
}

// Main setup function
function main() {
  console.log('🔍 Checking system requirements...\n');

  // Create .env file
  createEnvFile();

  // Check for existing SQL Server
  const sqlServerStatus = checkSQLServer();
  if (sqlServerStatus.running) {
    console.log(`✅ ${sqlServerStatus.type} is running`);
    if (sqlServerStatus.instance === 'SQLEXPRESS') {
      console.log('🎉 SQL Server Express detected - perfect for development!');
      console.log('📋 Connection Details:');
      console.log('   Server: localhost\\SQLEXPRESS');
      console.log('   Database: Create "cameroon_farm_connect" database');
      console.log('\n🔗 Recommended Connection String:');
      console.log('   DATABASE_URL="sqlserver://localhost\\\\SQLEXPRESS;database=cameroon_farm_connect;integratedSecurity=true;trustServerCertificate=true;encrypt=true"');
    } else {
      console.log('ℹ️  SQL Server default instance detected');
      console.log('📋 Connection Details:');
      console.log('   Server: localhost');
      console.log('   Database: Create "cameroon_farm_connect" database');
    }
    console.log('\n⚠️  Make sure to create the "cameroon_farm_connect" database');
  } else {
    console.log('ℹ️  No local SQL Server detected');

    console.log('\n🎯 Recommended: Install SQL Server Express');
    console.log('   SQL Server Express is free and perfect for development');

    // Check for Docker as alternative
    if (checkDocker()) {
      console.log('\n✅ Docker is available as an alternative');

      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      console.log('\nChoose your setup method:');
      console.log('1. SQL Server Express (Recommended for Windows)');
      console.log('2. Docker SQL Server (Cross-platform)');

      rl.question('Enter your choice (1 or 2): ', (answer) => {
        if (answer === '2') {
          setupDockerSQLServer();
        } else {
          provideSQLServerExpressGuidance();
        }
        rl.close();
      });
    } else {
      console.log('❌ Docker is not available');
      provideSQLServerExpressGuidance();
    }
  }
  
  console.log('\n📚 Next Steps:');
  console.log('   1. Update .env file with your database connection');
  console.log('   2. Run: npm install');
  console.log('   3. Run: npm run db:generate');
  console.log('   4. Run: npm run migrate');
  console.log('   5. Run: npm run seed (optional)');
  console.log('   6. Run: npm run dev');
  
  console.log('\n📖 For detailed setup instructions, see:');
  console.log('   - docs/SQL_SERVER_SETUP.md');
  console.log('   - README.md');
}

// Run the setup
main();
