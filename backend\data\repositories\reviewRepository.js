const { Review, User, Product } = require('../models');

class ReviewRepository {
  async create(reviewData) {
    return await Review.create(reviewData);
  }

  async findById(id) {
    return await Review.findByPk(id, {
      include: [
        { model: User, attributes: ['id', 'email', 'firstName', 'lastName'] },
        { model: Product, attributes: ['id', 'name'] }
      ]
    });
  }

  async findAll(options = {}) {
    return await Review.findAll({
      ...options,
      include: [
        { model: User, attributes: ['id', 'email', 'firstName', 'lastName'] },
        { model: Product, attributes: ['id', 'name'] }
      ]
    });
  }

  async update(id, reviewData) {
    const [updatedRowsCount, updatedReviews] = await Review.update(reviewData, {
      where: { id },
      returning: true
    });
    return updatedReviews[0];
  }

  async delete(id) {
    return await Review.destroy({ where: { id } });
  }

  async findByProduct(productId) {
    return await Review.findAll({
      where: { productId },
      include: [
        { model: User, attributes: ['id', 'email', 'firstName', 'lastName'] }
      ]
    });
  }

  async findByUser(userId) {
    return await Review.findAll({
      where: { userId },
      include: [
        { model: Product, attributes: ['id', 'name'] }
      ]
    });
  }
}

module.exports = new ReviewRepository();