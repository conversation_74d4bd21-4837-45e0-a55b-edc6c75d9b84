{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo \"No build needed for JavaScript\"", "test": "echo \"Error: no test specified\" && exit 1", "db:create": "node scripts/create-database.js", "db:seed": "node scripts/seed-database.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "express-csp-header": "^6.1.0", "express-rate-limit": "^7.4.0", "express-validator": "^7.2.0", "helmet": "^7.1.0", "hpp": "^0.2.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "sequelize": "^6.37.3", "socket.io": "^4.8.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/node": "^20.8.10", "nodemon": "^3.1.7", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}