const express = require('express');
const router = express.Router();
const { Op } = require('sequelize');
const Product = require('../models/product');

router.get('/products', async (req, res) => {
  try {
    const { category, minPrice, maxPrice, sortBy } = req.query;
    
    let whereClause = {};
    let order = [];

    if (category) {
      whereClause.category = category;
    }

    if (minPrice || maxPrice) {
      whereClause.price = {};
      if (minPrice) whereClause.price[Op.gte] = minPrice;
      if (maxPrice) whereClause.price[Op.lte] = maxPrice;
    }

    if (sortBy) {
      switch (sortBy) {
        case 'price_asc':
          order.push(['price', 'ASC']);
          break;
        case 'price_desc':
          order.push(['price', 'DESC']);
          break;
        case 'name_asc':
          order.push(['name', 'ASC']);
          break;
        case 'name_desc':
          order.push(['name', 'DESC']);
          break;
      }
    }

    const products = await Product.findAll({
      where: whereClause,
      order: order
    });

    res.json(products);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching products', error: error.message });
  }
});

module.exports = router;