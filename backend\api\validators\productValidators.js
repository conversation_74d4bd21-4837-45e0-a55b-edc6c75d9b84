// Import necessary modules
const { body } = require('express-validator');
const validate = require('../middleware/validate');

// Validation rules for creating/updating a product
exports.validateProduct = [
  body('name').trim().notEmpty().withMessage('Product name is required'),
  body('description').optional().trim(),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('stock').isInt({ min: 0 }).withMessage('Stock must be a non-negative integer'),
  body('categoryId').isInt({ min: 1 }).withMessage('Valid category ID is required'),
  validate,
];