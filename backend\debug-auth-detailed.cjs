// Detailed authentication debugging
const axios = require('axios');
const jwt = require('jsonwebtoken');
const config = require('./config/config');

async function debugAuthDetailed() {
  console.log('🔍 Detailed Authentication Debugging...\n');
  
  try {
    // Step 1: Test registration
    console.log('1. Testing user registration...');
    const email = `debug.${Date.now()}@example.com`;
    
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Debug',
      lastName: 'User',
      email: email,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    console.log('✅ Registration successful');
    const token = registerResponse.data.data?.accessToken;
    console.log('   Token received:', token ? 'Yes' : 'No');
    
    if (!token) {
      console.log('❌ No token received from registration');
      return;
    }
    
    // Step 2: Verify token structure
    console.log('\n2. Analyzing token...');
    console.log('   Token length:', token.length);
    console.log('   Token starts with:', token.substring(0, 20) + '...');
    
    // Decode token without verification to see payload
    try {
      const decoded = jwt.decode(token);
      console.log('   Token payload:', JSON.stringify(decoded, null, 2));
      console.log('   User ID in token:', decoded?.id);
      console.log('   Token expiry:', new Date(decoded?.exp * 1000));
    } catch (decodeError) {
      console.log('   ❌ Token decode failed:', decodeError.message);
    }
    
    // Step 3: Verify token with correct secret
    console.log('\n3. Verifying token with JWT secret...');
    try {
      const verified = jwt.verify(token, config.jwt.secret);
      console.log('   ✅ Token verification successful');
      console.log('   Verified user ID:', verified.id);
    } catch (verifyError) {
      console.log('   ❌ Token verification failed:', verifyError.message);
      console.log('   JWT Secret being used:', config.jwt.secret);
    }
    
    // Step 4: Test protected route with detailed headers
    console.log('\n4. Testing protected route...');
    console.log('   Making request to /api/cart');
    console.log('   Authorization header:', `Bearer ${token.substring(0, 20)}...`);
    
    try {
      const cartResponse = await axios.get('http://localhost:3001/api/cart', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('   ✅ Protected route accessible');
      console.log('   Response status:', cartResponse.status);
      console.log('   Response success:', cartResponse.data.success);
      
    } catch (authError) {
      console.log('   ❌ Protected route failed');
      console.log('   Status:', authError.response?.status);
      console.log('   Error message:', authError.response?.data?.message);
      console.log('   Full error:', authError.response?.data);
      
      // Step 5: Test with manual token verification
      console.log('\n5. Manual token verification test...');
      try {
        const manualVerify = jwt.verify(token, config.jwt.secret);
        console.log('   ✅ Manual verification works');
        console.log('   This suggests the issue is in the middleware');
      } catch (manualError) {
        console.log('   ❌ Manual verification also fails:', manualError.message);
      }
    }
    
    // Step 6: Test login to get fresh token
    console.log('\n6. Testing login for fresh token...');
    try {
      const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
        email: email,
        password: 'Test123!'
      });
      
      const loginToken = loginResponse.data.data?.accessToken;
      console.log('   ✅ Login successful');
      console.log('   Fresh token received:', loginToken ? 'Yes' : 'No');
      
      if (loginToken) {
        console.log('\n7. Testing with fresh login token...');
        const freshCartResponse = await axios.get('http://localhost:3001/api/cart', {
          headers: {
            'Authorization': `Bearer ${loginToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('   ✅ Fresh token works!');
        console.log('   The issue might be with token expiry or format');
      }
      
    } catch (loginError) {
      console.log('   ❌ Login failed:', loginError.response?.data || loginError.message);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.response?.data || error.message);
  }
}

debugAuthDetailed();
