const { body, param, query, validationResult } = require('express-validator');
const { ApiError, formatValidationErrors } = require('./errorHandler');

// Validation result handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = formatValidationErrors(errors.array());
    const message = formattedErrors.map(err => err.message).join(', ');
    return next(new ApiError(message, 400));
  }
  next();
};

// User validation rules
const validateUserRegistration = [
  body('firstName')
    .trim()
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  
  body('lastName')
    .trim()
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail(),
  
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  
  body('role')
    .optional()
    .isIn(['CUSTOMER', 'FARMER', 'ADMIN', 'DELIVERY_AGENT', 'NUTRITIONIST'])
    .withMessage('Invalid role specified'),
  
  // Farmer-specific fields
  body('farmName')
    .if(body('role').equals('FARMER'))
    .notEmpty()
    .withMessage('Farm name is required for farmers')
    .isLength({ min: 2, max: 100 })
    .withMessage('Farm name must be between 2 and 100 characters'),
  
  body('farmLocation')
    .if(body('role').equals('FARMER'))
    .notEmpty()
    .withMessage('Farm location is required for farmers')
    .isLength({ min: 5, max: 200 })
    .withMessage('Farm location must be between 5 and 200 characters'),
  
  handleValidationErrors
];

const validateUserLogin = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail(),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  
  handleValidationErrors
];

// Product validation rules
const validateProduct = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Product name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Product name must be between 2 and 100 characters'),
  
  body('description')
    .trim()
    .notEmpty()
    .withMessage('Product description is required')
    .isLength({ min: 10, max: 1000 })
    .withMessage('Product description must be between 10 and 1000 characters'),
  
  body('price')
    .isFloat({ min: 0.01 })
    .withMessage('Price must be a positive number'),
  
  body('unit')
    .optional()
    .isIn(['kg', 'g', 'piece', 'bunch', 'bag', 'liter', 'ml'])
    .withMessage('Invalid unit specified'),
  
  body('stock')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Stock must be a non-negative integer'),
  
  body('minOrder')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Minimum order must be at least 1'),
  
  body('categoryId')
    .isUUID()
    .withMessage('Valid category ID is required'),
  
  body('harvestDate')
    .optional()
    .isISO8601()
    .withMessage('Harvest date must be a valid date'),
  
  body('expiryDate')
    .optional()
    .isISO8601()
    .withMessage('Expiry date must be a valid date'),
  
  body('isOrganic')
    .optional()
    .isBoolean()
    .withMessage('Organic flag must be true or false'),
  
  body('isFairTrade')
    .optional()
    .isBoolean()
    .withMessage('Fair trade flag must be true or false'),
  
  handleValidationErrors
];

// Category validation rules
const validateCategory = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Category name must be between 2 and 50 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Category description must not exceed 500 characters'),
  
  body('parentId')
    .optional()
    .isUUID()
    .withMessage('Parent ID must be a valid UUID'),
  
  handleValidationErrors
];

// Cart validation rules
const validateCartItem = [
  body('productId')
    .isUUID()
    .withMessage('Valid product ID is required'),
  
  body('quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  
  handleValidationErrors
];

// Order validation rules
const validateOrder = [
  body('shippingAddress')
    .isObject()
    .withMessage('Shipping address is required'),
  
  body('shippingAddress.street')
    .trim()
    .notEmpty()
    .withMessage('Street address is required'),
  
  body('shippingAddress.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  
  body('shippingAddress.region')
    .trim()
    .notEmpty()
    .withMessage('Region is required'),
  
  body('shippingAddress.postalCode')
    .optional()
    .trim(),
  
  body('shippingAddress.phone')
    .isMobilePhone('any')
    .withMessage('Valid phone number is required for delivery'),
  
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes must not exceed 500 characters'),
  
  handleValidationErrors
];

// Review validation rules
const validateReview = [
  body('productId')
    .isUUID()
    .withMessage('Valid product ID is required'),
  
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  
  body('title')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Review title must not exceed 100 characters'),
  
  body('comment')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Review comment must not exceed 1000 characters'),
  
  handleValidationErrors
];

// Parameter validation
const validateUUID = (paramName) => [
  param(paramName)
    .isUUID()
    .withMessage(`${paramName} must be a valid UUID`),
  
  handleValidationErrors
];

// Query validation
const validatePagination = [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Offset must be a non-negative integer'),
  
  handleValidationErrors
];

module.exports = {
  validateUserRegistration,
  validateUserLogin,
  validateProduct,
  validateCategory,
  validateCartItem,
  validateOrder,
  validateReview,
  validateUUID,
  validatePagination,
  handleValidationErrors
};
