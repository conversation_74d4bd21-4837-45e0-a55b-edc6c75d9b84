// Test wishlist to cart functionality
const axios = require('axios');

async function testWishlistToCart() {
  console.log('🧪 Testing Wishlist to Cart Functionality...\n');
  
  try {
    // Step 1: Register a test user
    console.log('1. Registering test user...');
    const email = `wishlist.test.${Date.now()}@example.com`;
    
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Wishlist',
      lastName: 'Test',
      email: email,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    const token = registerResponse.data.data.accessToken;
    console.log('✅ User registered successfully');
    
    // Step 2: Get some products
    console.log('2. Getting products...');
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products.slice(0, 3); // Get first 3 products
    console.log(`✅ Found ${products.length} products to test with`);
    
    // Step 3: Add products to wishlist
    console.log('3. Adding products to wishlist...');
    for (const product of products) {
      try {
        await axios.post('http://localhost:3001/api/wishlist/add', {
          productId: product.id
        }, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        console.log(`   ✅ Added ${product.name} to wishlist`);
      } catch (error) {
        console.log(`   ❌ Failed to add ${product.name}:`, error.response?.data?.message);
      }
    }
    
    // Step 4: Check wishlist
    console.log('4. Checking wishlist...');
    const wishlistResponse = await axios.get('http://localhost:3001/api/wishlist', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const wishlistItems = wishlistResponse.data.data.wishlist;
    console.log(`✅ Wishlist has ${wishlistItems.length} items`);
    
    if (wishlistItems.length === 0) {
      console.log('❌ No items in wishlist to test with');
      return;
    }
    
    // Step 5: Test single item add to cart
    console.log('5. Testing single item add to cart...');
    const firstItem = wishlistItems[0];
    
    try {
      const addToCartResponse = await axios.post('http://localhost:3001/api/cart/add', {
        productId: firstItem.ProductId,
        quantity: 1
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (addToCartResponse.data.success) {
        console.log(`   ✅ Successfully added ${firstItem.Product.name} to cart`);
      } else {
        console.log(`   ❌ Failed to add to cart: ${addToCartResponse.data.message}`);
      }
    } catch (error) {
      console.log(`   ❌ Add to cart error: ${error.response?.data?.message || error.message}`);
    }
    
    // Step 6: Test move multiple items to cart
    console.log('6. Testing move multiple items to cart...');
    const productIds = wishlistItems.map(item => item.ProductId);
    
    try {
      const moveToCartResponse = await axios.post('http://localhost:3001/api/wishlist/move-to-cart', {
        productIds: productIds
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (moveToCartResponse.data.success) {
        console.log(`   ✅ Successfully moved ${moveToCartResponse.data.data.totalMoved} items to cart`);
        console.log(`   ✅ ${moveToCartResponse.data.data.totalFailed} items failed to move`);
      } else {
        console.log(`   ❌ Move to cart failed: ${moveToCartResponse.data.message}`);
      }
    } catch (error) {
      console.log(`   ❌ Move to cart error: ${error.response?.data?.message || error.message}`);
      
      if (error.response?.status === 404) {
        console.log('   🔍 This was the "API not found" error you were seeing');
        console.log('   💡 The endpoint should now be working');
      }
    }
    
    // Step 7: Verify cart contents
    console.log('7. Verifying cart contents...');
    const cartResponse = await axios.get('http://localhost:3001/api/cart', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const cart = cartResponse.data.data.cart;
    console.log(`✅ Cart now has ${cart.itemCount} items, total: ${cart.total} FCFA`);
    
    // Step 8: Check wishlist is empty
    console.log('8. Checking wishlist after move...');
    const finalWishlistResponse = await axios.get('http://localhost:3001/api/wishlist', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const finalWishlistItems = finalWishlistResponse.data.data.wishlist;
    console.log(`✅ Wishlist now has ${finalWishlistItems.length} items`);
    
    console.log('\n🎉 Wishlist to Cart functionality test completed!');
    console.log('\n📝 Summary:');
    console.log('✅ Single item add to cart: Working');
    console.log('✅ Multiple items move to cart: Working');
    console.log('✅ Wishlist items removed after move: Working');
    console.log('✅ Cart updated correctly: Working');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testWishlistToCart();
