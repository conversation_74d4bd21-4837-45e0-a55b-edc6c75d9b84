// Simple API service to connect frontend with backend
const API_BASE_URL = 'http://localhost:3001';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('accessToken');
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Helper function to make API calls
const apiCall = async (endpoint, options = {}) => {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders(),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
};

// Products API
export const productsAPI = {
  // Get all products
  getAll: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/api/products${queryString ? `?${queryString}` : ''}`);
  },

  // Get product by ID
  getById: async (id) => {
    return apiCall(`/api/products/${id}`);
  },

  // Create new product
  create: async (productData) => {
    return apiCall('/api/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  },

  // Update product
  update: async (id, productData) => {
    return apiCall(`/api/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    });
  },

  // Delete product
  delete: async (id) => {
    return apiCall(`/api/products/${id}`, {
      method: 'DELETE',
    });
  },
};

// Categories API
export const categoriesAPI = {
  getAll: async () => {
    return apiCall('/api/categories');
  },

  getById: async (id) => {
    return apiCall(`/api/categories/${id}`);
  },
};

// Users API
export const usersAPI = {
  // Get all users (for admin)
  getAll: async () => {
    return apiCall('/api/users');
  },

  // Get user profile
  getProfile: async (id) => {
    return apiCall(`/api/users/${id}`);
  },

  // Update user profile
  updateProfile: async (id, userData) => {
    return apiCall(`/api/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },
};

// Orders API
export const ordersAPI = {
  // Get all orders
  getAll: async () => {
    return apiCall('/api/orders');
  },

  // Get order by ID
  getById: async (id) => {
    return apiCall(`/api/orders/${id}`);
  },

  // Create new order
  create: async (orderData) => {
    return apiCall('/api/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  },

  // Update order status
  updateStatus: async (id, status) => {
    return apiCall(`/api/orders/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  },
};

// Reviews API
export const reviewsAPI = {
  // Get reviews for a product
  getByProduct: async (productId) => {
    return apiCall(`/api/reviews/product/${productId}`);
  },

  // Create new review
  create: async (reviewData) => {
    return apiCall('/api/reviews', {
      method: 'POST',
      body: JSON.stringify(reviewData),
    });
  },

  // Update review
  update: async (id, reviewData) => {
    return apiCall(`/api/reviews/${id}`, {
      method: 'PUT',
      body: JSON.stringify(reviewData),
    });
  },

  // Delete review
  delete: async (id) => {
    return apiCall(`/api/reviews/${id}`, {
      method: 'DELETE',
    });
  },
};

// Cart API
export const cartAPI = {
  // Get user's cart
  get: async (userId) => {
    return apiCall(`/api/cart/${userId}`);
  },

  // Add item to cart
  addItem: async (cartData) => {
    return apiCall('/api/cart', {
      method: 'POST',
      body: JSON.stringify(cartData),
    });
  },

  // Update cart item
  updateItem: async (itemId, quantity) => {
    return apiCall(`/api/cart/${itemId}`, {
      method: 'PUT',
      body: JSON.stringify({ quantity }),
    });
  },

  // Remove item from cart
  removeItem: async (itemId) => {
    return apiCall(`/api/cart/${itemId}`, {
      method: 'DELETE',
    });
  },

  // Clear cart
  clear: async (userId) => {
    return apiCall(`/api/cart/${userId}/clear`, {
      method: 'DELETE',
    });
  },
};

// Health check
export const healthAPI = {
  check: async () => {
    return apiCall('/health');
  },
};

// Analytics API
export const analyticsAPI = {
  // Get dashboard stats
  getDashboardStats: async () => {
    return apiCall('/api/analytics/dashboard');
  },

  // Get sales data
  getSalesData: async (period = '30d') => {
    return apiCall(`/api/analytics/sales?period=${period}`);
  },

  // Get user analytics
  getUserAnalytics: async () => {
    return apiCall('/api/analytics/users');
  },

  // Get product analytics
  getProductAnalytics: async () => {
    return apiCall('/api/analytics/products');
  },
};

// Export default API object
export default {
  products: productsAPI,
  categories: categoriesAPI,
  users: usersAPI,
  orders: ordersAPI,
  reviews: reviewsAPI,
  cart: cartAPI,
  health: healthAPI,
  analytics: analyticsAPI,
};

