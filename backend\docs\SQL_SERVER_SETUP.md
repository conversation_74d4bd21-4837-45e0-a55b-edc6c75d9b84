# SQL Server Setup Guide

This guide will help you set up SQL Server for the Cameroon Farm Connect Hub backend.

## Option 1: SQL Server Express (Free, Recommended for Development)

### Windows Installation

1. **Download SQL Server Express**
   - Go to [Microsoft SQL Server Express](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)
   - Download SQL Server 2022 Express

2. **Install SQL Server Express**
   - Run the installer
   - Choose "Basic" installation
   - Accept the license terms
   - Choose installation location
   - Wait for installation to complete

3. **Install SQL Server Management Studio (SSMS)**
   - Download from [Microsoft SSMS](https://docs.microsoft.com/en-us/sql/ssms/download-sql-server-management-studio-ssms)
   - Install SSMS for easier database management

### Linux Installation (Ubuntu/Debian)

```bash
# Import the public repository GPG keys
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -

# Register the Microsoft SQL Server Ubuntu repository
sudo add-apt-repository "$(wget -qO- https://packages.microsoft.com/config/ubuntu/20.04/mssql-server-2022.list)"

# Update packages and install SQL Server
sudo apt-get update
sudo apt-get install -y mssql-server

# Run setup
sudo /opt/mssql/bin/mssql-conf setup

# Install SQL Server command-line tools
curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list
sudo apt-get update
sudo apt-get install mssql-tools unixodbc-dev

# Add to PATH
echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc
source ~/.bashrc
```

### macOS Installation (Using Docker)

```bash
# Pull SQL Server Docker image
docker pull mcr.microsoft.com/mssql/server:2022-latest

# Run SQL Server container
docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=YourStrongPassword123" \
   -p 1433:1433 --name sql_server_container \
   -d mcr.microsoft.com/mssql/server:2022-latest
```

## Option 2: Docker Setup (Cross-Platform)

### Using Docker Compose

Create a `docker-compose.yml` file in your project root:

```yaml
version: '3.8'

services:
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: cameroon_farm_sqlserver
    environment:
      - ACCEPT_EULA=Y
      - MSSQL_SA_PASSWORD=YourStrongPassword123
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    restart: unless-stopped

volumes:
  sqlserver_data:
```

Run with:
```bash
docker-compose up -d
```

## Database Configuration

### 1. Create Database

Connect to SQL Server and create the database:

```sql
-- Using SSMS or sqlcmd
CREATE DATABASE cameroon_farm_connect;
GO

USE cameroon_farm_connect;
GO
```

### 2. Create User (Optional, for security)

```sql
-- Create a dedicated user for the application
CREATE LOGIN cameroon_farm_user WITH PASSWORD = 'YourAppPassword123';
GO

USE cameroon_farm_connect;
GO

CREATE USER cameroon_farm_user FOR LOGIN cameroon_farm_user;
GO

-- Grant necessary permissions
ALTER ROLE db_datareader ADD MEMBER cameroon_farm_user;
ALTER ROLE db_datawriter ADD MEMBER cameroon_farm_user;
ALTER ROLE db_ddladmin ADD MEMBER cameroon_farm_user;
GO
```

### 3. Environment Configuration

Update your `.env` file:

```env
# For SA user (development)
DATABASE_URL="sqlserver://localhost:1433;database=cameroon_farm_connect;user=sa;password=YourStrongPassword123;trustServerCertificate=true;encrypt=true"

# For dedicated user (recommended)
DATABASE_URL="sqlserver://localhost:1433;database=cameroon_farm_connect;user=cameroon_farm_user;password=YourAppPassword123;trustServerCertificate=true;encrypt=true"

# Individual connection parameters
SQL_SERVER_HOST=localhost
SQL_SERVER_PORT=1433
SQL_SERVER_DATABASE=cameroon_farm_connect
SQL_SERVER_USER=sa
SQL_SERVER_PASSWORD=YourStrongPassword123
SQL_SERVER_ENCRYPT=true
SQL_SERVER_TRUST_CERT=true
```

## Connection String Formats

### Standard Authentication
```
sqlserver://localhost:1433;database=cameroon_farm_connect;user=sa;password=YourPassword;trustServerCertificate=true;encrypt=true
```

### Windows Authentication (Windows only)
```
sqlserver://localhost:1433;database=cameroon_farm_connect;integratedSecurity=true;trustServerCertificate=true;encrypt=true
```

### Azure SQL Database
```
sqlserver://your-server.database.windows.net:1433;database=cameroon_farm_connect;user=your-username;password=your-password;encrypt=true
```

## Running Prisma Migrations

After setting up SQL Server:

```bash
# Generate Prisma client
npm run db:generate

# Create and apply migrations
npm run migrate

# Seed the database
npm run seed
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure SQL Server is running
   - Check if port 1433 is open
   - Verify firewall settings

2. **Authentication Failed**
   - Check username and password
   - Ensure SQL Server authentication is enabled
   - For SA account, ensure it's enabled

3. **SSL/TLS Issues**
   - Use `trustServerCertificate=true` for development
   - For production, configure proper SSL certificates

4. **Docker Issues**
   - Ensure Docker is running
   - Check container logs: `docker logs sql_server_container`
   - Verify port mapping

### Enable SQL Server Authentication (Windows)

If using Windows Authentication is not working:

1. Open SQL Server Management Studio
2. Right-click server → Properties
3. Go to Security tab
4. Select "SQL Server and Windows Authentication mode"
5. Restart SQL Server service

### Check SQL Server Status

```bash
# Windows
net start mssqlserver

# Linux
sudo systemctl status mssql-server

# Docker
docker ps | grep sql
```

## Performance Optimization

### For Development
- Use SQL Server Express (free, sufficient for development)
- Enable TCP/IP protocol
- Set appropriate memory limits

### For Production
- Use SQL Server Standard or Enterprise
- Configure proper backup strategies
- Set up monitoring and alerting
- Configure connection pooling
- Optimize database settings

## Security Best Practices

1. **Use Strong Passwords**
   - Minimum 12 characters
   - Mix of letters, numbers, and symbols

2. **Create Dedicated Users**
   - Don't use SA account for applications
   - Grant minimal required permissions

3. **Enable Encryption**
   - Use SSL/TLS for connections
   - Encrypt sensitive data at rest

4. **Regular Updates**
   - Keep SQL Server updated
   - Apply security patches promptly

5. **Network Security**
   - Use firewalls to restrict access
   - Consider VPN for remote connections
