import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Leaf,
  Mail,
  Phone,
  MapPin,
  ArrowRight,
  Globe,
  Share2
} from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="bg-green-600 p-2 rounded-xl">
                <Leaf className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">Agri Connect</h3>
                <p className="text-gray-400 text-sm">Cameroon</p>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Connecting Cameroon's farmers directly with customers, promoting sustainable
              agriculture and supporting local communities across all 10 regions.
            </p>
            <div className="flex space-x-3">
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white hover:bg-gray-800 p-2 rounded-lg">
                <Globe className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white hover:bg-gray-800 p-2 rounded-lg">
                <Share2 className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white hover:bg-gray-800 p-2 rounded-lg">
                <Mail className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white hover:bg-gray-800 p-2 rounded-lg">
                <Phone className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/products" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Products
                </Link>
              </li>
              <li>
                <Link to="/farmers" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Our Farmers
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">Categories</h4>
            <ul className="space-y-3">
              <li>
                <Link to="/products?category=fruits" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Fresh Fruits
                </Link>
              </li>
              <li>
                <Link to="/products?category=vegetables" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Vegetables
                </Link>
              </li>
              <li>
                <Link to="/products?category=grains" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Grains & Cereals
                </Link>
              </li>
              <li>
                <Link to="/products?category=cocoa" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Cocoa & Coffee
                </Link>
              </li>
              <li>
                <Link to="/products?category=spices" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Spices & Herbs
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">Stay Updated</h4>
            <p className="text-gray-300 mb-6">
              Get the latest updates on fresh products and special offers delivered to your inbox.
            </p>
            <div className="space-y-4">
              <div className="flex">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  className="rounded-l-lg rounded-r-none border-gray-700 bg-gray-800 text-white placeholder-gray-400 focus:border-green-500 focus:ring-green-500"
                />
                <Button className="rounded-l-none rounded-r-lg bg-green-600 hover:bg-green-700 text-white px-4">
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </div>
              <p className="text-xs text-gray-400">
                By subscribing, you agree to our Privacy Policy and consent to receive updates.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Info */}
        <div className="border-t border-gray-800 mt-12 pt-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex items-start space-x-4">
              <div className="bg-green-600 p-3 rounded-lg">
                <MapPin className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="font-semibold text-white mb-1">Address</p>
                <p className="text-gray-300 text-sm leading-relaxed">
                  Douala, Littoral Region<br />
                  Cameroon
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="bg-green-600 p-3 rounded-lg">
                <Phone className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="font-semibold text-white mb-1">Phone</p>
                <p className="text-gray-300 text-sm">+237 6XX XXX XXX</p>
                <p className="text-gray-300 text-sm">+237 6XX XXX XXX</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="bg-green-600 p-3 rounded-lg">
                <Mail className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="font-semibold text-white mb-1">Email</p>
                <p className="text-gray-300 text-sm"><EMAIL></p>
                <p className="text-gray-300 text-sm"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 Agri Connect Cameroon. All rights reserved.
          </p>
          <div className="flex flex-wrap justify-center md:justify-end space-x-6 mt-4 md:mt-0">
            <Link to="/privacy" className="text-gray-400 hover:text-green-400 text-sm transition-colors duration-200">
              Privacy Policy
            </Link>
            <Link to="/terms" className="text-gray-400 hover:text-green-400 text-sm transition-colors duration-200">
              Terms of Service
            </Link>
            <Link to="/support" className="text-gray-400 hover:text-green-400 text-sm transition-colors duration-200">
              Support
            </Link>
            <Link to="/sitemap" className="text-gray-400 hover:text-green-400 text-sm transition-colors duration-200">
              Sitemap
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
