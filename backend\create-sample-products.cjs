// Create sample products for testing
const axios = require('axios');

async function createSampleProducts() {
  console.log('🌱 Creating Sample Products for Testing...\n');
  
  try {
    // Step 1: Register a farmer
    console.log('1. Registering sample farmer...');
    const farmerEmail = `farmer.${Date.now()}@example.com`;
    
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Sample',
      lastName: 'Farmer',
      email: farmerEmail,
      password: 'Test123!',
      role: 'FARMER',
      farmName: 'Green Valley Farm',
      farmLocation: 'Douala, Littoral'
    });
    
    if (!registerResponse.data.success) {
      console.log('❌ Farmer registration failed:', registerResponse.data.message);
      return;
    }
    
    const token = registerResponse.data.data.accessToken;
    console.log('✅ Farmer registered successfully');
    
    // Step 2: Get categories
    console.log('2. Getting categories...');
    const categoriesResponse = await axios.get('http://localhost:3001/api/categories');
    const categories = categoriesResponse.data.data;
    
    if (!categories || categories.length === 0) {
      console.log('❌ No categories found');
      return;
    }
    
    console.log(`✅ Found ${categories.length} categories`);
    
    // Step 3: Create sample products
    console.log('3. Creating sample products...');
    
    const sampleProducts = [
      {
        name: 'Fresh Red Tomatoes',
        description: 'Organic red tomatoes, freshly harvested from our farm. Perfect for cooking and salads.',
        price: 500,
        unit: 'kg',
        stock: 200,
        categoryName: 'Fruit Vegetables'
      },
      {
        name: 'Yellow Maize (Corn)',
        description: 'High-quality yellow corn, locally grown and properly dried. Ideal for various uses.',
        price: 400,
        unit: 'kg',
        stock: 500,
        categoryName: 'Cereals & Grains'
      },
      {
        name: 'Fresh Cassava Tubers',
        description: 'Sweet cassava variety, freshly harvested tubers. Rich in carbohydrates.',
        price: 300,
        unit: 'kg',
        stock: 300,
        categoryName: 'Root Crops & Tubers'
      },
      {
        name: 'Sweet Ripe Bananas',
        description: 'Ripe yellow bananas, perfect for eating fresh or cooking. Naturally sweet.',
        price: 250,
        unit: 'bunch',
        stock: 150,
        categoryName: 'Tropical Fruits'
      },
      {
        name: 'Fresh Green Spinach',
        description: 'Tender green spinach leaves, locally grown without chemicals. Rich in nutrients.',
        price: 400,
        unit: 'kg',
        stock: 80,
        categoryName: 'Leafy Greens'
      }
    ];
    
    let createdCount = 0;
    
    for (const productData of sampleProducts) {
      try {
        // Find the category
        const category = categories.find(cat => cat.name === productData.categoryName);
        
        if (!category) {
          console.log(`   ⚠️ Category "${productData.categoryName}" not found, skipping ${productData.name}`);
          continue;
        }
        
        // Create FormData for product creation
        const formData = new FormData();
        formData.append('name', productData.name);
        formData.append('description', productData.description);
        formData.append('price', productData.price.toString());
        formData.append('unit', productData.unit);
        formData.append('stock', productData.stock.toString());
        formData.append('categoryId', category.id);
        formData.append('minOrder', '1');
        
        // Create the product
        const productResponse = await axios.post('http://localhost:3001/api/products', formData, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          }
        });
        
        if (productResponse.data.success) {
          createdCount++;
          console.log(`   ✅ Created: ${productData.name} in ${category.name}`);
        } else {
          console.log(`   ❌ Failed to create ${productData.name}:`, productResponse.data.message);
        }
        
      } catch (productError) {
        console.log(`   ❌ Error creating ${productData.name}:`, productError.response?.data?.message || productError.message);
      }
    }
    
    // Step 4: Verify products were created
    console.log('\n4. Verifying products...');
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    
    if (productsResponse.data.success) {
      const totalProducts = productsResponse.data.data.total;
      console.log(`✅ Total products in database: ${totalProducts}`);
      
      if (totalProducts > 0) {
        console.log('\n🎉 Sample products created successfully!');
        console.log('📝 You can now:');
        console.log('   - Visit http://localhost:5173/products');
        console.log('   - See products displayed');
        console.log('   - Test category dropdown filtering');
        console.log('   - Add products to cart');
      }
    }
    
  } catch (error) {
    console.error('❌ Failed to create sample products:', error.response?.data || error.message);
  }
}

createSampleProducts();
