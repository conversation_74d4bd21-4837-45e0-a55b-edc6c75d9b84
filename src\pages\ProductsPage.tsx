import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import ProductCard from '@/components/ProductCard';
import {
  Search,
  Grid3X3,
  List,
  MapPin
} from 'lucide-react';
import { toast } from 'sonner';
import cartService from '@/services/cartService';
import wishlistService from '@/services/wishlistService';
import authService from '@/services/authService';

// API functions
const API_BASE = 'http://localhost:3001/api';

const fetchProducts = async (params: any = {}) => {
  const queryParams = new URLSearchParams();
  if (params.category) queryParams.append('category', params.category);
  if (params.search) queryParams.append('search', params.search);
  if (params.sort) queryParams.append('sort', params.sort);

  const response = await fetch(`${API_BASE}/products?${queryParams}`);
  if (!response.ok) throw new Error('Failed to fetch products');
  const result = await response.json();
  // Backend returns { success: true, data: { products: [...], pagination: {...} } }
  return result.success ? result.data.products : [];
};

const fetchCategories = async () => {
  const response = await fetch(`${API_BASE}/categories`);
  if (!response.ok) throw new Error('Failed to fetch categories');
  const result = await response.json();
  // Backend returns { success: true, data: [...] }
  return result.success ? result.data : [];
};

const ProductsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'name');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [wishlistItems, setWishlistItems] = useState<string[]>([]);
  const user = authService.getCurrentUser();

  // Fetch data from backend
  const { data: products = [], isLoading: productsLoading, refetch } = useQuery({
    queryKey: ['products', selectedCategory, searchQuery, sortBy],
    queryFn: () => fetchProducts({
      category: selectedCategory,
      search: searchQuery,
      sort: sortBy
    }),
    staleTime: 5 * 60 * 1000,
  });

  const { data: categories = [] } = useQuery({
    queryKey: ['categories'],
    queryFn: fetchCategories,
    staleTime: 10 * 60 * 1000,
  });

  useEffect(() => {
    const params = new URLSearchParams();
    if (searchQuery) params.set('search', searchQuery);
    if (selectedCategory) params.set('category', selectedCategory);
    if (sortBy !== 'name') params.set('sort', sortBy);
    setSearchParams(params);
  }, [searchQuery, selectedCategory, sortBy, setSearchParams]);

  // Load wishlist items on component mount
  useEffect(() => {
    const loadWishlist = async () => {
      if (user) {
        try {
          const response = await wishlistService.getWishlist();
          if (response.success) {
            const productIds = response.data.wishlist.map(item => String(item.ProductId));
            setWishlistItems(productIds);
          }
        } catch (error) {
          console.error('Failed to load wishlist:', error);
        }
      }
    };

    loadWishlist();
  }, [user]);



  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    refetch();
  };

  const handleAddToCart = async (productId: string) => {
    if (!user) {
      toast.error('Please login to add items to cart');
      return;
    }

    try {
      const response = await cartService.addToCart(productId, 1);
      if (response.success) {
        toast.success('Product added to cart!');
        window.dispatchEvent(new Event('cartUpdated'));
      } else {
        toast.error(response.message || 'Failed to add to cart');
      }
    } catch (error) {
      console.error('Add to cart error:', error);
      toast.error('Failed to add to cart');
    }
  };

  const handleAddToWishlist = async (productId: string) => {
    if (!user) {
      toast.error('Please login to add items to wishlist');
      return;
    }

    try {
      const isInWishlist = wishlistItems.includes(productId);

      if (isInWishlist) {
        const response = await wishlistService.removeFromWishlist(productId);
        if (response.success) {
          setWishlistItems(prev => prev.filter(itemId => itemId !== productId));
          toast.success('Removed from wishlist');
        }
      } else {
        const response = await wishlistService.addToWishlist(productId);
        if (response.success) {
          setWishlistItems(prev => [...prev, productId]);
          toast.success('Added to wishlist!');
        }
      }
    } catch (error) {
      console.error('Wishlist error:', error);
      toast.error('Failed to update wishlist');
    }
  };

  const handleCompare = (productId: string) => {
    toast.success('Product added to compare!');
    console.log(`Added product ${productId} to compare`);
  };

  const handleViewProduct = (productId: string) => {
    navigate(`/products/${productId}`);
  };

  // Modern ProductsPage with Nest-inspired design
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Products</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      {/* Page Header */}
      <div className="bg-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {selectedCategory ? categories.find((c: any) => c.id.toString() === selectedCategory)?.name : 'All Products'}
              </h1>
              <p className="text-gray-600 mt-1">
                Showing {products.length} products{searchQuery && <span> for "{searchQuery}"</span>}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-600">Cameroon</span>
            </div>
          </div>
        </div>
      </div>
      {/* Filters and Controls */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search Bar */}
            <form onSubmit={handleSearch} className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:ring-green-500"
                />
              </div>
            </form>

            {/* Controls */}
            <div className="flex items-center gap-4">
              {/* Category Filter */}
              <Select value={selectedCategory || "all"} onValueChange={(value) => setSelectedCategory(value === "all" ? "" : value)}>
                <SelectTrigger className="w-48 border-gray-300">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((category: any) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48 border-gray-300">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name A-Z</SelectItem>
                  <SelectItem value="price_asc">Price: Low to High</SelectItem>
                  <SelectItem value="price_desc">Price: High to Low</SelectItem>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="rating">Best Rating</SelectItem>
                </SelectContent>
              </Select>

              {/* View Mode */}
              <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-none border-0"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-none border-0"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Products Area - Full Width */}
        <div className="w-full">
          {/* Products Grid */}
          {productsLoading ? (
              <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                    <div className="bg-gray-200 h-4 rounded mb-2"></div>
                    <div className="bg-gray-200 h-4 rounded w-3/4"></div>
                  </div>
                ))}
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-gray-400 mb-4">
                  <Search className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
                <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
              </div>
            ) : (
              <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                {products.map((product: any) => (
                  <ProductCard
                    key={product.id}
                    id={product.id?.toString()}
                    name={product.name}
                    image={product.images && product.images.length > 0 ? product.images[0] : '/placeholder.svg'}
                    price={`${product.price?.toLocaleString() || '0'} FCFA`}
                    originalPrice={product.originalPrice ? `${product.originalPrice.toLocaleString()} FCFA` : undefined}
                    region={product.region || 'Cameroon'}
                    farmer={product.farmer || 'Local Farmer'}
                    rating={product.rating || 4.5}
                    reviewCount={product.reviewCount || 32}
                    category={product.Category?.name || 'Fresh Produce'}
                    inStock={product.stock > 0}
                    isOnSale={product.isOnSale}
                    isHot={product.isHot}
                    isNew={product.isNew}
                    discount={product.discount}
                    onAddToCart={handleAddToCart}
                    onAddToWishlist={handleAddToWishlist}
                    onCompare={handleCompare}
                    onViewProduct={handleViewProduct}
                  />
                ))}
              </div>
            )}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ProductsPage;
