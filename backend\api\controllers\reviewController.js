// Import necessary modules
const { Review, Product, User, Order } = require('../../data/models');
const { asyncHandler } = require('../../middleware/errorHandler');

// Create a new review
exports.createReview = asyncHandler(async (req, res) => {
  const { productId, rating, title, comment, orderId } = req.body;

  // Check if product exists
  const product = await Product.findByPk(productId);
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  // Check if user already reviewed this product
  const existingReview = await Review.findOne({
    where: {
      userId: req.user.id,
      productId: productId
    }
  });

  if (existingReview) {
    return res.status(400).json({
      success: false,
      message: 'You have already reviewed this product'
    });
  }

  // Check if this is a verified purchase
  let isVerifiedPurchase = false;
  if (orderId) {
    const order = await Order.findOne({
      where: {
        id: orderId,
        userId: req.user.id,
        status: 'DELIVERED'
      }
    });
    isVerifiedPurchase = !!order;
  }

  // Handle uploaded images
  const images = req.files ? req.files.map(file => `/uploads/reviews/${file.filename}`) : [];

  // Create the review
  const review = await Review.create({
    userId: req.user.id,
    productId,
    orderId: orderId || null,
    rating: parseInt(rating),
    title: title || null,
    comment,
    images,
    isVerifiedPurchase
  });

  // Update product rating
  const reviews = await Review.findAll({ where: { productId } });
  const avgRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

  await Product.update(
    {
      rating: Math.round(avgRating * 100) / 100,
      totalReviews: reviews.length
    },
    { where: { id: productId } }
  );

  // Fetch the created review with user info
  const createdReview = await Review.findByPk(review.id, {
    include: [
      {
        model: User,
        attributes: ['id', 'firstName', 'lastName', 'avatar']
      },
      {
        model: Product,
        attributes: ['id', 'name']
      }
    ]
  });

  res.status(201).json({
    success: true,
    data: createdReview,
    message: 'Review created successfully'
  });
});

// Get reviews for a specific product
exports.getReviewsForProduct = async (req, res, next) => {
  try {
    // Find all reviews for the product
    const reviews = await Review.findAll({
      where: { productId: req.params.productId },
      include: [{ model: Product, attributes: ['id', 'name'] }],
    });

    // Return the reviews
    res.json(reviews);
  } catch (error) {
    next(error);
  }
};

// Update a review
exports.updateReview = async (req, res, next) => {
  try {
    // Find the review by id and userId
    const review = await Review.findOne({
      where: { id: req.params.id, userId: req.user.id },
    });

    // If review not found, return 404
    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Update the review
    review.rating = req.body.rating || review.rating;
    review.comment = req.body.comment || review.comment;
    await review.save();

    // Return the updated review
    res.json(review);
  } catch (error) {
    next(error);
  }
};

// Delete a review
exports.deleteReview = async (req, res, next) => {
  try {
    // Find the review by id and userId
    const review = await Review.findOne({
      where: { id: req.params.id, userId: req.user.id },
    });

    // If review not found, return 404
    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Delete the review
    await review.destroy();

    // Return success message
    res.json({ message: 'Review deleted successfully' });
  } catch (error) {
    next(error);
  }
};