// Reset and create simplified categories
const mysql = require('mysql2/promise');

async function resetCategories() {
  console.log('🔄 Resetting to Simplified Agricultural Categories...\n');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to database');
    
    // Step 1: Clear existing categories and products
    console.log('1. Clearing existing categories and products...');
    
    await connection.execute('DELETE FROM products');
    await connection.execute('DELETE FROM categories');
    
    console.log('   ✅ Cleared existing data');
    
    // Step 2: Create simplified categories
    console.log('2. Creating simplified agricultural categories...');
    
    const categories = [
      {
        id: 'cat-cereals',
        name: 'Cereals & Grains',
        description: 'Maize, rice, millet, sorghum, wheat'
      },
      {
        id: 'cat-roots',
        name: 'Root Crops & Tubers',
        description: 'Cassava, yam, sweet potato, Irish potato, cocoyam, plantain'
      },
      {
        id: 'cat-fruits',
        name: 'Fruits',
        description: 'Banana, pineapple, mango, papaya, avocado, citrus fruits, bush mango'
      },
      {
        id: 'cat-leafy',
        name: 'Leafy Vegetables',
        description: 'Spinach, lettuce, cabbage, bitter leaf, water leaf'
      },
      {
        id: 'cat-vegetables',
        name: 'Other Vegetables',
        description: 'Tomato, pepper, onion, garlic, carrot, okra, cucumber'
      },
      {
        id: 'cat-legumes',
        name: 'Legumes & Beans',
        description: 'Groundnuts, cowpea, beans, soybeans, bambara groundnut'
      },
      {
        id: 'cat-cash',
        name: 'Cash Crops',
        description: 'Cocoa, coffee, cotton, palm oil'
      },
      {
        id: 'cat-spices',
        name: 'Spices & Herbs',
        description: 'Ginger, pepper, basil, lemongrass, mint, garlic'
      },
      {
        id: 'cat-livestock',
        name: 'Livestock',
        description: 'Cattle, goat, sheep, pig, poultry, duck, rabbit'
      },
      {
        id: 'cat-other',
        name: 'Other Products',
        description: 'Honey, mushrooms, medicinal plants'
      }
    ];
    
    // Insert categories
    for (const category of categories) {
      await connection.execute(`
        INSERT INTO categories (id, name, description, isActive, createdAt, updatedAt)
        VALUES (?, ?, ?, true, NOW(), NOW())
      `, [category.id, category.name, category.description]);
      
      console.log(`   ✅ Created: ${category.name}`);
    }
    
    // Step 3: Create sample farmer
    console.log('\n3. Creating sample farmer...');
    
    const userId = 'sample-user-123';
    const farmerId = 'sample-farmer-123';
    
    await connection.execute(`
      INSERT IGNORE INTO users (id, firstName, lastName, email, password, role, status, emailVerified, createdAt, updatedAt)
      VALUES (?, 'Sample', 'Farmer', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'FARMER', 'ACTIVE', true, NOW(), NOW())
    `, [userId]);
    
    await connection.execute(`
      INSERT IGNORE INTO farmers (id, userId, farmName, farmLocation, verified, rating, totalSales, createdAt, updatedAt)
      VALUES (?, ?, 'Green Valley Farm', 'Douala, Cameroon', true, 4.5, 0, NOW(), NOW())
    `, [farmerId, userId]);
    
    console.log('   ✅ Sample farmer created');
    
    // Step 4: Create sample products
    console.log('\n4. Creating sample products...');
    
    const products = [
      // Cereals & Grains
      ['Yellow Maize (Corn)', 'High-quality yellow corn, locally grown', 400, 'kg', 500, 'cat-cereals'],
      ['Premium White Rice', 'High quality lowland white rice', 800, 'kg', 200, 'cat-cereals'],
      
      // Root Crops & Tubers
      ['Fresh Cassava', 'Sweet cassava variety, freshly harvested', 300, 'kg', 300, 'cat-roots'],
      ['Sweet Yam', 'Fresh yam tubers, excellent for cooking', 450, 'kg', 200, 'cat-roots'],
      
      // Fruits
      ['Sweet Bananas', 'Ripe yellow bananas, perfect for eating', 250, 'bunch', 150, 'cat-fruits'],
      ['Fresh Pineapple', 'Sweet tropical pineapples, locally grown', 600, 'piece', 100, 'cat-fruits'],
      
      // Leafy Vegetables
      ['Fresh Spinach', 'Tender green spinach leaves, organic', 400, 'kg', 80, 'cat-leafy'],
      ['Green Cabbage', 'Fresh cabbage heads, locally grown', 300, 'head', 120, 'cat-leafy'],
      
      // Other Vegetables
      ['Fresh Tomatoes', 'Organic red tomatoes, freshly harvested', 500, 'kg', 200, 'cat-vegetables'],
      ['Red Onions', 'Fresh red bulb onions, locally grown', 700, 'kg', 150, 'cat-vegetables'],
      
      // Legumes & Beans
      ['Groundnuts (Peanuts)', 'Spanish variety groundnuts, fresh', 900, 'kg', 200, 'cat-legumes'],
      ['Black-eyed Peas', 'Fresh cowpeas, high protein content', 600, 'kg', 180, 'cat-legumes'],
      
      // Cash Crops
      ['Cocoa Beans', 'Premium cocoa beans, sun-dried', 2000, 'kg', 50, 'cat-cash'],
      ['Coffee Beans', 'Arabica coffee beans, mountain grown', 3000, 'kg', 30, 'cat-cash'],
      
      // Spices & Herbs
      ['Fresh Ginger', 'Yellow ginger variety, aromatic', 1500, 'kg', 80, 'cat-spices'],
      ['Hot Pepper', 'Fresh hot peppers, locally grown', 800, 'kg', 60, 'cat-spices'],
      
      // Other Products
      ['Pure Honey', 'Natural honey from local beehives', 2500, 'bottle', 40, 'cat-other'],
      ['Fresh Mushrooms', 'Oyster mushrooms, organically grown', 1200, 'kg', 25, 'cat-other']
    ];
    
    let createdCount = 0;
    
    for (let i = 0; i < products.length; i++) {
      const [name, description, price, unit, stock, categoryId] = products[i];
      const productId = 'product-' + (i + 1);
      
      try {
        await connection.execute(`
          INSERT INTO products (
            id, name, description, price, unit, stock, minOrder, images, status, 
            farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, 
            totalSold, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, 1, ?, 'ACTIVE', ?, ?, false, false, 0, 0, 0, NOW(), NOW())
        `, [
          productId, name, description, price, unit, stock,
          JSON.stringify(['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80']),
          farmerId, categoryId
        ]);
        
        createdCount++;
        console.log(`   ✅ Created: ${name}`);
        
      } catch (error) {
        console.log(`   ❌ Failed to create ${name}:`, error.message);
      }
    }
    
    console.log(`\n🎉 Reset completed successfully!`);
    console.log(`   Categories: ${categories.length} simplified agricultural categories`);
    console.log(`   Products: ${createdCount} sample products created`);
    console.log('\n📝 New categories:');
    categories.forEach((cat, index) => {
      console.log(`   ${index + 1}. ${cat.name}`);
    });
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Reset failed:', error.message);
  }
  
  process.exit(0);
}

resetCategories();
