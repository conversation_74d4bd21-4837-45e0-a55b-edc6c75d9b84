// Import required modules
const { Category, Product } = require('../../data/models');

// Define the CategoryController
const CategoryController = {
  // Create a new category
  async createCategory(req, res, next) {
    try {
      const { name } = req.body;
      const category = await Category.create({ name });
      res.status(201).json(category);
    } catch (error) {
      next(error);
    }
  },

  // Get all categories
  async getAllCategories(req, res, next) {
    try {
      const categories = await Category.findAll({
        where: { isActive: true },
        order: [['name', 'ASC']]
      });

      res.json({
        success: true,
        data: categories,
        message: 'Categories retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  },

  // Get a category by ID
  async getCategoryById(req, res, next) {
    try {
      const category = await Category.findByPk(req.params.id);
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      res.json(category);
    } catch (error) {
      next(error);
    }
  },

  // Update a category
  async updateCategory(req, res, next) {
    try {
      const { name } = req.body;
      const category = await Category.findByPk(req.params.id);
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      await category.update({ name });
      res.json(category);
    } catch (error) {
      next(error);
    }
  },

  // Delete a category
  async deleteCategory(req, res, next) {
    try {
      const category = await Category.findByPk(req.params.id);
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      await category.destroy();
      res.json({ message: 'Category deleted successfully' });
    } catch (error) {
      next(error);
    }
  }
};

// Export the CategoryController
module.exports = CategoryController;