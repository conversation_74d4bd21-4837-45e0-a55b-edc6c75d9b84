import React, { useEffect, useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { ordersAPI } from "@/services/api";
import OrderCard from "@/components/OrderCard";
import { Loader2 } from "lucide-react";

const OrdersPage: React.FC = () => {
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrders = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await ordersAPI.getAll();
        // Support both { success, data: { orders } } and array response
        let ordersList = Array.isArray(response)
          ? response
          : response?.data?.orders || response?.orders || [];
        setOrders(ordersList);
      } catch (err: any) {
        setError("Failed to load orders. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchOrders();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-emerald-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold mb-4">My Orders</h1>
          <p className="text-green-100 text-lg">Track and manage your orders</p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {loading ? (
          <div className="flex justify-center items-center py-24">
            <div className="text-center">
              <Loader2 className="w-12 h-12 text-green-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading your orders...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-lg p-8 max-w-md mx-auto">
              <p className="text-red-600 font-semibold mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-semibold"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-16">
            <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">No Orders Yet</h3>
              <p className="text-gray-600 mb-6">You haven't placed any orders yet. Start shopping to see your orders here.</p>
              <a
                href="/products"
                className="inline-block bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg px-8 py-3 transition-colors"
              >
                Shop Products
              </a>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {orders.map((order: any) => (
              <OrderCard
                key={order.id}
                orderId={order.id}
                date={order.createdAt ? new Date(order.createdAt).toLocaleDateString() : ""}
                status={order.status?.toLowerCase?.() || "pending"}
                items={order.OrderItems?.map((item: any) => ({
                  name: item.Product?.name || "Product",
                  quantity: item.quantity,
                  price: item.price,
                  farmer: item.Product?.Farmer?.username || "Farmer"
                })) || []}
                total={order.total || order.totalAmount || 0}
                deliveryAddress={order.shippingAddress || "N/A"}
                estimatedDelivery={order.estimatedDelivery}
                trackingNumber={order.trackingNumber}
              />
            ))}
          </div>
        )}
      </div>
      <Footer />
    </div>
  );
};

export default OrdersPage;