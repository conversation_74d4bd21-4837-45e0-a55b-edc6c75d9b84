// Debug orders issue
const mysql = require('mysql2/promise');
const axios = require('axios');

async function debugOrders() {
  console.log('🔍 Debugging Orders Issue...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    // Check if orders exist in database
    console.log('📦 Checking Orders in Database:');
    const [orders] = await connection.execute(`
      SELECT o.id, o.orderNumber, o.totalAmount, o.status, o.createdAt,
             u.firstName, u.lastName, u.email
      FROM orders o
      LEFT JOIN users u ON o.userId = u.id
      ORDER BY o.createdAt DESC
      LIMIT 5
    `);
    
    if (orders.length > 0) {
      console.log(`✅ Found ${orders.length} orders in database:`);
      orders.forEach(order => {
        console.log(`   - Order ${order.orderNumber}: ${order.totalAmount} FCFA (${order.status})`);
        console.log(`     User: ${order.firstName} ${order.lastName} (${order.email})`);
        console.log(`     Created: ${order.createdAt}`);
        console.log('');
      });
    } else {
      console.log('❌ No orders found in database');
    }
    
    // Check order items
    console.log('📋 Checking Order Items:');
    const [orderItems] = await connection.execute(`
      SELECT oi.id, oi.orderId, oi.quantity, oi.price, oi.total,
             p.name as productName, o.orderNumber
      FROM order_items oi
      LEFT JOIN products p ON oi.productId = p.id
      LEFT JOIN orders o ON oi.orderId = o.id
      ORDER BY oi.createdAt DESC
      LIMIT 5
    `);
    
    if (orderItems.length > 0) {
      console.log(`✅ Found ${orderItems.length} order items:`);
      orderItems.forEach(item => {
        console.log(`   - ${item.productName}: ${item.quantity}x ${item.price} = ${item.total} FCFA`);
        console.log(`     Order: ${item.orderNumber}`);
        console.log('');
      });
    } else {
      console.log('❌ No order items found');
    }
    
    // Test orders API endpoint
    console.log('🌐 Testing Orders API:');
    
    // First, create a test user and login
    try {
      const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
        firstName: 'Order',
        lastName: 'Test',
        email: `order.test.${Date.now()}@example.com`,
        password: 'Test123!',
        role: 'CUSTOMER'
      });
      
      const token = registerResponse.data.data.accessToken;
      console.log('✅ Test user created and logged in');
      
      // Test get orders API
      try {
        const ordersResponse = await axios.get('http://localhost:3001/api/orders', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        console.log('✅ Orders API working');
        console.log('   Response structure:', Object.keys(ordersResponse.data));
        console.log('   Orders count:', ordersResponse.data.data?.orders?.length || 0);
        
      } catch (ordersApiError) {
        console.log('❌ Orders API failed:');
        console.log('   Status:', ordersApiError.response?.status);
        console.log('   Error:', ordersApiError.response?.data?.message || ordersApiError.message);
      }
      
    } catch (authError) {
      console.log('❌ Authentication failed:', authError.response?.data || authError.message);
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugOrders();
