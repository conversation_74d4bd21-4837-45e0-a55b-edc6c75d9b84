// Test cart API endpoints
const axios = require('axios');

async function testCartAPI() {
  console.log('🧪 Testing Cart API Endpoints...\n');
  
  try {
    // Test if the cart endpoint exists (without auth)
    console.log('1. Testing cart endpoint accessibility...');
    try {
      const response = await axios.get('http://localhost:3001/api/cart');
      console.log('✅ Cart endpoint accessible');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Cart endpoint exists (requires authentication)');
      } else if (error.response?.status === 404) {
        console.log('❌ Cart endpoint not found (404)');
        console.log('   Error:', error.response?.data);
      } else {
        console.log('⚠️ Cart endpoint error:', error.response?.status, error.response?.data);
      }
    }
    
    // Test other API endpoints for comparison
    console.log('\n2. Testing other API endpoints...');
    
    try {
      const productsResponse = await axios.get('http://localhost:3001/api/products');
      console.log('✅ Products endpoint working');
    } catch (error) {
      console.log('❌ Products endpoint failed:', error.response?.status);
    }
    
    try {
      const categoriesResponse = await axios.get('http://localhost:3001/api/categories');
      console.log('✅ Categories endpoint working');
    } catch (error) {
      console.log('❌ Categories endpoint failed:', error.response?.status);
    }
    
    // Test with authentication
    console.log('\n3. Testing with authentication...');
    
    // First register a test user
    try {
      const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
        firstName: 'Cart',
        lastName: 'Test',
        email: `cart.test.${Date.now()}@example.com`,
        password: 'Test123!',
        role: 'CUSTOMER'
      });
      
      const token = registerResponse.data.data.accessToken;
      console.log('✅ Test user created and logged in');
      
      // Now test cart with auth
      try {
        const cartResponse = await axios.get('http://localhost:3001/api/cart', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        console.log('✅ Cart API working with authentication');
        console.log('   Response:', cartResponse.data);
      } catch (cartError) {
        console.log('❌ Cart API failed with authentication');
        console.log('   Status:', cartError.response?.status);
        console.log('   Error:', cartError.response?.data);
      }
      
      // Test add to cart
      try {
        const productsResponse = await axios.get('http://localhost:3001/api/products');
        const products = productsResponse.data.data.products;
        
        if (products.length > 0) {
          const testProduct = products[0];
          
          const addCartResponse = await axios.post('http://localhost:3001/api/cart/add', {
            productId: testProduct.id,
            quantity: 1
          }, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          console.log('✅ Add to cart working');
          console.log('   Response:', addCartResponse.data);
        }
      } catch (addError) {
        console.log('❌ Add to cart failed');
        console.log('   Status:', addError.response?.status);
        console.log('   Error:', addError.response?.data);
      }
      
    } catch (authError) {
      console.log('❌ Authentication failed:', authError.response?.data);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testCartAPI();
