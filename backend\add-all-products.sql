-- Add all 56 products with correct images
USE cameroon_farm_connect;

-- Insert all products (8 per category = 56 total)

-- Cereals & Grains (8 products)
INSERT INTO products (id, name, description, price, unit, stock, minOrder, images, status, farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, totalSold, createdAt, updatedAt) VALUES
('prod-cat-cereals-1', 'Yellow Maize (Corn)', 'High-quality yellow corn, locally grown and dried', 400, 'kg', 500, 1, '["https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cereals', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cereals-2', 'Premium White Rice', 'High quality lowland white rice, locally grown', 800, 'kg', 200, 1, '["https://images.unsplash.com/photo-1586201375761-83865001e31c?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cereals', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cereals-3', 'Red Sorghum', 'Traditional red sorghum variety, excellent for porridge', 550, 'kg', 300, 1, '["https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cereals', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cereals-4', 'Pearl Millet', 'Nutritious pearl millet grains, drought-resistant crop', 600, 'kg', 250, 1, '["https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cereals', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cereals-5', 'Wheat Flour', 'Fresh ground wheat flour, locally processed', 700, 'kg', 150, 1, '["https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cereals', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cereals-6', 'Brown Rice', 'Organic brown rice, unpolished and nutritious', 900, 'kg', 180, 1, '["https://images.unsplash.com/photo-1586201375761-83865001e31c?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cereals', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cereals-7', 'White Maize', 'Premium white corn variety, sweet and tender', 450, 'kg', 400, 1, '["https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cereals', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cereals-8', 'Fonio Grain', 'Ancient West African superfood grain, gluten-free', 1200, 'kg', 100, 1, '["https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cereals', FALSE, FALSE, 0, 0, 0, NOW(), NOW());

-- Root Crops & Tubers (8 products)
INSERT INTO products (id, name, description, price, unit, stock, minOrder, images, status, farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, totalSold, createdAt, updatedAt) VALUES
('prod-cat-roots-1', 'Fresh Cassava', 'Sweet cassava variety, freshly harvested tubers', 300, 'kg', 500, 1, '["https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-roots', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-roots-2', 'Sweet Yam', 'Fresh yam tubers, excellent for cooking and frying', 450, 'kg', 300, 1, '["https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-roots', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-roots-3', 'Irish Potatoes', 'Fresh Irish potatoes, perfect for various dishes', 600, 'kg', 250, 1, '["https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-roots', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-roots-4', 'Sweet Potatoes', 'Orange-fleshed sweet potatoes, rich in vitamins', 400, 'kg', 350, 1, '["https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-roots', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-roots-5', 'Cocoyam (Taro)', 'Fresh cocoyam tubers, traditional Cameroon staple', 500, 'kg', 200, 1, '["https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-roots', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-roots-6', 'Ripe Plantains', 'Sweet ripe plantains, ready for cooking', 350, 'bunch', 180, 1, '["https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-roots', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-roots-7', 'Green Plantains', 'Unripe plantains, perfect for boiling and frying', 300, 'bunch', 220, 1, '["https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-roots', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-roots-8', 'Water Yam', 'Large water yam variety, excellent for pounding', 550, 'kg', 150, 1, '["https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-roots', FALSE, FALSE, 0, 0, 0, NOW(), NOW());

-- Fruits (8 products)
INSERT INTO products (id, name, description, price, unit, stock, minOrder, images, status, farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, totalSold, createdAt, updatedAt) VALUES
('prod-cat-fruits-1', 'Sweet Bananas', 'Ripe yellow bananas, perfect for eating fresh', 250, 'bunch', 200, 1, '["https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-fruits', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-fruits-2', 'Fresh Pineapple', 'Sweet tropical pineapples, locally grown', 600, 'piece', 150, 1, '["https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-fruits', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-fruits-3', 'Ripe Mangoes', 'Sweet Julie mangoes, locally grown and organic', 400, 'kg', 180, 1, '["https://images.unsplash.com/photo-1553279768-865429fa0078?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-fruits', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-fruits-4', 'Fresh Papaya', 'Ripe papayas, sweet and nutritious', 350, 'piece', 120, 1, '["https://images.unsplash.com/photo-1617112848923-cc2234396a8d?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-fruits', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-fruits-5', 'Avocados', 'Fresh avocados, creamy and nutritious', 800, 'kg', 100, 1, '["https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-fruits', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-fruits-6', 'Bush Mango', 'Wild bush mango, traditional Cameroon fruit', 1000, 'kg', 80, 1, '["https://images.unsplash.com/photo-1553279768-865429fa0078?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-fruits', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-fruits-7', 'Oranges', 'Sweet Valencia oranges, juicy and fresh', 300, 'kg', 250, 1, '["https://images.unsplash.com/photo-1547514701-42782101795e?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-fruits', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-fruits-8', 'Lemons', 'Fresh lemons, perfect for cooking and drinks', 500, 'kg', 150, 1, '["https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-fruits', FALSE, FALSE, 0, 0, 0, NOW(), NOW());

-- Vegetables (8 products)
INSERT INTO products (id, name, description, price, unit, stock, minOrder, images, status, farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, totalSold, createdAt, updatedAt) VALUES
('prod-cat-vegetables-1', 'Fresh Tomatoes', 'Organic red tomatoes, freshly harvested', 500, 'kg', 300, 1, '["https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-vegetables', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-vegetables-2', 'Fresh Spinach', 'Tender green spinach leaves, organically grown', 400, 'kg', 150, 1, '["https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-vegetables', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-vegetables-3', 'Red Onions', 'Fresh red bulb onions, locally grown', 700, 'kg', 200, 1, '["https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-vegetables', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-vegetables-4', 'Green Cabbage', 'Fresh cabbage heads, crisp and nutritious', 300, 'head', 180, 1, '["https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-vegetables', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-vegetables-5', 'Fresh Carrots', 'Orange carrots, sweet and crunchy', 450, 'kg', 160, 1, '["https://images.unsplash.com/photo-1445282768818-728615cc910a?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-vegetables', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-vegetables-6', 'Green Peppers', 'Fresh green bell peppers, locally grown', 600, 'kg', 140, 1, '["https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-vegetables', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-vegetables-7', 'Fresh Okra', 'Tender okra pods, perfect for soups', 350, 'kg', 120, 1, '["https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-vegetables', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-vegetables-8', 'Cucumber', 'Fresh cucumbers, crisp and refreshing', 400, 'kg', 100, 1, '["https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-vegetables', FALSE, FALSE, 0, 0, 0, NOW(), NOW());

-- Legumes & Beans (8 products)
INSERT INTO products (id, name, description, price, unit, stock, minOrder, images, status, farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, totalSold, createdAt, updatedAt) VALUES
('prod-cat-legumes-1', 'Groundnuts (Peanuts)', 'Spanish variety groundnuts, freshly harvested', 900, 'kg', 300, 1, '["https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-legumes', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-legumes-2', 'Black-eyed Peas', 'Fresh cowpeas, high protein content', 600, 'kg', 250, 1, '["https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-legumes', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-legumes-3', 'Red Kidney Beans', 'Premium red kidney beans, locally grown', 800, 'kg', 200, 1, '["https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-legumes', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-legumes-4', 'Soybeans', 'High-protein soybeans, excellent for processing', 700, 'kg', 280, 1, '["https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-legumes', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-legumes-5', 'White Beans', 'Fresh white beans, tender and nutritious', 650, 'kg', 220, 1, '["https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-legumes', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-legumes-6', 'Bambara Groundnuts', 'Traditional bambara nuts, drought-resistant', 1000, 'kg', 150, 1, '["https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-legumes', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-legumes-7', 'Green Beans', 'Fresh green beans, tender and crisp', 500, 'kg', 180, 1, '["https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-legumes', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-legumes-8', 'Lima Beans', 'Large lima beans, creamy and delicious', 750, 'kg', 160, 1, '["https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-legumes', FALSE, FALSE, 0, 0, 0, NOW(), NOW());

-- Cash Crops (8 products)
INSERT INTO products (id, name, description, price, unit, stock, minOrder, images, status, farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, totalSold, createdAt, updatedAt) VALUES
('prod-cat-cash-1', 'Cocoa Beans', 'Premium cocoa beans, sun-dried and fermented', 2000, 'kg', 100, 1, '["https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cash', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cash-2', 'Coffee Beans', 'Arabica coffee beans, mountain grown', 3000, 'kg', 80, 1, '["https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cash', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cash-3', 'Raw Cotton', 'High-quality cotton fiber, locally grown', 1500, 'kg', 120, 1, '["https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cash', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cash-4', 'Palm Oil', 'Fresh red palm oil, traditionally processed', 1200, 'liter', 150, 1, '["https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cash', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cash-5', 'Palm Kernels', 'Fresh palm kernels for oil extraction', 800, 'kg', 200, 1, '["https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cash', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cash-6', 'Rubber Latex', 'Fresh rubber latex from mature trees', 1000, 'liter', 100, 1, '["https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cash', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cash-7', 'Cocoa Pods', 'Fresh cocoa pods for processing', 500, 'kg', 180, 1, '["https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cash', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-cash-8', 'Coffee Cherries', 'Fresh coffee cherries, ready for processing', 1800, 'kg', 90, 1, '["https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-cash', FALSE, FALSE, 0, 0, 0, NOW(), NOW());

-- Spices & Herbs (8 products)
INSERT INTO products (id, name, description, price, unit, stock, minOrder, images, status, farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, totalSold, createdAt, updatedAt) VALUES
('prod-cat-spices-1', 'Fresh Ginger', 'Yellow ginger variety, aromatic and spicy', 1500, 'kg', 120, 1, '["https://images.unsplash.com/photo-1599639832862-bd197d5c1b5d?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-spices', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-spices-2', 'Hot Pepper', 'Fresh hot peppers, locally grown and spicy', 800, 'kg', 100, 1, '["https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-spices', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-spices-3', 'Fresh Garlic', 'Local garlic bulbs, aromatic and flavorful', 1200, 'kg', 80, 1, '["https://images.unsplash.com/photo-1553978297-833d24758ba5?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-spices', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-spices-4', 'Fresh Basil', 'Sweet basil leaves, perfect for cooking', 600, 'kg', 60, 1, '["https://images.unsplash.com/photo-1618375569909-3c8616cf7733?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-spices', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-spices-5', 'Lemongrass', 'Fresh lemongrass stalks, aromatic herb', 400, 'kg', 90, 1, '["https://images.unsplash.com/photo-1618375569909-3c8616cf7733?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-spices', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-spices-6', 'Fresh Mint', 'Spearmint leaves, refreshing and aromatic', 500, 'kg', 70, 1, '["https://images.unsplash.com/photo-1618375569909-3c8616cf7733?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-spices', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-spices-7', 'Turmeric Root', 'Fresh turmeric roots, anti-inflammatory spice', 1000, 'kg', 85, 1, '["https://images.unsplash.com/photo-1599639832862-bd197d5c1b5d?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-spices', FALSE, FALSE, 0, 0, 0, NOW(), NOW()),
('prod-cat-spices-8', 'Scotch Bonnet Peppers', 'Very hot peppers, traditional variety', 1500, 'kg', 50, 1, '["https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=500&h=400&fit=crop&q=80"]', 'ACTIVE', 'farmer-001', 'cat-spices', FALSE, FALSE, 0, 0, 0, NOW(), NOW());

-- All products added successfully!
SELECT 'All 56 products added successfully!' as status;
