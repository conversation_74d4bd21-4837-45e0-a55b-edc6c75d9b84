import authService from './authService';

const API_BASE = 'http://localhost:3001/api';

export interface CartItem {
  id: number;
  quantity: number;
  price: number;
  Product: {
    id: number;
    name: string;
    description: string;
    price: number;
    stock: number;
    Category?: {
      id: number;
      name: string;
    };
    Farmer?: {
      id: number;
      username: string;
      email: string;
    };
  };
}

export interface Cart {
  id: number;
  items: CartItem[];
  itemCount: number;
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
}

export interface CartResponse {
  success: boolean;
  data: {
    cart: Cart;
  };
  message?: string;
}

class CartService {
  // Get user's cart
  async getCart(): Promise<CartResponse> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/cart`);
      return await response.json();
    } catch (error) {
      console.error('Get cart error:', error);
      throw new Error('Failed to fetch cart');
    }
  }

  // Add item to cart
  async addToCart(productId: string, quantity: number = 1): Promise<any> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/cart/add`, {
        method: 'POST',
        body: JSON.stringify({
          productId,
          quantity,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Add to cart error:', error);
      throw new Error('Failed to add item to cart');
    }
  }

  // Update cart item quantity
  async updateCartItem(itemId: number, quantity: number): Promise<any> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/cart/update/${itemId}`, {
        method: 'PUT',
        body: JSON.stringify({
          quantity,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Update cart item error:', error);
      throw new Error('Failed to update cart item');
    }
  }

  // Remove item from cart
  async removeFromCart(itemId: number): Promise<any> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/cart/remove/${itemId}`, {
        method: 'DELETE',
      });

      return await response.json();
    } catch (error) {
      console.error('Remove from cart error:', error);
      throw new Error('Failed to remove item from cart');
    }
  }

  // Clear entire cart
  async clearCart(): Promise<any> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/cart/clear`, {
        method: 'DELETE',
      });

      return await response.json();
    } catch (error) {
      console.error('Clear cart error:', error);
      throw new Error('Failed to clear cart');
    }
  }

  // Get cart item count (for navbar badge)
  async getCartItemCount(): Promise<number> {
    try {
      const cartResponse = await this.getCart();
      if (cartResponse.success) {
        return cartResponse.data.cart.itemCount || 0;
      }
      return 0;
    } catch (error) {
      console.error('Get cart count error:', error);
      return 0;
    }
  }
}

// Create and export a singleton instance
const cartService = new CartService();
export default cartService;
