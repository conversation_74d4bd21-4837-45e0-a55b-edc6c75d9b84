// Test cart and wishlist with fresh user
const axios = require('axios');

async function testWithFreshUser() {
  console.log('🧪 Testing Cart and Wishlist with Fresh User...\n');
  
  try {
    // Create a fresh user
    console.log('👤 Creating fresh user...');
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Test',
      lastName: 'User',
      email: `test.user.${Date.now()}@example.com`,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    const token = registerResponse.data.data.accessToken;
    const user = registerResponse.data.data.user;
    console.log(`✅ User created: ${user.firstName} ${user.lastName} (${user.email})\n`);
    
    // Get products
    console.log('📦 Getting products...');
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products;
    console.log(`✅ Found ${products.length} products\n`);
    
    if (products.length === 0) {
      console.log('❌ No products found');
      return;
    }
    
    const testProduct = products[0];
    console.log(`🎯 Testing with: ${testProduct.name}\n`);
    
    // Test Cart API endpoints
    console.log('🛒 Testing Cart Endpoints...');
    
    // 1. Add to Cart
    try {
      const addCartResponse = await axios.post('http://localhost:3001/api/cart/add', {
        productId: testProduct.id,
        quantity: 2
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Add to cart:', addCartResponse.data.success ? 'SUCCESS' : 'FAILED');
    } catch (error) {
      console.log('❌ Add to cart error:', error.response?.data?.message || error.message);
    }
    
    // 2. Get Cart
    try {
      const getCartResponse = await axios.get('http://localhost:3001/api/cart', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Get cart:', getCartResponse.data.success ? 'SUCCESS' : 'FAILED');
      if (getCartResponse.data.success) {
        console.log(`   Cart items: ${getCartResponse.data.data.cart?.itemCount || 0}`);
      }
    } catch (error) {
      console.log('❌ Get cart error:', error.response?.data?.message || error.message);
    }
    
    // Test Wishlist API endpoints
    console.log('\n❤️ Testing Wishlist Endpoints...');
    
    // 1. Add to Wishlist
    try {
      const addWishlistResponse = await axios.post('http://localhost:3001/api/wishlist/add', {
        productId: testProduct.id
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Add to wishlist:', addWishlistResponse.data.success ? 'SUCCESS' : 'FAILED');
    } catch (error) {
      console.log('❌ Add to wishlist error:', error.response?.data?.message || error.message);
    }
    
    // 2. Get Wishlist
    try {
      const getWishlistResponse = await axios.get('http://localhost:3001/api/wishlist', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Get wishlist:', getWishlistResponse.data.success ? 'SUCCESS' : 'FAILED');
      if (getWishlistResponse.data.success) {
        console.log(`   Wishlist items: ${getWishlistResponse.data.data?.length || 0}`);
      }
    } catch (error) {
      console.log('❌ Get wishlist error:', error.response?.data?.message || error.message);
    }
    
    console.log('\n🎉 Tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testWithFreshUser();
