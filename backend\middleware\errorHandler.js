const { ValidationError } = require('sequelize');

// Error handling middleware
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;
  
  // Log error for debugging
  console.error('Error:', {
    name: err.name,
    message: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
  
  // Sequelize validation error
  if (err instanceof ValidationError) {
    const message = err.errors.map(e => e.message).join(', ');
    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message,
      errors: err.errors
    });
  }
  
  // Sequelize unique constraint error
  if (err.name === 'SequelizeUniqueConstraintError') {
    const message = err.errors.map(e => e.message).join(', ');
    return res.status(400).json({
      success: false,
      error: 'Unique Constraint Error',
      message,
      errors: err.errors
    });
  }
  
  // Sequelize foreign key constraint error
  if (err.name === 'SequelizeForeignKeyConstraintError') {
    return res.status(400).json({
      success: false,
      error: 'Foreign Key Constraint Error',
      message: 'The referenced record does not exist'
    });
  }
  
  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      error: 'Invalid Token',
      message: 'Please log in again'
    });
  }
  
  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      error: 'Token Expired',
      message: 'Your session has expired. Please log in again'
    });
  }
  
  // Custom API error
  if (err.name === 'ApiError') {
    return res.status(err.statusCode || 500).json({
      success: false,
      error: err.name,
      message: err.message
    });
  }
  
  // Default to 500 server error
  return res.status(error.statusCode || 500).json({
    success: false,
    error: 'Server Error',
    message: error.message || 'Something went wrong on the server',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

// Custom API Error class
class ApiError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
    this.name = 'ApiError';
    Error.captureStackTrace(this, this.constructor);
  }
}

// Async handler to avoid try-catch blocks
const asyncHandler = (fn) => (req, res, next) =>
  Promise.resolve(fn(req, res, next)).catch(next);

// 404 handler
const notFound = (req, res, next) => {
  const error = new ApiError(`Not found - ${req.originalUrl}`, 404);
  next(error);
};

// Validation error formatter
const formatValidationErrors = (errors) => {
  return errors.map(error => ({
    field: error.param,
    message: error.msg,
    value: error.value
  }));
};

module.exports = {
  errorHandler,
  ApiError,
  asyncHandler,
  notFound,
  formatValidationErrors
};