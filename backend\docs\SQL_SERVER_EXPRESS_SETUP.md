# SQL Server Express Setup Guide

This guide will walk you through setting up SQL Server Express for the Cameroon Farm Connect Hub project.

## Why SQL Server Express?

- ✅ **Free** - No licensing costs
- ✅ **Lightweight** - Perfect for development
- ✅ **Full SQL Server features** - Same engine as full SQL Server
- ✅ **Easy to install** - Simple setup process
- ✅ **Windows integrated** - Works seamlessly with Windows

## Step 1: Download SQL Server Express

1. Go to [Microsoft SQL Server Downloads](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)
2. Scroll down to **SQL Server Express** section
3. Click **Download now** for SQL Server 2022 Express

## Step 2: Install SQL Server Express

### Basic Installation (Recommended)

1. **Run the installer** (`SQL2022-SSEI-Expr.exe`)
2. Choose **Basic** installation type
3. **Accept** the license terms
4. Choose installation location (default is fine)
5. Wait for download and installation to complete
6. **Note the connection string** shown at the end

### Custom Installation (Advanced)

If you need specific configurations:

1. Choose **Custom** installation
2. Download the installation media
3. Run `SETUP.EXE` from the downloaded folder
4. Follow the installation wizard:
   - **Feature Selection**: Database Engine Services (required)
   - **Instance Configuration**: 
     - Named instance: `SQLEXPRESS` (default)
     - Instance ID: `SQLEXPRESS`
   - **Server Configuration**: Use default settings
   - **Database Engine Configuration**:
     - Authentication Mode: **Mixed Mode** (recommended)
     - Set SA password: Use a strong password
     - Add current user as SQL Server Administrator

## Step 3: Install SQL Server Management Studio (SSMS)

1. Download SSMS from [Microsoft SSMS Download](https://docs.microsoft.com/en-us/sql/ssms/download-sql-server-management-studio-ssms)
2. Run the installer (`SSMS-Setup-ENU.exe`)
3. Follow the installation wizard
4. Restart your computer if prompted

## Step 4: Configure SQL Server Express

### Enable SQL Server Browser (Important)

1. Open **SQL Server Configuration Manager**
   - Search for "SQL Server Configuration Manager" in Start menu
2. Go to **SQL Server Services**
3. Right-click **SQL Server Browser** → **Properties**
4. Set **Start Mode** to **Automatic**
5. Click **OK** and start the service

### Enable TCP/IP Protocol

1. In **SQL Server Configuration Manager**
2. Go to **SQL Server Network Configuration** → **Protocols for SQLEXPRESS**
3. Right-click **TCP/IP** → **Enable**
4. Right-click **TCP/IP** → **Properties**
5. Go to **IP Addresses** tab
6. Scroll to **IPAll** section at the bottom
7. Set **TCP Port** to `1433` (if you want to use standard port)
8. Click **OK**
9. **Restart SQL Server service**

### Enable Mixed Mode Authentication

1. Open **SQL Server Management Studio (SSMS)**
2. Connect to your SQL Server Express instance:
   - Server name: `localhost\SQLEXPRESS` or `.\SQLEXPRESS`
   - Authentication: Windows Authentication
3. Right-click server name → **Properties**
4. Go to **Security** page
5. Select **SQL Server and Windows Authentication mode**
6. Click **OK**
7. **Restart SQL Server service**

### Enable SA Account (if using SQL Authentication)

1. In SSMS, expand **Security** → **Logins**
2. Right-click **sa** → **Properties**
3. Go to **General** tab:
   - Set a strong password
   - Uncheck "Enforce password policy" (for development only)
4. Go to **Status** tab:
   - Set Login: **Enabled**
5. Click **OK**

## Step 5: Create Database

### Using SSMS (Recommended)

1. Open **SQL Server Management Studio**
2. Connect to `localhost\SQLEXPRESS`
3. Right-click **Databases** → **New Database**
4. Database name: `cameroon_farm_connect`
5. Click **OK**

### Using SQL Commands

```sql
-- Connect to master database first
USE master;
GO

-- Create the database
CREATE DATABASE cameroon_farm_connect;
GO

-- Verify database creation
SELECT name FROM sys.databases WHERE name = 'cameroon_farm_connect';
GO
```

## Step 6: Configure Your Project

### Update .env File

Create a `.env` file in your backend folder:

```env
# SQL Server Express with SA authentication
DATABASE_URL="sqlserver://localhost\\SQLEXPRESS;database=cameroon_farm_connect;user=sa;password=YourStrongPassword123;trustServerCertificate=true;encrypt=true"

# OR SQL Server Express with Windows Authentication (easier for development)
# DATABASE_URL="sqlserver://localhost\\SQLEXPRESS;database=cameroon_farm_connect;integratedSecurity=true;trustServerCertificate=true;encrypt=true"

# Connection details
SQL_SERVER_HOST=localhost\\SQLEXPRESS
SQL_SERVER_DATABASE=cameroon_farm_connect
SQL_SERVER_USER=sa
SQL_SERVER_PASSWORD=YourStrongPassword123
SQL_SERVER_ENCRYPT=true
SQL_SERVER_TRUST_CERT=true
```

### Test Connection

Run this command to test your connection:

```bash
npm run setup:sqlserver
```

## Step 7: Initialize Database Schema

```bash
# Install dependencies
npm install

# Generate Prisma client
npm run db:generate

# Create and apply migrations
npm run migrate

# Seed the database with sample data
npm run seed
```

## Step 8: Start Development

```bash
npm run dev
```

Your API should now be running on `http://localhost:5000` with SQL Server Express!

## Common Connection Strings

### Named Instance (Default)
```
sqlserver://localhost\\SQLEXPRESS;database=cameroon_farm_connect;user=sa;password=YourPassword;trustServerCertificate=true;encrypt=true
```

### Windows Authentication
```
sqlserver://localhost\\SQLEXPRESS;database=cameroon_farm_connect;integratedSecurity=true;trustServerCertificate=true;encrypt=true
```

### Custom Port (if configured)
```
sqlserver://localhost:1433;database=cameroon_farm_connect;user=sa;password=YourPassword;trustServerCertificate=true;encrypt=true
```

## Troubleshooting

### Cannot Connect to Server

1. **Check if SQL Server is running**:
   - Open Services (services.msc)
   - Look for "SQL Server (SQLEXPRESS)"
   - Make sure it's running

2. **Check SQL Server Browser**:
   - Ensure "SQL Server Browser" service is running
   - This is required for named instances

3. **Verify instance name**:
   - In SSMS, try connecting to:
     - `localhost\SQLEXPRESS`
     - `.\SQLEXPRESS`
     - `(local)\SQLEXPRESS`

### Authentication Issues

1. **For SA login issues**:
   - Ensure Mixed Mode authentication is enabled
   - Verify SA account is enabled
   - Check password is correct

2. **For Windows Authentication**:
   - Make sure your Windows user has access
   - Try running as administrator

### Network Issues

1. **Enable TCP/IP protocol** (see configuration steps above)
2. **Check Windows Firewall**:
   - Allow SQL Server through firewall
   - Default port 1433 should be open

### Port Issues

1. **Find the actual port**:
   - Open SQL Server Configuration Manager
   - Go to SQL Server Network Configuration → Protocols for SQLEXPRESS
   - Right-click TCP/IP → Properties → IP Addresses
   - Check the port in IPAll section

## Performance Tips

1. **Increase memory limit** (if needed):
   - SQL Server Express has 1GB RAM limit
   - For development, this is usually sufficient

2. **Database file location**:
   - Default location: `C:\Program Files\Microsoft SQL Server\MSSQL16.SQLEXPRESS\MSSQL\DATA\`
   - You can change this during installation

3. **Backup strategy**:
   - Set up regular backups for important data
   - Use SQL Server Agent (if available) or Windows Task Scheduler

## Next Steps

Once SQL Server Express is set up and your project is running:

1. **Explore the API** at `http://localhost:5000/health`
2. **Use SSMS** to view and manage your database
3. **Check logs** in the `backend/logs` folder
4. **Start building** your application features!

## Need Help?

- Check the main [SQL Server Setup Guide](./SQL_SERVER_SETUP.md) for more options
- Review the [README.md](../README.md) for project-specific information
- SQL Server Express documentation: [Microsoft Docs](https://docs.microsoft.com/en-us/sql/sql-server/)
