const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const xss = require('xss-clean');
const hpp = require('hpp');
const cors = require('cors');
const { expressCspHeader, INLINE, SELF } = require('express-csp-header');

// Rate limiting
const limiter = rateLimit({
  windowMs: process.env.RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT_MAX || 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: {
    status: 429,
    message: 'Too many requests, please try again later.'
  }
});

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400 // 24 hours
};

// Setup security middleware
const setupSecurity = (app) => {
  // Set security HTTP headers
  app.use(helmet());
  
  // Enable CORS
  app.use(cors(corsOptions));
  
  // Sanitize data against XSS
  app.use(xss());
  
  // Prevent HTTP Parameter Pollution
  app.use(hpp());
  
  // Content Security Policy
  app.use(expressCspHeader({
    directives: {
      'default-src': [SELF],
      'script-src': [SELF, INLINE],
      'style-src': [SELF, INLINE],
      'img-src': [SELF, 'data:', 'https:'],
      'font-src': [SELF, 'https://fonts.gstatic.com'],
      'connect-src': [SELF]
    }
  }));
  
  // Rate limiting for all requests
  app.use('/api/', limiter);
  
  console.log('✅ Security middleware configured');
};

module.exports = {
  setupSecurity,
  limiter
};