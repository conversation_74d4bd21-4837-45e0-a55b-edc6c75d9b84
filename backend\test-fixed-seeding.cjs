// Test the fixed seeding functionality
const { initializeModels } = require('./data/models');

async function testFixedSeeding() {
  console.log('🧪 Testing Fixed Seeding Functionality...\n');
  
  try {
    console.log('1. Testing database initialization...');
    
    // This will run the seeding function
    await initializeModels();
    
    console.log('✅ Database initialization completed successfully!');
    console.log('\n📝 Summary:');
    console.log('✅ No more duplicate entry errors');
    console.log('✅ Categories seeding with ignoreDuplicates');
    console.log('✅ Sample user/farmer creation with existence check');
    console.log('✅ Products seeding with ignoreDuplicates');
    console.log('\n🎉 Server should now start without seeding errors!');
    
  } catch (error) {
    console.error('❌ Seeding test failed:', error.message);
    
    if (error.message.includes('Duplicate entry')) {
      console.log('\n🔍 Still getting duplicate errors - need more fixes');
    }
  }
}

testFixedSeeding();
