
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Truck, MapPin, Clock, Thermometer, CheckCircle } from "lucide-react";

interface DeliveryTrackingProps {
  orderId: string;
  status: "preparing" | "in_transit" | "delivered";
  currentLocation: string;
  estimatedArrival: string;
  temperature: number;
  preservationMethod: string;
  driverName: string;
  driverPhone: string;
}

const DeliveryTracking = ({
  orderId,
  status,
  currentLocation,
  estimatedArrival,
  temperature,
  preservationMethod,
  driverName,
  driverPhone
}: DeliveryTrackingProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "preparing": return "bg-yellow-100 text-yellow-700";
      case "in_transit": return "bg-blue-100 text-blue-700";
      case "delivered": return "bg-green-100 text-green-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getTemperatureStatus = (temp: number) => {
    if (temp <= 4) return { color: "text-blue-600", status: "Optimal Cold" };
    if (temp <= 10) return { color: "text-green-600", status: "Good" };
    if (temp <= 15) return { color: "text-yellow-600", status: "Acceptable" };
    return { color: "text-red-600", status: "Warning" };
  };

  const tempStatus = getTemperatureStatus(temperature);

  return (
    <Card className="enhanced-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Truck className="w-5 h-5 text-primary" />
          Delivery Tracking - #{orderId}
        </CardTitle>
        <Badge className={getStatusColor(status)}>
          {status.replace('_', ' ').toUpperCase()}
        </Badge>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
          <MapPin className="w-5 h-5 text-blue-600" />
          <div>
            <p className="font-medium">Current Location</p>
            <p className="text-sm text-muted-foreground">{currentLocation}</p>
          </div>
        </div>

        <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
          <Clock className="w-5 h-5 text-green-600" />
          <div>
            <p className="font-medium">Estimated Arrival</p>
            <p className="text-sm text-muted-foreground">{estimatedArrival}</p>
          </div>
        </div>

        <div className="flex items-center gap-3 p-3 bg-amber-50 rounded-lg">
          <Thermometer className={`w-5 h-5 ${tempStatus.color}`} />
          <div>
            <p className="font-medium">Cold Chain Status</p>
            <p className="text-sm text-muted-foreground">
              {temperature}°C - {tempStatus.status}
            </p>
            <p className="text-xs text-muted-foreground">{preservationMethod}</p>
          </div>
        </div>

        <div className="border-t pt-4">
          <h4 className="font-medium mb-2">Driver Information</h4>
          <div className="space-y-1">
            <p className="text-sm">{driverName}</p>
            <p className="text-sm text-muted-foreground">{driverPhone}</p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button size="sm" variant="outline" className="flex-1">
            Call Driver
          </Button>
          <Button size="sm" className="flex-1">
            Live Track
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DeliveryTracking;
