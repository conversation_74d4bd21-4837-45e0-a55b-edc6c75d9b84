import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles, 
  Leaf, 
  Calendar, 
  TrendingUp, 
  MapPin,
  Lightbulb,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import aiService from '@/services/aiService';

interface CropRecommendationsProps {
  onClose: () => void;
}

const CropRecommendations: React.FC<CropRecommendationsProps> = ({ onClose }) => {
  const [loading, setLoading] = useState(false);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    location: 'Douala, Cameroon',
    soilType: 'clay',
    farmSize: 1,
    season: 'rainy',
    previousCrops: ''
  });

  const soilTypes = [
    { value: 'clay', label: 'Clay Soil' },
    { value: 'sandy', label: 'Sandy Soil' },
    { value: 'loamy', label: 'Loamy Soil' },
    { value: 'volcanic', label: 'Volcanic Soil' }
  ];

  const seasons = [
    { value: 'dry', label: 'Dry Season' },
    { value: 'rainy', label: 'Rainy Season' }
  ];

  const cameroonRegions = [
    'Douala, Littoral',
    'Yaoundé, Centre',
    'Bafoussam, West',
    'Bamenda, Northwest',
    'Garoua, North',
    'Maroua, Far North',
    'Bertoua, East',
    'Ebolowa, South',
    'Buea, Southwest',
    'Ngaoundéré, Adamawa'
  ];

  useEffect(() => {
    // Load initial recommendations
    handleGetRecommendations();
  }, []);

  const handleGetRecommendations = async () => {
    setLoading(true);
    try {
      const params = {
        location: formData.location,
        soilType: formData.soilType,
        farmSize: formData.farmSize,
        season: formData.season,
        previousCrops: formData.previousCrops ? formData.previousCrops.split(',').map(c => c.trim()) : []
      };

      const result = await aiService.recommendCrops(params);
      setRecommendations(result);
      
      if (result.length > 0) {
        toast.success(`Found ${result.length} crop recommendations for your farm!`);
      }
    } catch (error) {
      toast.error('Failed to get crop recommendations');
      console.error('Crop recommendation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSuccessColor = (probability: number) => {
    if (probability >= 0.8) return 'text-green-600 bg-green-100';
    if (probability >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-600" />
              AI Crop Recommendations
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          </div>
          <p className="text-gray-600">
            Get AI-powered recommendations for the best crops to grow on your farm
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Input Form */}
          <Card className="bg-gray-50">
            <CardContent className="p-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="location">Farm Location</Label>
                  <select
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full p-2 border rounded-md"
                  >
                    {cameroonRegions.map(region => (
                      <option key={region} value={region}>{region}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label htmlFor="soilType">Soil Type</Label>
                  <select
                    id="soilType"
                    value={formData.soilType}
                    onChange={(e) => setFormData(prev => ({ ...prev, soilType: e.target.value }))}
                    className="w-full p-2 border rounded-md"
                  >
                    {soilTypes.map(soil => (
                      <option key={soil.value} value={soil.value}>{soil.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label htmlFor="farmSize">Farm Size (hectares)</Label>
                  <Input
                    id="farmSize"
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={formData.farmSize}
                    onChange={(e) => setFormData(prev => ({ ...prev, farmSize: parseFloat(e.target.value) || 1 }))}
                  />
                </div>

                <div>
                  <Label htmlFor="season">Current Season</Label>
                  <select
                    id="season"
                    value={formData.season}
                    onChange={(e) => setFormData(prev => ({ ...prev, season: e.target.value }))}
                    className="w-full p-2 border rounded-md"
                  >
                    {seasons.map(season => (
                      <option key={season.value} value={season.value}>{season.label}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="previousCrops">Previous Crops (optional)</Label>
                <Input
                  id="previousCrops"
                  value={formData.previousCrops}
                  onChange={(e) => setFormData(prev => ({ ...prev, previousCrops: e.target.value }))}
                  placeholder="e.g., Maize, Cassava, Plantain"
                />
                <p className="text-sm text-gray-500 mt-1">Separate multiple crops with commas</p>
              </div>

              <Button 
                onClick={handleGetRecommendations}
                disabled={loading}
                className="w-full bg-purple-600 hover:bg-purple-700"
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Getting AI Recommendations...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    Get AI Recommendations
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Leaf className="w-5 h-5 text-green-600" />
                Recommended Crops for Your Farm
              </h3>

              <div className="grid gap-4">
                {recommendations.map((crop, index) => (
                  <Card key={index} className="border-l-4 border-l-green-500">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="text-xl font-bold text-green-700">{crop.crop}</h4>
                          <p className="text-gray-600">Expected Yield: {crop.expectedYield}</p>
                        </div>
                        <Badge className={`${getSuccessColor(crop.successProbability)} border-0`}>
                          {Math.round(crop.successProbability * 100)}% Success Rate
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-blue-500" />
                          <span className="text-sm">
                            <strong>Plant:</strong> {crop.plantingTime}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <TrendingUp className="w-4 h-4 text-orange-500" />
                          <span className="text-sm">
                            <strong>Harvest:</strong> {crop.harvestTime}
                          </span>
                        </div>
                      </div>

                      {crop.tips && crop.tips.length > 0 && (
                        <div className="bg-blue-50 rounded-lg p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <Lightbulb className="w-4 h-4 text-blue-600" />
                            <span className="font-medium text-blue-800">AI Tips:</span>
                          </div>
                          <ul className="space-y-1">
                            {crop.tips.map((tip: string, tipIndex: number) => (
                              <li key={tipIndex} className="text-sm text-blue-700 flex items-start gap-2">
                                <span className="text-blue-400 mt-1">•</span>
                                {tip}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* AI Disclaimer */}
          <Card className="bg-yellow-50 border-yellow-200">
            <CardContent className="p-4">
              <div className="flex items-start gap-2">
                <Sparkles className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-800">AI-Powered Recommendations</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    These recommendations are generated using AI analysis of your farm conditions, 
                    local climate data, and agricultural best practices for Cameroon. Always consult 
                    with local agricultural experts for personalized advice.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
};

export default CropRecommendations;
