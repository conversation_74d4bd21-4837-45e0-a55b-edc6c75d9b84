const paymentService = require('../../business/services/paymentService');

// Process payment
exports.processPayment = async (req, res, next) => {
  try {
    const { orderId, token, paymentMethod } = req.body;
    const result = await paymentService.processPayment(orderId, token, paymentMethod);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

// Refund payment
exports.refundPayment = async (req, res, next) => {
  try {
    const result = await paymentService.refundPayment(req.params.orderId);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

// Get payment methods
exports.getPaymentMethods = async (req, res, next) => {
  try {
    const methods = await paymentService.getAvailablePaymentMethods();
    res.json(methods);
  } catch (error) {
    next(error);
  }
};

// Get payment status
exports.getPaymentStatus = async (req, res, next) => {
  try {
    const status = await paymentService.getPaymentStatus(req.params.orderId);
    res.json(status);
  } catch (error) {
    next(error);
  }
};

// Handle Stripe webhook
exports.handleWebhook = async (req, res, next) => {
  try {
    const sig = req.headers['stripe-signature'];
    const result = await paymentService.handleWebhook(req.body, sig);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

// Process mobile money payment
exports.processMobileMoneyPayment = async (req, res, next) => {
  try {
    const { orderId, phoneNumber, provider } = req.body;
    const result = await paymentService.processMobileMoneyPayment(orderId, phoneNumber, provider);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

// Process cash on delivery
exports.processCashOnDelivery = async (req, res, next) => {
  try {
    const { orderId } = req.body;
    const result = await paymentService.processCashOnDelivery(orderId);
    res.json(result);
  } catch (error) {
    next(error);
  }
};