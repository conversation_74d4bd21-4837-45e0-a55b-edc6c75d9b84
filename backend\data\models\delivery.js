const { DataTypes } = require('sequelize');
const { getSequelize } = require('../../config/database');

const Delivery = getSequelize().define('Delivery', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  orderId: {
    type: DataTypes.UUID,
    allowNull: false,
    unique: true,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  deliveryAgentId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  status: {
    type: DataTypes.ENUM('PENDING', 'ASSIGNED', 'IN_TRANSIT', 'DELIVERED', 'FAILED'),
    defaultValue: 'PENDING',
  },
  trackingNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  estimatedDelivery: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  actualDelivery: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  deliveryFee: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  deliveryAddress: {
    type: DataTypes.JSON,
    allowNull: false,
  },
}, {
  tableName: 'deliveries',
  timestamps: true,
});

module.exports = Delivery;