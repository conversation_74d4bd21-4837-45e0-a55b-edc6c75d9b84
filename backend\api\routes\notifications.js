const express = require('express');
const router = express.Router();

// Simple notifications routes - will be implemented later
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: {
      notifications: [],
      unreadCount: 0
    }
  });
});

router.put('/:id/read', (req, res) => {
  res.json({
    success: true,
    message: 'Notification marked as read'
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'Notification deleted'
  });
});

module.exports = router;
