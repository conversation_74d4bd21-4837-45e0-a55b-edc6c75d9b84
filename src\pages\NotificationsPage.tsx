import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import {
  Bell,
  Package,
  Heart,
  ShoppingCart,
  User,
  Truck,
  Star,
  AlertCircle,
  CheckCircle,
  Clock,
  Trash2,
  Settings,
  Mail
} from 'lucide-react';
import { toast } from 'sonner';

interface Notification {
  id: number;
  type: 'order' | 'product' | 'system' | 'promotion';
  title: string;
  message: string;
  time: string;
  read: boolean;
  icon: React.ReactNode;
  actionUrl?: string;
}

const NotificationsPage: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      type: 'order',
      title: 'Order Delivered',
      message: 'Your order #12345 has been successfully delivered. Premium Cocoa Beans from <PERSON>.',
      time: '2 hours ago',
      read: false,
      icon: <Package className="w-5 h-5 text-green-600" />,
      actionUrl: '/orders/12345'
    },
    {
      id: 2,
      type: 'product',
      title: 'Back in Stock',
      message: 'Fresh Mangoes from Marie Fon are now available! Get them before they run out.',
      time: '4 hours ago',
      read: false,
      icon: <Heart className="w-5 h-5 text-red-600" />,
      actionUrl: '/products/2'
    },
    {
      id: 3,
      type: 'promotion',
      title: 'Special Offer',
      message: 'Get 20% off on all vegetables this weekend! Use code FRESH20 at checkout.',
      time: '1 day ago',
      read: true,
      icon: <Star className="w-5 h-5 text-yellow-600" />,
      actionUrl: '/products?category=vegetables'
    },
    {
      id: 4,
      type: 'order',
      title: 'Order Shipped',
      message: 'Your order #12344 is on its way! Track your package for real-time updates.',
      time: '2 days ago',
      read: true,
      icon: <Truck className="w-5 h-5 text-blue-600" />,
      actionUrl: '/orders/12344'
    },
    {
      id: 5,
      type: 'system',
      title: 'Profile Updated',
      message: 'Your profile information has been successfully updated.',
      time: '3 days ago',
      read: true,
      icon: <User className="w-5 h-5 text-purple-600" />
    },
    {
      id: 6,
      type: 'product',
      title: 'Price Drop Alert',
      message: 'Arabica Coffee Beans price dropped by 15%! Now available for 2,720 FCFA.',
      time: '1 week ago',
      read: false,
      icon: <AlertCircle className="w-5 h-5 text-orange-600" />,
      actionUrl: '/products/3'
    }
  ]);

  const markAsRead = (id: number) => {
    setNotifications(notifications.map(notif => 
      notif.id === id ? { ...notif, read: true } : notif
    ));
  };

  const markAsUnread = (id: number) => {
    setNotifications(notifications.map(notif => 
      notif.id === id ? { ...notif, read: false } : notif
    ));
  };

  const deleteNotification = (id: number) => {
    setNotifications(notifications.filter(notif => notif.id !== id));
    toast.success('Notification deleted');
  };

  const markAllAsRead = () => {
    setNotifications(notifications.map(notif => ({ ...notif, read: true })));
    toast.success('All notifications marked as read');
  };

  const clearAll = () => {
    setNotifications([]);
    toast.success('All notifications cleared');
  };

  const unreadCount = notifications.filter(n => !n.read).length;
  const orderNotifications = notifications.filter(n => n.type === 'order');
  const productNotifications = notifications.filter(n => n.type === 'product');
  const systemNotifications = notifications.filter(n => n.type === 'system' || n.type === 'promotion');

  const NotificationCard = ({ notification }: { notification: Notification }) => (
    <Card className="shadow-lg rounded-xl border-0 bg-gray-900 hover:shadow-2xl transition-all duration-300 mb-4">
      <CardContent>
        <h2 className="text-lg font-bold text-green-400 mb-2">{notification.title}</h2>
        <p className="text-green-200 mb-2">{notification.message}</p>
        <span className="text-xs text-green-500">{notification.time}</span>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Bell className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
                <p className="text-gray-600">
                  {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}
                </p>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Button variant="outline" onClick={markAllAsRead} disabled={unreadCount === 0}>
                <CheckCircle className="w-4 h-4 mr-2" />
                Mark All Read
              </Button>
              <Button variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
              <Button variant="outline" onClick={clearAll} disabled={notifications.length === 0}>
                <Trash2 className="w-4 h-4 mr-2" />
                Clear All
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all" className="flex items-center space-x-2">
              <Bell className="w-4 h-4" />
              <span>All ({notifications.length})</span>
            </TabsTrigger>
            <TabsTrigger value="orders" className="flex items-center space-x-2">
              <Package className="w-4 h-4" />
              <span>Orders ({orderNotifications.length})</span>
            </TabsTrigger>
            <TabsTrigger value="products" className="flex items-center space-x-2">
              <ShoppingCart className="w-4 h-4" />
              <span>Products ({productNotifications.length})</span>
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4" />
              <span>System ({systemNotifications.length})</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {notifications.length === 0 ? (
              <div className="text-center py-12">
                <Bell className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
                <p className="text-gray-600">You're all caught up! Check back later for updates.</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <NotificationCard key={notification.id} notification={notification} />
              ))
            )}
          </TabsContent>

          <TabsContent value="orders" className="space-y-4">
            {orderNotifications.length === 0 ? (
              <div className="text-center py-12">
                <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No order notifications</h3>
                <p className="text-gray-600">Order updates will appear here.</p>
              </div>
            ) : (
              orderNotifications.map((notification) => (
                <NotificationCard key={notification.id} notification={notification} />
              ))
            )}
          </TabsContent>

          <TabsContent value="products" className="space-y-4">
            {productNotifications.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingCart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No product notifications</h3>
                <p className="text-gray-600">Product updates and alerts will appear here.</p>
              </div>
            ) : (
              productNotifications.map((notification) => (
                <NotificationCard key={notification.id} notification={notification} />
              ))
            )}
          </TabsContent>

          <TabsContent value="system" className="space-y-4">
            {systemNotifications.length === 0 ? (
              <div className="text-center py-12">
                <AlertCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No system notifications</h3>
                <p className="text-gray-600">System updates and promotions will appear here.</p>
              </div>
            ) : (
              systemNotifications.map((notification) => (
                <NotificationCard key={notification.id} notification={notification} />
              ))
            )}
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
};

export default NotificationsPage;
