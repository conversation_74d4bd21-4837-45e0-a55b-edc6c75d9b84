// Check if all products are assigned to categories
const mysql = require('mysql2/promise');

async function checkProductCategories() {
  console.log('🔍 Checking Product-Category Relationships...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to MySQL database\n');
    
    // 1. Check total products and categories
    const [productCount] = await connection.execute('SELECT COUNT(*) as total FROM products');
    const [categoryCount] = await connection.execute('SELECT COUNT(*) as total FROM categories');
    
    console.log('📊 Database Overview:');
    console.log(`   Total Products: ${productCount[0].total}`);
    console.log(`   Total Categories: ${categoryCount[0].total}\n`);
    
    // 2. Check products without categories
    const [orphanProducts] = await connection.execute(`
      SELECT id, name, categoryId 
      FROM products 
      WHERE categoryId IS NULL
    `);
    
    if (orphanProducts.length > 0) {
      console.log('⚠️ Products WITHOUT Categories:');
      orphanProducts.forEach(product => {
        console.log(`   - ${product.name} (ID: ${product.id})`);
      });
      console.log('');
    } else {
      console.log('✅ All products have categories assigned!\n');
    }
    
    // 3. Check products with categories
    const [productsWithCategories] = await connection.execute(`
      SELECT 
        p.id,
        p.name as product_name,
        p.price,
        p.stock,
        c.name as category_name,
        c.id as category_id
      FROM products p
      LEFT JOIN categories c ON p.categoryId = c.id
      ORDER BY c.name, p.name
    `);
    
    console.log('📋 Products by Category:\n');
    
    let currentCategory = '';
    let productsByCategory = {};
    
    productsWithCategories.forEach(product => {
      const categoryName = product.category_name || 'NO CATEGORY';
      
      if (!productsByCategory[categoryName]) {
        productsByCategory[categoryName] = [];
      }
      productsByCategory[categoryName].push(product);
    });
    
    // Display products grouped by category
    Object.keys(productsByCategory).sort().forEach(categoryName => {
      const products = productsByCategory[categoryName];
      console.log(`📁 ${categoryName} (${products.length} products):`);
      
      products.forEach(product => {
        console.log(`   - ${product.product_name}`);
        console.log(`     Price: ${product.price} FCFA | Stock: ${product.stock}`);
      });
      console.log('');
    });
    
    // 4. Category distribution summary
    console.log('📈 Category Distribution Summary:');
    Object.keys(productsByCategory).sort().forEach(categoryName => {
      const count = productsByCategory[categoryName].length;
      console.log(`   ${categoryName}: ${count} products`);
    });
    
    // 5. Check for empty categories
    const [emptyCategories] = await connection.execute(`
      SELECT c.id, c.name, c.description
      FROM categories c
      LEFT JOIN products p ON c.id = p.categoryId
      WHERE p.id IS NULL
    `);
    
    if (emptyCategories.length > 0) {
      console.log('\n📂 Empty Categories (no products):');
      emptyCategories.forEach(category => {
        console.log(`   - ${category.name}: ${category.description}`);
      });
    } else {
      console.log('\n✅ All categories have products assigned!');
    }
    
    // 6. Validation summary
    console.log('\n🎯 Validation Summary:');
    console.log(`✅ Total Products: ${productCount[0].total}`);
    console.log(`✅ Total Categories: ${categoryCount[0].total}`);
    console.log(`${orphanProducts.length === 0 ? '✅' : '❌'} Products without categories: ${orphanProducts.length}`);
    console.log(`${emptyCategories.length === 0 ? '✅' : '⚠️'} Empty categories: ${emptyCategories.length}`);
    
    if (orphanProducts.length === 0 && emptyCategories.length === 0) {
      console.log('\n🎉 Perfect! All products are properly categorized!');
    } else {
      console.log('\n💡 Some optimization needed for better organization.');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Error checking product categories:', error.message);
  }
}

checkProductCategories();
