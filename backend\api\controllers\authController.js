const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User, Farmer } = require('../../data/models');
const config = require('../../config/config');

exports.register = async (req, res, next) => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      password,
      role = 'CUSTOMER',
      farmName,
      farmLocation
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      where: { email: email.toLowerCase() }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, config.security?.bcryptRounds || 12);

    // Create user
    const newUser = await User.create({
      firstName,
      lastName,
      email: email.toLowerCase(),
      phone,
      password: hashedPassword,
      role: role.toUpperCase(),
      status: 'ACTIVE',
      emailVerified: false,
      phoneVerified: false
    });

    // If farmer, create farmer profile
    let farmerProfile = null;
    if (role.toUpperCase() === 'FARMER' && farmName && farmLocation) {
      farmerProfile = await Farmer.create({
        userId: newUser.id,
        farmName,
        farmLocation,
        verified: false,
        rating: 0,
        totalSales: 0
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { id: newUser.id, role: newUser.role },
      config.jwt?.secret || config.jwtSecret || 'fallback-secret',
      { expiresIn: config.jwt?.expiresIn || config.jwtExpiresIn || '24h' }
    );

    // Prepare user response
    const userResponse = {
      id: newUser.id,
      firstName: newUser.firstName,
      lastName: newUser.lastName,
      email: newUser.email,
      phone: newUser.phone,
      role: newUser.role,
      status: newUser.status,
      emailVerified: newUser.emailVerified,
      phoneVerified: newUser.phoneVerified,
      createdAt: newUser.createdAt,
      updatedAt: newUser.updatedAt,
      farmerProfile: farmerProfile ? {
        id: farmerProfile.id,
        farmName: farmerProfile.farmName,
        farmLocation: farmerProfile.farmLocation,
        verified: farmerProfile.verified,
        rating: farmerProfile.rating
      } : null
    };

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userResponse,
        accessToken: token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    next(error);
  }
};

exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find user with farmer profile if exists
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: [{
        model: Farmer,
        as: 'farmerProfile',
        required: false
      }]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      return res.status(403).json({
        success: false,
        message: 'Your account is not active. Please contact support.'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Update last login
    await user.update({ lastLogin: new Date() });

    // Generate JWT token
    const token = jwt.sign(
      { id: user.id, role: user.role },
      config.jwt?.secret || config.jwtSecret || 'fallback-secret',
      { expiresIn: config.jwt?.expiresIn || config.jwtExpiresIn || '24h' }
    );

    // Prepare user response
    const userResponse = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      farmerProfile: user.farmerProfile ? {
        id: user.farmerProfile.id,
        farmName: user.farmerProfile.farmName,
        farmLocation: user.farmerProfile.farmLocation,
        farmDescription: user.farmerProfile.farmDescription,
        verified: user.farmerProfile.verified,
        rating: user.farmerProfile.rating
      } : null
    };

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        accessToken: token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    next(error);
  }
};

