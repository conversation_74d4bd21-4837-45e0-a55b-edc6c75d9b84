// Test complete auth flow
const axios = require('axios');

async function testAuthFlow() {
  console.log('🔐 Testing Complete Authentication Flow...\n');
  
  try {
    // Step 1: Register
    console.log('1. Registering new user...');
    const email = `test.${Date.now()}@example.com`;
    
    const registerData = {
      firstName: 'Test',
      lastName: 'User',
      email: email,
      password: 'Test123!',
      role: 'CUSTOMER'
    };
    
    console.log('   Registration data:', JSON.stringify(registerData, null, 2));
    
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', registerData);
    
    console.log('✅ Registration successful');
    console.log('   Response:', registerResponse.data.success ? 'Success' : 'Failed');
    
    const token = registerResponse.data.data?.accessToken;
    console.log('   Token received:', token ? 'Yes' : 'No');
    
    if (!token) {
      console.log('❌ No token received - cannot test protected routes');
      return;
    }
    
    // Step 2: Test protected route
    console.log('\n2. Testing protected route...');
    console.log('   Using token:', token.substring(0, 20) + '...');
    
    const cartResponse = await axios.get('http://localhost:3001/api/cart', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Protected route accessible');
    console.log('   Cart response:', cartResponse.data.success ? 'Success' : 'Failed');
    
    // Step 3: Test login
    console.log('\n3. Testing login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: email,
      password: 'Test123!'
    });
    
    console.log('✅ Login successful');
    const loginToken = loginResponse.data.data?.accessToken;
    console.log('   New token received:', loginToken ? 'Yes' : 'No');
    
    // Step 4: Test with login token
    console.log('\n4. Testing with login token...');
    const cartResponse2 = await axios.get('http://localhost:3001/api/cart', {
      headers: {
        'Authorization': `Bearer ${loginToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login token works');
    console.log('   Cart response:', cartResponse2.data.success ? 'Success' : 'Failed');
    
    console.log('\n🎉 Authentication flow is working correctly!');
    console.log('\n📝 Instructions for frontend:');
    console.log('1. Clear browser localStorage');
    console.log('2. Register a new account');
    console.log('3. The auth should work properly');
    
  } catch (error) {
    console.log('❌ Authentication test failed:');
    console.log('   Status:', error.response?.status);
    console.log('   Error:', error.response?.data?.message || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n🔍 This is the authorization error!');
      console.log('   Possible causes:');
      console.log('   - Invalid JWT_SECRET');
      console.log('   - Token format issue');
      console.log('   - User not found in database');
    }
  }
}

testAuthFlow();
