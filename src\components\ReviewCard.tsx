
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, ThumbsUp, ThumbsDown } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ReviewCardProps {
  customerName: string;
  customerAvatar: string;
  rating: number;
  date: string;
  reviewText: string;
  productName: string;
  verified: boolean;
  helpful: number;
  images?: string[];
}

const ReviewCard = ({ 
  customerName, 
  customerAvatar, 
  rating, 
  date, 
  reviewText, 
  productName, 
  verified,
  helpful,
  images 
}: ReviewCardProps) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        key={i} 
        className={`w-4 h-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} 
      />
    ));
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <Avatar className="w-10 h-10">
            <AvatarImage src={customerAvatar} alt={customerName} />
            <AvatarFallback>{customerName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h4 className="font-semibold">{customerName}</h4>
              {verified && (
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                  Verified Purchase
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2 mb-2">
              <div className="flex">{renderStars(rating)}</div>
              <span className="text-sm text-muted-foreground">{date}</span>
            </div>
            
            <p className="text-sm font-medium text-muted-foreground mb-2">
              Product: {productName}
            </p>
            
            <p className="text-sm mb-3">{reviewText}</p>
            
            {images && images.length > 0 && (
              <div className="flex gap-2 mb-3">
                {images.map((image, index) => (
                  <img 
                    key={index}
                    src={image} 
                    alt={`Review ${index + 1}`}
                    className="w-16 h-16 rounded object-cover cursor-pointer hover:opacity-80"
                  />
                ))}
              </div>
            )}
            
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <span>Helpful?</span>
                <Button size="sm" variant="ghost" className="h-6 px-2">
                  <ThumbsUp className="w-3 h-3 mr-1" />
                  {helpful}
                </Button>
                <Button size="sm" variant="ghost" className="h-6 px-2">
                  <ThumbsDown className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReviewCard;
