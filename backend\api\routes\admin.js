const express = require('express');
const adminController = require('../controllers/adminController');
const auth = require('../middleware/auth');
const admin = require('../middleware/admin');

const router = express.Router();

router.get('/dashboard', auth, admin, adminController.getDashboard);
router.put('/listings/:id/quality', auth, admin, adminController.updateListingQuality);
router.get('/low-quality-listings', auth, admin, adminController.getLowQualityListings);
router.put('/vendors/:id/approve', auth, admin, adminController.approveVendor);

router.get('/admin/dashboard', authenticateToken, requireRole(['admin']), adminController.getDashboard);
router.get('/admin/users', authenticateToken, requireRole(['admin']), adminController.getAllUsers);
// Delete user account
router.delete('/account', authenticateToken, requireRole(['user', 'vendor', 'admin']), userController.deleteAccount);

module.exports = router;