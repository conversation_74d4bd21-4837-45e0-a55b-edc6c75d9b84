// Debug cart add issue
const mysql = require('mysql2/promise');

async function debugCartAdd() {
  console.log('🛒 Debugging Cart Add Issue...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    // Check your user account
    console.log('👤 Checking your user account:');
    const [users] = await connection.execute(`
      SELECT id, firstName, lastName, email, role 
      FROM users 
      WHERE email = '<EMAIL>'
    `);
    
    if (users.length > 0) {
      const user = users[0];
      console.log(`✅ User found: ${user.firstName} ${user.lastName} (ID: ${user.id})`);
      
      // Check if user has a cart
      const [carts] = await connection.execute(`
        SELECT id, userId, total, itemCount 
        FROM carts 
        WHERE userId = ?
      `, [user.id]);
      
      if (carts.length > 0) {
        console.log(`✅ Cart exists: ${carts[0].id} (${carts[0].itemCount} items)`);
      } else {
        console.log('⚠️ No cart found for user');
      }
      
      // Check available products
      console.log('\n📦 Checking available products:');
      const [products] = await connection.execute(`
        SELECT p.id, p.name, p.price, p.stock, p.farmerId, p.categoryId,
               f.id as farmer_exists, c.id as category_exists
        FROM products p
        LEFT JOIN farmers f ON p.farmerId = f.id
        LEFT JOIN categories c ON p.categoryId = c.id
        LIMIT 5
      `);
      
      products.forEach(product => {
        const farmerStatus = product.farmer_exists ? '✅' : '❌';
        const categoryStatus = product.category_exists ? '✅' : '❌';
        console.log(`   - ${product.name} (ID: ${product.id})`);
        console.log(`     Farmer: ${farmerStatus} | Category: ${categoryStatus} | Stock: ${product.stock}`);
      });
      
      // Test adding a product to cart manually
      if (products.length > 0) {
        const testProduct = products[0];
        console.log(`\n🧪 Testing add to cart with: ${testProduct.name}`);
        
        try {
          // Check if cart exists, create if not
          let cartId;
          if (carts.length > 0) {
            cartId = carts[0].id;
          } else {
            console.log('Creating new cart...');
            const cartUuid = require('crypto').randomUUID();
            await connection.execute(`
              INSERT INTO carts (id, userId, total, itemCount, createdAt, updatedAt)
              VALUES (?, ?, 0, 0, NOW(), NOW())
            `, [cartUuid, user.id]);
            cartId = cartUuid;
            console.log(`✅ Cart created: ${cartId}`);
          }
          
          // Try to add cart item
          const cartItemId = require('crypto').randomUUID();
          await connection.execute(`
            INSERT INTO cart_items (id, cartId, productId, quantity, price, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, NOW(), NOW())
          `, [cartItemId, cartId, testProduct.id, 1, testProduct.price]);
          
          console.log('✅ Cart item added successfully!');
          
          // Update cart totals
          await connection.execute(`
            UPDATE carts 
            SET itemCount = (SELECT COUNT(*) FROM cart_items WHERE cartId = ?),
                total = (SELECT SUM(quantity * price) FROM cart_items WHERE cartId = ?)
            WHERE id = ?
          `, [cartId, cartId, cartId]);
          
          console.log('✅ Cart totals updated!');
          
        } catch (addError) {
          console.log('❌ Failed to add to cart:');
          console.log('   Error:', addError.message);
          console.log('   Code:', addError.code);
          
          if (addError.code === 'ER_NO_REFERENCED_ROW_2') {
            console.log('   🔍 This is a foreign key constraint error');
            console.log('   💡 One of the referenced records doesn\'t exist');
          }
        }
      }
      
    } else {
      console.log('❌ User not found with email: <EMAIL>');
      console.log('💡 You might need to register or use a different email');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugCartAdd();
