const API_BASE = 'http://localhost:3001/api';

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: 'CUSTOMER' | 'FARMER' | 'ADMIN' | 'DELIVERY_AGENT' | 'NUTRITIONIST';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION';
  emailVerified: boolean;
  phoneVerified: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  farmerProfile?: {
    id: string;
    farmName: string;
    farmLocation: string;
    farmDescription?: string;
    verified: boolean;
    rating: number;
  };
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    accessToken: string;
    refreshToken?: string;
  };
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  password: string;
  role?: 'CUSTOMER' | 'FARMER';
  farmName?: string;
  farmLocation?: string;
}

class AuthService {
  private token: string | null = null;
  private user: User | null = null;

  constructor() {
    // Load token and user from localStorage on initialization
    this.token = localStorage.getItem('accessToken');
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        this.user = JSON.parse(userData);
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        this.clearAuth();
      }
    }
  }

  // Register new user
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        this.setAuth(result.data.accessToken, result.data.user);
      }

      return result;
    } catch (error) {
      console.error('Registration error:', error);
      throw new Error('Network error during registration');
    }
  }

  // Login user
  async login(data: LoginData): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        this.setAuth(result.data.accessToken, result.data.user);
      }

      return result;
    } catch (error) {
      console.error('Login error:', error);
      throw new Error('Network error during login');
    }
  }

  // Logout user
  logout(): void {
    this.clearAuth();
    // Trigger auth change event
    window.dispatchEvent(new Event('authChange'));
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.user;
  }

  // Get auth token
  getToken(): string | null {
    return this.token;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.token && !!this.user;
  }

  // Check if user has specific role
  hasRole(role: string): boolean {
    return this.user?.role === role;
  }

  // Check if user is admin
  isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }

  // Check if user is farmer
  isFarmer(): boolean {
    return this.hasRole('FARMER');
  }

  // Check if user is customer
  isCustomer(): boolean {
    return this.hasRole('CUSTOMER');
  }

  // Check if user is delivery agent
  isDeliveryAgent(): boolean {
    return this.hasRole('DELIVERY_AGENT');
  }

  // Get auth headers for API requests
  getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  // Make authenticated API request
  async authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    const authHeaders = this.getAuthHeaders();
    
    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...authHeaders,
        ...options.headers,
      },
    };

    const response = await fetch(url, requestOptions);

    // If unauthorized, clear auth and redirect to login
    if (response.status === 401) {
      this.clearAuth();
      window.location.href = '/login';
    }

    return response;
  }

  // Set authentication data
  private setAuth(token: string, user: User): void {
    this.token = token;
    this.user = user;
    
    localStorage.setItem('accessToken', token);
    localStorage.setItem('user', JSON.stringify(user));
  }

  // Clear authentication data
  private clearAuth(): void {
    this.token = null;
    this.user = null;
    
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');
  }

  // Update user profile
  async updateProfile(data: Partial<User>): Promise<any> {
    try {
      const response = await this.authenticatedFetch(`${API_BASE}/auth/profile`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success && result.data.user) {
        this.setAuth(this.token!, result.data.user);
      }

      return result;
    } catch (error) {
      console.error('Profile update error:', error);
      throw new Error('Network error during profile update');
    }
  }

  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<any> {
    try {
      const response = await this.authenticatedFetch(`${API_BASE}/auth/change-password`, {
        method: 'PUT',
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Password change error:', error);
      throw new Error('Network error during password change');
    }
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService;
