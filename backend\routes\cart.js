const express = require('express');
const { Cart, CartItem, Product, User, Category } = require('../data/models');
const { protect } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const router = express.Router();

// @desc    Get user's cart
// @route   GET /api/cart
// @access  Private
const getCart = asyncHandler(async (req, res) => {
  let cart = await Cart.findOne({
    where: { UserId: req.user.id },
    include: [
      {
        model: CartItem,
        include: [
          {
            model: Product,
            include: [
              { model: Category },
              { model: User, as: 'Farmer', attributes: ['id', 'username', 'email'] }
            ]
          }
        ]
      }
    ]
  });

  // Create cart if it doesn't exist
  if (!cart) {
    cart = await Cart.create({
      UserId: req.user.id,
      total: 0
    });
    cart.CartItems = [];
  }

  // Calculate totals
  const subtotal = cart.CartItems.reduce((sum, item) => {
    return sum + (item.quantity * item.price);
  }, 0);

  const shipping = subtotal > 10000 ? 0 : 500; // Free shipping over 10,000 FCFA
  const tax = Math.round(subtotal * 0.05); // 5% tax
  const total = subtotal + shipping + tax;

  // Update cart total
  cart.total = total;
  await cart.save();

  res.json({
    success: true,
    data: {
      cart: {
        id: cart.id,
        items: cart.CartItems,
        itemCount: cart.CartItems.length,
        subtotal,
        shipping,
        tax,
        total
      }
    }
  });
});

// @desc    Add item to cart
// @route   POST /api/cart/add
// @access  Private
const addToCart = asyncHandler(async (req, res) => {
  const { productId, quantity = 1 } = req.body;

  if (!productId) {
    return res.status(400).json({
      success: false,
      message: 'Product ID is required'
    });
  }

  // Check if product exists and is available
  const product = await Product.findByPk(productId);
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  if (product.stock < quantity) {
    return res.status(400).json({
      success: false,
      message: `Only ${product.stock} items available in stock`
    });
  }

  // Get or create cart
  let cart = await Cart.findOne({
    where: { UserId: req.user.id }
  });

  if (!cart) {
    cart = await Cart.create({
      UserId: req.user.id,
      total: 0
    });
  }

  // Check if item already exists in cart
  let cartItem = await CartItem.findOne({
    where: {
      CartId: cart.id,
      ProductId: productId
    }
  });

  if (cartItem) {
    // Update quantity
    const newQuantity = cartItem.quantity + quantity;
    
    if (product.stock < newQuantity) {
      return res.status(400).json({
        success: false,
        message: `Cannot add ${quantity} more items. Only ${product.stock - cartItem.quantity} more available.`
      });
    }

    cartItem.quantity = newQuantity;
    await cartItem.save();
  } else {
    // Create new cart item
    cartItem = await CartItem.create({
      CartId: cart.id,
      ProductId: productId,
      quantity,
      price: product.price
    });
  }

  // Get updated cart
  const updatedCart = await Cart.findOne({
    where: { UserId: req.user.id },
    include: [
      {
        model: CartItem,
        include: [
          {
            model: Product,
            include: [
              { model: Category },
              { model: User, as: 'Farmer', attributes: ['id', 'username', 'email'] }
            ]
          }
        ]
      }
    ]
  });

  res.json({
    success: true,
    message: 'Item added to cart successfully',
    data: {
      cart: updatedCart,
      addedItem: cartItem
    }
  });
});

// @desc    Update cart item quantity
// @route   PUT /api/cart/update/:itemId
// @access  Private
const updateCartItem = asyncHandler(async (req, res) => {
  const { itemId } = req.params;
  const { quantity } = req.body;

  if (!quantity || quantity < 1) {
    return res.status(400).json({
      success: false,
      message: 'Quantity must be at least 1'
    });
  }

  // Find cart item
  const cartItem = await CartItem.findOne({
    where: { id: itemId },
    include: [
      {
        model: Cart,
        where: { UserId: req.user.id }
      },
      { model: Product }
    ]
  });

  if (!cartItem) {
    return res.status(404).json({
      success: false,
      message: 'Cart item not found'
    });
  }

  // Check stock availability
  if (cartItem.Product.stock < quantity) {
    return res.status(400).json({
      success: false,
      message: `Only ${cartItem.Product.stock} items available in stock`
    });
  }

  // Update quantity
  cartItem.quantity = quantity;
  await cartItem.save();

  res.json({
    success: true,
    message: 'Cart item updated successfully',
    data: {
      cartItem
    }
  });
});

// @desc    Remove item from cart
// @route   DELETE /api/cart/remove/:itemId
// @access  Private
const removeFromCart = asyncHandler(async (req, res) => {
  const { itemId } = req.params;

  // Find and delete cart item
  const cartItem = await CartItem.findOne({
    where: { id: itemId },
    include: [
      {
        model: Cart,
        where: { UserId: req.user.id }
      }
    ]
  });

  if (!cartItem) {
    return res.status(404).json({
      success: false,
      message: 'Cart item not found'
    });
  }

  await cartItem.destroy();

  res.json({
    success: true,
    message: 'Item removed from cart successfully'
  });
});

// @desc    Clear entire cart
// @route   DELETE /api/cart/clear
// @access  Private
const clearCart = asyncHandler(async (req, res) => {
  const cart = await Cart.findOne({
    where: { UserId: req.user.id }
  });

  if (!cart) {
    return res.status(404).json({
      success: false,
      message: 'Cart not found'
    });
  }

  // Delete all cart items
  await CartItem.destroy({
    where: { CartId: cart.id }
  });

  // Reset cart total
  cart.total = 0;
  await cart.save();

  res.json({
    success: true,
    message: 'Cart cleared successfully'
  });
});

// Routes
router.get('/', protect, getCart);
router.post('/add', protect, addToCart);
router.put('/update/:itemId', protect, updateCartItem);
router.delete('/remove/:itemId', protect, removeFromCart);
router.delete('/clear', protect, clearCart);

module.exports = router;
