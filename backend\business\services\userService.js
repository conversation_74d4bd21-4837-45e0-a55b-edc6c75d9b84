const { User } = require('../../data/models');
const userRepository = require('../../data/repositories/userRepository');

class UserService {
  async createUser(userData) {
    // Additional business logic can be added here
    return await userRepository.create(userData);
  }

  async getUserById(id) {
    return await userRepository.findById(id);
  }

  async updateUser(id, userData) {
    // Add any business logic for updating user data
    return await userRepository.update(id, userData);
  }

  async deleteUser(id) {
    return await userRepository.delete(id);
  }

  async getAllUsers(options) {
    return await userRepository.findAll(options);
  }

  async changePassword(userId, oldPassword, newPassword) {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }
    
    const isPasswordValid = await user.validatePassword(oldPassword);
    if (!isPasswordValid) {
      throw new Error('Invalid old password');
    }

    user.password = newPassword;
    await user.save();
    return user;
  }
}

module.exports = new UserService();