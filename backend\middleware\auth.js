const jwt = require('jsonwebtoken');
const { User } = require('../data/models');
const { ApiError, asyncHandler } = require('./errorHandler');
const config = require('../config/config');

// Protect routes
const protect = asyncHandler(async (req, res, next) => {
  let token;
  
  // Get token from Authorization header
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  
  // Check if token exists
  if (!token) {
    return next(new ApiError('Not authorized to access this route', 401));
  }
  
  try {
    // Verify token
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // Get user from database
    const user = await User.findByPk(decoded.id);

    if (!user) {
      // User not found - token is invalid (likely due to database reset)
      return next(new ApiError('Invalid token - please login again', 401));
    }

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', {
      message: error.message,
      name: error.name,
      token: token ? token.substring(0, 20) + '...' : 'No token'
    });
    return next(new ApiError('Not authorized to access this route', 401));
  }
});

// Authorize by role
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new ApiError('User not authenticated', 401));
    }
    
    if (!roles.includes(req.user.role)) {
      return next(new ApiError(`User role ${req.user.role} is not authorized to access this route`, 403));
    }
    
    next();
  };
};

// Generate JWT token
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_ACCESS_EXPIRY || '15m'
  });
};

// Generate refresh token
const generateRefreshToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRY || '7d'
  });
};

module.exports = {
  protect,
  authorize,
  generateToken,
  generateRefreshToken
};