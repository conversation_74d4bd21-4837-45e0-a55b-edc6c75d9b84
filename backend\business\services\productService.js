const { Product, Category } = require('../../data/models');
const productRepository = require('../../data/repositories/productRepository');
const { Op } = require('sequelize');

class ProductService {
  async createProduct(productData) {
    return await productRepository.create(productData);
  }

  async getProductById(id) {
    return await productRepository.findById(id);
  }

  async updateProduct(id, productData) {
    return await productRepository.update(id, productData);
  }

  async deleteProduct(id) {
    return await productRepository.delete(id);
  }

  async getAllProducts(options) {
    return await productRepository.findAll(options);
  }

  async getProductsByCategory(categoryId) {
    return await Product.findAll({
      where: { categoryId },
      include: [{ model: Category, as: 'category' }]
    });
  }

  async searchProducts(query) {
    return await Product.findAll({
      where: {
        [Op.or]: [
          { name: { [Op.like]: `%${query}%` } },
          { description: { [Op.like]: `%${query}%` } }
        ]
      }
    });
  }

 }


module.exports = new ProductService();