const express = require('express');
const { Order, OrderItem, Product, User, Category, Cart, CartItem } = require('../data/models');
const { protect } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const router = express.Router();

// @desc    Create new order from cart
// @route   POST /api/orders/create
// @access  Private
const createOrder = asyncHandler(async (req, res) => {
  const { 
    shippingAddress, 
    paymentMethod = 'mobile_money',
    phoneNumber,
    notes 
  } = req.body;

  if (!shippingAddress || !phoneNumber) {
    return res.status(400).json({
      success: false,
      message: 'Shipping address and phone number are required'
    });
  }

  // Get user's cart
  const cart = await Cart.findOne({
    where: { UserId: req.user.id },
    include: [
      {
        model: CartItem,
        include: [{ model: Product }]
      }
    ]
  });

  if (!cart || cart.CartItems.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Cart is empty'
    });
  }

  // Check stock availability for all items
  for (const item of cart.CartItems) {
    if (item.Product.stock < item.quantity) {
      return res.status(400).json({
        success: false,
        message: `Insufficient stock for ${item.Product.name}. Only ${item.Product.stock} available.`
      });
    }
  }

  // Calculate totals
  const subtotal = cart.CartItems.reduce((sum, item) => {
    return sum + (item.quantity * item.price);
  }, 0);

  const shipping = subtotal > 10000 ? 0 : 500;
  const tax = Math.round(subtotal * 0.05);
  const total = subtotal + shipping + tax;

  // Generate order number
  const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

  // Create order
  const order = await Order.create({
    orderNumber,
    UserId: req.user.id,
    status: 'PENDING',
    subtotal,
    shipping,
    tax,
    total,
    paymentMethod,
    paymentStatus: 'PENDING',
    shippingAddress,
    phoneNumber,
    notes
  });

  // Create order items and update product stock
  for (const cartItem of cart.CartItems) {
    await OrderItem.create({
      OrderId: order.id,
      ProductId: cartItem.ProductId,
      quantity: cartItem.quantity,
      price: cartItem.price,
      total: cartItem.quantity * cartItem.price
    });

    // Update product stock
    await Product.update(
      { stock: cartItem.Product.stock - cartItem.quantity },
      { where: { id: cartItem.ProductId } }
    );
  }

  // Clear cart
  await CartItem.destroy({ where: { CartId: cart.id } });
  await cart.update({ total: 0 });

  // Get complete order with items
  const completeOrder = await Order.findByPk(order.id, {
    include: [
      {
        model: OrderItem,
        include: [
          {
            model: Product,
            include: [
              { model: Category },
              { model: User, as: 'Farmer', attributes: ['id', 'username', 'email'] }
            ]
          }
        ]
      },
      { model: User, as: 'Customer', attributes: ['id', 'username', 'email'] }
    ]
  });

  res.status(201).json({
    success: true,
    message: 'Order created successfully',
    data: {
      order: completeOrder
    }
  });
});

// @desc    Get user's orders
// @route   GET /api/orders
// @access  Private
const getOrders = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status } = req.query;
  const offset = (page - 1) * limit;

  const whereClause = { UserId: req.user.id };
  if (status) {
    whereClause.status = status;
  }

  const orders = await Order.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: OrderItem,
        include: [
          {
            model: Product,
            include: [
              { model: Category },
              { model: User, as: 'Farmer', attributes: ['id', 'username', 'email'] }
            ]
          }
        ]
      }
    ],
    order: [['createdAt', 'DESC']],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.json({
    success: true,
    data: {
      orders: orders.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(orders.count / limit),
        totalOrders: orders.count,
        hasNext: page * limit < orders.count,
        hasPrev: page > 1
      }
    }
  });
});

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private
const getOrder = asyncHandler(async (req, res) => {
  const order = await Order.findOne({
    where: { 
      id: req.params.id,
      UserId: req.user.id 
    },
    include: [
      {
        model: OrderItem,
        include: [
          {
            model: Product,
            include: [
              { model: Category },
              { model: User, as: 'Farmer', attributes: ['id', 'username', 'email'] }
            ]
          }
        ]
      },
      { model: User, as: 'Customer', attributes: ['id', 'username', 'email'] }
    ]
  });

  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  res.json({
    success: true,
    data: {
      order
    }
  });
});

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
const cancelOrder = asyncHandler(async (req, res) => {
  const order = await Order.findOne({
    where: { 
      id: req.params.id,
      UserId: req.user.id 
    },
    include: [{ model: OrderItem, include: [{ model: Product }] }]
  });

  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  if (order.status !== 'PENDING') {
    return res.status(400).json({
      success: false,
      message: 'Only pending orders can be cancelled'
    });
  }

  // Restore product stock
  for (const item of order.OrderItems) {
    await Product.update(
      { stock: item.Product.stock + item.quantity },
      { where: { id: item.ProductId } }
    );
  }

  // Update order status
  order.status = 'CANCELLED';
  order.cancelledAt = new Date();
  await order.save();

  res.json({
    success: true,
    message: 'Order cancelled successfully',
    data: {
      order
    }
  });
});

// @desc    Update order status (Admin/Farmer only)
// @route   PUT /api/orders/:id/status
// @access  Private (Admin/Farmer)
const updateOrderStatus = asyncHandler(async (req, res) => {
  const { status, trackingNumber } = req.body;
  
  const validStatuses = ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED'];
  
  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid status'
    });
  }

  const order = await Order.findByPk(req.params.id);

  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  order.status = status;
  if (trackingNumber) order.trackingNumber = trackingNumber;
  if (status === 'DELIVERED') order.deliveredAt = new Date();
  if (status === 'SHIPPED') order.shippedAt = new Date();

  await order.save();

  res.json({
    success: true,
    message: 'Order status updated successfully',
    data: {
      order
    }
  });
});

// Routes
router.post('/create', protect, createOrder);
router.get('/', protect, getOrders);
router.get('/:id', protect, getOrder);
router.put('/:id/cancel', protect, cancelOrder);
router.put('/:id/status', protect, updateOrderStatus); // Should add role check

module.exports = router;
