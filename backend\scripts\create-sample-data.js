const { connectDB } = require('../config/database');
const { User, Farmer, Category, Product } = require('../data/models');
const bcrypt = require('bcryptjs');

async function createSampleData() {
  try {
    console.log('🌱 Creating sample data...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Database connected');

    // Check if sample data already exists
    const existingUsers = await User.findAll();
    if (existingUsers.length > 0) {
      console.log('✅ Sample data already exists');
      return;
    }

    // Create sample farmers
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    const farmers = [
      {
        firstName: 'Jean',
        lastName: 'Mballa',
        email: '<EMAIL>',
        phone: '+237677123456',
        password: hashedPassword,
        role: 'FARMER',
        status: 'ACTIVE',
        emailVerified: true,
        phoneVerified: true,
        farmName: 'Mballa Organic Farm',
        farmLocation: 'Yaoundé, Centre Region',
        farmDescription: 'Organic vegetables and fruits farm specializing in traditional Cameroonian produce'
      },
      {
        firstName: 'Marie',
        lastName: 'Ngono',
        email: '<EMAIL>',
        phone: '+237678234567',
        password: hashedPassword,
        role: 'FARMER',
        status: 'ACTIVE',
        emailVerified: true,
        phoneVerified: true,
        farmName: 'Ngono Cocoa Plantation',
        farmLocation: 'Douala, Littoral Region',
        farmDescription: 'Premium cocoa beans and coffee production'
      },
      {
        firstName: 'Paul',
        lastName: 'Biya',
        email: '<EMAIL>',
        phone: '+237679345678',
        password: hashedPassword,
        role: 'FARMER',
        status: 'ACTIVE',
        emailVerified: true,
        phoneVerified: true,
        farmName: 'Biya Spice Gardens',
        farmLocation: 'Bamenda, Northwest Region',
        farmDescription: 'Traditional spices and herbs cultivation'
      }
    ];

    // Create farmers and their profiles
    const createdFarmers = [];
    for (const farmerData of farmers) {
      const { farmName, farmLocation, farmDescription, ...userData } = farmerData;
      
      const user = await User.create(userData);
      const farmerProfile = await Farmer.create({
        userId: user.id,
        farmName,
        farmLocation,
        farmDescription,
        verified: true,
        rating: 4.5,
        totalSales: 0
      });
      
      createdFarmers.push({ user, farmerProfile });
    }

    console.log(`✅ Created ${createdFarmers.length} farmers`);

    // Get categories
    const categories = await Category.findAll();
    if (categories.length === 0) {
      console.log('❌ No categories found. Please run create-categories.js first');
      return;
    }

    // Create sample products
    const sampleProducts = [
      // Vegetables
      {
        name: 'Fresh Tomatoes',
        description: 'Organic red tomatoes, perfect for cooking and salads. Grown without pesticides.',
        price: 1500,
        unit: 'kg',
        stock: 50,
        minOrder: 1,
        categoryName: 'Vegetables',
        farmerIndex: 0,
        images: ['tomatoes.jpg'],
        isOrganic: true,
        nutritionInfo: { vitamin_c: 'High', lycopene: 'Rich' }
      },
      {
        name: 'Green Leafy Vegetables',
        description: 'Mixed green vegetables including spinach, lettuce, and local greens.',
        price: 800,
        unit: 'bunch',
        stock: 30,
        minOrder: 1,
        categoryName: 'Vegetables',
        farmerIndex: 0,
        images: ['greens.jpg'],
        isOrganic: true
      },
      // Fruits
      {
        name: 'Sweet Bananas',
        description: 'Fresh sweet bananas from Cameroon. Perfect for snacking or cooking.',
        price: 1000,
        unit: 'bunch',
        stock: 25,
        minOrder: 1,
        categoryName: 'Fruits',
        farmerIndex: 0,
        images: ['bananas.jpg'],
        isOrganic: false
      },
      {
        name: 'Pineapples',
        description: 'Juicy tropical pineapples, sweet and fresh.',
        price: 2000,
        unit: 'piece',
        stock: 20,
        minOrder: 1,
        categoryName: 'Fruits',
        farmerIndex: 0,
        images: ['pineapple.jpg'],
        isOrganic: false
      },
      // Cocoa & Coffee
      {
        name: 'Premium Cocoa Beans',
        description: 'High-quality cocoa beans from Cameroon. Perfect for chocolate making.',
        price: 5000,
        unit: 'kg',
        stock: 100,
        minOrder: 5,
        categoryName: 'Cocoa & Coffee',
        farmerIndex: 1,
        images: ['cocoa.jpg'],
        isFairTrade: true
      },
      {
        name: 'Arabica Coffee Beans',
        description: 'Premium Arabica coffee beans with rich flavor and aroma.',
        price: 4500,
        unit: 'kg',
        stock: 75,
        minOrder: 2,
        categoryName: 'Cocoa & Coffee',
        farmerIndex: 1,
        images: ['coffee.jpg'],
        isFairTrade: true
      },
      // Spices & Herbs
      {
        name: 'Cameroon Pepper',
        description: 'Traditional Cameroon pepper, hot and flavorful.',
        price: 3000,
        unit: 'kg',
        stock: 15,
        minOrder: 1,
        categoryName: 'Spices & Herbs',
        farmerIndex: 2,
        images: ['pepper.jpg'],
        isOrganic: true
      },
      {
        name: 'Fresh Ginger',
        description: 'Organic fresh ginger root, perfect for cooking and tea.',
        price: 2500,
        unit: 'kg',
        stock: 40,
        minOrder: 1,
        categoryName: 'Spices & Herbs',
        farmerIndex: 2,
        images: ['ginger.jpg'],
        isOrganic: true
      }
    ];

    // Create products
    const createdProducts = [];
    for (const productData of sampleProducts) {
      const { categoryName, farmerIndex, ...productInfo } = productData;
      
      const category = categories.find(c => c.name === categoryName);
      if (!category) {
        console.log(`⚠️ Category ${categoryName} not found, skipping product ${productInfo.name}`);
        continue;
      }

      const farmer = createdFarmers[farmerIndex];
      if (!farmer) {
        console.log(`⚠️ Farmer index ${farmerIndex} not found, skipping product ${productInfo.name}`);
        continue;
      }

      const product = await Product.create({
        ...productInfo,
        farmerId: farmer.user.id,
        categoryId: category.id,
        status: 'ACTIVE',
        rating: 4.5,
        totalReviews: 0,
        totalSold: 0
      });

      createdProducts.push(product);
    }

    console.log(`✅ Created ${createdProducts.length} products`);
    console.log('✅ Sample data creation completed');

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createSampleData().then(() => {
    console.log('✅ Sample data creation completed');
    process.exit(0);
  });
}

module.exports = createSampleData;
