const { Vendor, ProductListing, User, Category } = require('../../data/models');

exports.registerVendor = async (userId, vendorData) => {
  const vendor = await Vendor.create({ ...vendorData, UserId: userId });
  return vendor;
};

exports.getVendorProfile = async (userId) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  return vendor;
};

exports.updateVendorProfile = async (userId, updateData) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  if (!vendor) throw new Error('Vendor not found');
  await vendor.update(updateData);
  return vendor;
};

exports.createProductListing = async (userId, listingData) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  if (!vendor) throw new Error('Vendor not found');
  
  const category = await Category.findByPk(listingData.CategoryId);
  if (!category) throw new Error('Category not found');

  const listing = await ProductListing.create({
    ...listingData,
    VendorId: vendor.id,
    CategoryId: category.id
  });
  return listing;
};

exports.updateProductListing = async (userId, listingId, updateData) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  if (!vendor) throw new Error('Vendor not found');
  
  const listing = await ProductListing.findOne({ where: { id: listingId, VendorId: vendor.id } });
  if (!listing) throw new Error('Listing not found');
  
  if (updateData.CategoryId) {
    const category = await Category.findByPk(updateData.CategoryId);
    if (!category) throw new Error('Category not found');
  }
  await listing.update(updateData);
  return listing;
};

exports.deleteProductListing = async (userId, listingId) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  if (!vendor) throw new Error('Vendor not found');
  const listing = await ProductListing.findOne({ where: { id: listingId, VendorId: vendor.id } });
  if (!listing) throw new Error('Listing not found');
  await listing.destroy();
};

exports.getVendorListings = async (userId) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  if (!vendor) throw new Error('Vendor not found');
  const listings = await ProductListing.findAll({ where: { VendorId: vendor.id } });
  return listings;
};

exports.uploadProductImage = async (userId, listingId, imageUrl) => {
  
};

exports.getVendorDashboard = async (userId) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  if (!vendor) throw new Error('Vendor not found');

  const totalListings = await ProductListing.count({ where: { VendorId: vendor.id } });
  const recentListings = await ProductListing.findAll({
    where: { VendorId: vendor.id },
    order: [['createdAt', 'DESC']],
    limit: 5
  });

  return {
    vendorName: vendor.name,
    totalListings,
    recentListings,
  };
};

exports.createProduct = async (userId, productData) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  if (!vendor) throw new Error('Vendor not found');
  const product = await ProductListing.create({ ...productData, VendorId: vendor.id });
  return product;
};

exports.deleteVendorAccount = async (userId) => {
  const vendor = await Vendor.findOne({ where: { UserId: userId } });
  if (!vendor) throw new Error('Vendor not found');

  await ProductListing.destroy({ where: { VendorId: vendor.id } });
  await vendor.destroy();

  const user = await User.findByPk(userId);
  if (user) {
    await user.update({ role: 'user' });
  }
};
