// Test the fixed cart controller
const axios = require('axios');

async function testFixedCartController() {
  console.log('🧪 Testing Fixed Cart Controller...\n');
  
  try {
    // Create a test user
    console.log('1. Creating test user...');
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Fixed',
      lastName: 'Test',
      email: `fixed.test.${Date.now()}@example.com`,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    const token = registerResponse.data.data.accessToken;
    console.log('✅ Test user created\n');
    
    // Test get cart (should not cause header errors)
    console.log('2. Testing get cart...');
    const cartResponse = await axios.get('http://localhost:3001/api/cart', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Get cart successful - no header errors!');
    console.log('   Response format:', cartResponse.data.success ? 'Correct' : 'Incorrect');
    
    // Add item to cart
    console.log('\n3. Adding item to cart...');
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products;
    const testProduct = products[0];
    
    const addResponse = await axios.post('http://localhost:3001/api/cart/add', {
      productId: testProduct.id,
      quantity: 1
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Add to cart successful');
    console.log('   Response format:', addResponse.data.success ? 'Correct' : 'Incorrect');
    
    // Test get cart again (should show the item)
    console.log('\n4. Testing get cart with items...');
    const cartWithItemsResponse = await axios.get('http://localhost:3001/api/cart', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Get cart with items successful');
    const cart = cartWithItemsResponse.data.data.cart;
    console.log(`   Items: ${cart.itemCount}, Total: ${cart.total} FCFA`);
    
    // Test clear cart
    console.log('\n5. Testing clear cart...');
    const clearResponse = await axios.delete('http://localhost:3001/api/cart/clear', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Clear cart successful');
    console.log('   Response format:', clearResponse.data.success ? 'Correct' : 'Incorrect');
    
    // Final cart check
    console.log('\n6. Final cart check...');
    const finalCartResponse = await axios.get('http://localhost:3001/api/cart', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Final cart check successful');
    const finalCart = finalCartResponse.data.data.cart;
    console.log(`   Items: ${finalCart.itemCount}, Total: ${finalCart.total} FCFA`);
    
    if (finalCart.itemCount === 0 && finalCart.total === 0) {
      console.log('\n🎉 Cart Controller Fixed Successfully!');
      console.log('\n📝 Summary:');
      console.log('✅ No more "Cannot set headers" errors');
      console.log('✅ Get cart works properly');
      console.log('✅ Add to cart works properly');
      console.log('✅ Clear cart works properly');
      console.log('✅ All responses have correct format');
    } else {
      console.log('\n⚠️ Cart not properly cleared');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.message.includes('Cannot set headers')) {
      console.log('\n🔍 Still getting header errors - need more fixes');
    }
  }
}

testFixedCartController();
