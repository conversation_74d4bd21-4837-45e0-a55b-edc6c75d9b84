// Check if products were created successfully
const mysql = require('mysql2/promise');

async function checkProductsCount() {
  console.log('🔍 Checking Products Count...\n');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to database');
    
    // Check total products
    const [totalCount] = await connection.execute('SELECT COUNT(*) as count FROM products');
    console.log(`\nTotal products: ${totalCount[0].count}`);
    
    // Check products per category
    const [categoryProductCounts] = await connection.execute(`
      SELECT c.name, COUNT(p.id) as product_count 
      FROM categories c 
      LEFT JOIN products p ON c.id = p.categoryId 
      GROUP BY c.id, c.name 
      ORDER BY c.name
    `);
    
    console.log('\n📊 Products per category:');
    categoryProductCounts.forEach(cat => {
      console.log(`   ${cat.name}: ${cat.product_count} products`);
    });
    
    // Check if we need to create more products
    const needsProducts = categoryProductCounts.filter(cat => cat.product_count < 8);
    
    if (needsProducts.length > 0) {
      console.log('\n⚠️ Categories that need more products:');
      needsProducts.forEach(cat => {
        console.log(`   ${cat.name}: needs ${8 - cat.product_count} more products`);
      });
    } else {
      console.log('\n🎉 All categories have 8 products each!');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  }
  
  process.exit(0);
}

checkProductsCount();
