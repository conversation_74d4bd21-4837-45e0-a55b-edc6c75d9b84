require('dotenv').config();

module.exports = {
  // Server configuration
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'cameroon-farm-connect-jwt-secret-2024',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'cameroon-farm-connect-refresh-secret-2024',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },

  // Database configuration
  database: {
    name: process.env.DB_NAME || 'cameroon_farm_connect',
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: process.env.DB_DIALECT || 'sqlite',
    storage: process.env.DB_STORAGE || './database.sqlite',
    logging: process.env.NODE_ENV === 'development' ? false : false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },

  // Email configuration
  email: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER,
    password: process.env.EMAIL_PASSWORD,
    from: process.env.EMAIL_FROM || '<EMAIL>',
  },

  // File upload configuration
  upload: {
    maxFileSize: process.env.MAX_FILE_SIZE || 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
    uploadDir: process.env.UPLOAD_DIR || 'uploads/',
    publicDir: process.env.PUBLIC_DIR || 'public/',
  },

  // Security configuration
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15 minutes
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 100, // requests per window
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  },

  // Payment configuration
  payment: {
    mtnMomo: {
      apiKey: process.env.MTN_MOMO_API_KEY,
      apiSecret: process.env.MTN_MOMO_API_SECRET,
      subscriptionKey: process.env.MTN_MOMO_SUBSCRIPTION_KEY,
      environment: process.env.MTN_MOMO_ENVIRONMENT || 'sandbox',
    },
    orangeMoney: {
      apiKey: process.env.ORANGE_MONEY_API_KEY,
      apiSecret: process.env.ORANGE_MONEY_API_SECRET,
      environment: process.env.ORANGE_MONEY_ENVIRONMENT || 'sandbox',
    }
  },

  // Application settings
  app: {
    name: 'Cameroon Farm Connect Hub',
    version: '1.0.0',
    description: 'Digital marketplace connecting farmers and consumers in Cameroon',
    supportEmail: '<EMAIL>',
    adminEmail: '<EMAIL>',
  },

  // Legacy support
  jwtSecret: process.env.JWT_SECRET || 'cameroon-farm-connect-jwt-secret-2024',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
  bcryptSaltRounds: 12,
};