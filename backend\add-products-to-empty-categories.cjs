// Add products to empty categories
const mysql = require('mysql2/promise');

async function addProductsToEmptyCategories() {
  console.log('🌱 Adding Products to Empty Categories...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to MySQL database\n');
    
    // Get a farmer ID to assign products to
    const [farmers] = await connection.execute('SELECT id FROM farmers LIMIT 1');
    const farmerId = farmers[0]?.id;
    
    if (!farmerId) {
      console.log('❌ No farmers found. Please create a farmer account first.');
      return;
    }
    
    console.log(`👨‍🌾 Using farmer ID: ${farmerId}\n`);
    
    // Define products for each empty category
    const productsToAdd = [
      // Fiber Crops
      {
        category: 'Fiber Crops',
        products: [
          { name: 'Organic Cotton', description: 'High-quality organic cotton for textile production', price: 2500, unit: 'kg', stock: 100 },
          { name: 'Sisal Fiber', description: 'Natural sisal fiber for rope and textile making', price: 1800, unit: 'kg', stock: 80 },
          { name: 'Bamboo Shoots', description: 'Fresh bamboo shoots for construction and crafts', price: 600, unit: 'piece', stock: 200 }
        ]
      },
      
      // Tree Crops & Nuts
      {
        category: 'Tree Crops & Nuts',
        products: [
          { name: 'Fresh Coconuts', description: 'Fresh coconuts with water and meat', price: 500, unit: 'piece', stock: 150 },
          { name: 'Kola Nuts', description: 'Traditional kola nuts for ceremonies and medicine', price: 3000, unit: 'kg', stock: 50 },
          { name: 'African Walnuts', description: 'Nutritious African walnuts', price: 4000, unit: 'kg', stock: 40 },
          { name: 'Raw Cashew Nuts', description: 'Fresh raw cashew nuts', price: 3500, unit: 'kg', stock: 60 }
        ]
      },
      
      // Aquaculture & Fisheries
      {
        category: 'Aquaculture & Fisheries',
        products: [
          { name: 'Fresh Tilapia', description: 'Farm-raised fresh tilapia fish', price: 2000, unit: 'kg', stock: 100 },
          { name: 'Catfish (Live)', description: 'Live catfish from local fish farms', price: 2500, unit: 'kg', stock: 80 },
          { name: 'Dried Fish', description: 'Smoked and dried fish for preservation', price: 4000, unit: 'kg', stock: 60 }
        ]
      },
      
      // Beverage Crops
      {
        category: 'Beverage Crops',
        products: [
          { name: 'Hibiscus Flowers', description: 'Dried hibiscus flowers for tea', price: 1500, unit: 'kg', stock: 100 },
          { name: 'Ginger Tea Blend', description: 'Natural ginger blend for beverages', price: 2000, unit: 'kg', stock: 70 }
        ]
      },
      
      // Livestock - Ruminants
      {
        category: 'Livestock - Ruminants',
        products: [
          { name: 'Fresh Goat Meat', description: 'Fresh goat meat from local farms', price: 3500, unit: 'kg', stock: 50 },
          { name: 'Cow Milk (Fresh)', description: 'Fresh cow milk from local dairy', price: 800, unit: 'liter', stock: 100 },
          { name: 'Sheep Wool', description: 'Natural sheep wool for textiles', price: 2500, unit: 'kg', stock: 30 }
        ]
      },
      
      // Oil Crops
      {
        category: 'Oil Crops',
        products: [
          { name: 'Sesame Seeds', description: 'High-quality sesame seeds for oil production', price: 1800, unit: 'kg', stock: 120 },
          { name: 'Sunflower Seeds', description: 'Fresh sunflower seeds', price: 1500, unit: 'kg', stock: 100 },
          { name: 'Castor Beans', description: 'Castor beans for oil extraction', price: 2000, unit: 'kg', stock: 80 }
        ]
      },
      
      // Livestock - Monogastrics
      {
        category: 'Livestock - Monogastrics',
        products: [
          { name: 'Free-Range Chicken', description: 'Fresh free-range chicken', price: 3000, unit: 'kg', stock: 80 },
          { name: 'Fresh Eggs', description: 'Farm-fresh chicken eggs', price: 150, unit: 'piece', stock: 500 },
          { name: 'Guinea Fowl', description: 'Fresh guinea fowl meat', price: 3500, unit: 'kg', stock: 40 }
        ]
      },
      
      // Mushrooms & Fungi
      {
        category: 'Mushrooms & Fungi',
        products: [
          { name: 'Oyster Mushrooms', description: 'Fresh oyster mushrooms', price: 2500, unit: 'kg', stock: 50 },
          { name: 'Shiitake Mushrooms', description: 'Premium shiitake mushrooms', price: 4000, unit: 'kg', stock: 30 },
          { name: 'Wild Mushrooms', description: 'Assorted wild edible mushrooms', price: 3000, unit: 'kg', stock: 40 }
        ]
      },
      
      // Flowers & Ornamentals
      {
        category: 'Flowers & Ornamentals',
        products: [
          { name: 'Fresh Cut Flowers', description: 'Beautiful fresh cut flowers for decoration', price: 1000, unit: 'bunch', stock: 100 },
          { name: 'Ornamental Plants', description: 'Decorative plants for gardens', price: 1500, unit: 'piece', stock: 80 }
        ]
      },
      
      // Forestry Products
      {
        category: 'Forestry Products',
        products: [
          { name: 'Rubber Latex', description: 'Fresh rubber latex from rubber trees', price: 2000, unit: 'liter', stock: 100 },
          { name: 'Teak Wood', description: 'High-quality teak wood planks', price: 15000, unit: 'cubic_meter', stock: 20 },
          { name: 'Eucalyptus Leaves', description: 'Fresh eucalyptus leaves for medicinal use', price: 800, unit: 'kg', stock: 60 }
        ]
      }
    ];
    
    let totalAdded = 0;
    
    for (const categoryData of productsToAdd) {
      console.log(`📁 Adding products to: ${categoryData.category}`);
      
      // Get category ID
      const [categories] = await connection.execute(
        'SELECT id FROM categories WHERE name = ?',
        [categoryData.category]
      );
      
      if (categories.length === 0) {
        console.log(`   ❌ Category "${categoryData.category}" not found`);
        continue;
      }
      
      const categoryId = categories[0].id;
      
      for (const product of categoryData.products) {
        try {
          const productId = require('crypto').randomUUID();
          
          await connection.execute(`
            INSERT INTO products (
              id, name, description, price, unit, stock, minOrder, 
              images, status, farmerId, categoryId, isOrganic, 
              createdAt, updatedAt
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
          `, [
            productId,
            product.name,
            product.description,
            product.price,
            product.unit,
            product.stock,
            1, // minOrder
            JSON.stringify(['https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&q=80']), // default image
            'ACTIVE',
            farmerId,
            categoryId,
            false // isOrganic
          ]);
          
          console.log(`   ✅ Added: ${product.name}`);
          totalAdded++;
          
        } catch (error) {
          console.log(`   ❌ Failed to add ${product.name}: ${error.message}`);
        }
      }
      
      console.log('');
    }
    
    console.log(`🎉 Successfully added ${totalAdded} products to empty categories!`);
    
    // Verify the results
    console.log('\n📊 Updated Category Summary:');
    const [categorySummary] = await connection.execute(`
      SELECT 
        c.name as category_name,
        COUNT(p.id) as product_count
      FROM categories c
      LEFT JOIN products p ON c.id = p.categoryId
      GROUP BY c.id, c.name
      ORDER BY product_count DESC, c.name
    `);
    
    categorySummary.forEach(cat => {
      const status = cat.product_count > 0 ? '✅' : '📂';
      console.log(`   ${status} ${cat.category_name}: ${cat.product_count} products`);
    });
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Error adding products:', error.message);
  }
}

addProductsToEmptyCategories();
