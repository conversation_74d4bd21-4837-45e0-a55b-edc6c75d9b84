# AgriConnect Backend API

## Overview
This is the backend API for the AgriConnect platform, an agricultural e-commerce application for Cameroon.

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MySQL (v5.7 or higher)

### Installation
1. Clone the repository
2. Install dependencies: `npm install`
3. Copy `.env.example` to `.env` and update the values
4. Run database migrations: `npm run migrate`
5. Start the server: `npm start`

## API Documentation

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `POST /api/auth/refresh` - Refresh access token

### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get a specific product
- `POST /api/products` - Create a new product (requires vendor role)
- `PUT /api/products/:id` - Update a product (requires vendor role)
- `DELETE /api/products/:id` - Delete a product (requires vendor role)

### Orders
- `GET /api/orders` - Get all orders for the authenticated user
- `GET /api/orders/:id` - Get a specific order
- `POST /api/orders` - Create a new order
- `PUT /api/orders/:id/status` - Update order status (requires admin role)

### Payments
- `POST /api/payments/process` - Process a payment
- `POST /api/payments/refund/:orderId` - Refund a payment
- `GET /api/payments/methods` - Get available payment methods
- `POST /api/payments/mobile-money` - Process mobile money payment
- `POST /api/payments/cash-on-delivery` - Process cash on delivery

### Farmers
- `GET /api/farmers` - Get all farmers
- `GET /api/farmers/:id` - Get a specific farmer
- `GET /api/farmers/:id/products` - Get all products from a farmer

## License
This project is licensed under the MIT License.