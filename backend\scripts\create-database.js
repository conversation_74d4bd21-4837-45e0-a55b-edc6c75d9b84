#!/usr/bin/env node

/**
 * Database Creation Script
 * This script creates the MySQL database for Cameroon Farm Connect Hub
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function createDatabase() {
  console.log(' Creating MySQL database for Cameroon Farm Connect Hub...\n');

  const config = {
    host: process.env.MYSQL_HOST || 'localhost',
    port: parseInt(process.env.MYSQL_PORT || '3306'),
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
  };

  const databaseName = process.env.MYSQL_DATABASE || 'cameroon_farm_connect';

  try {
    console.log(' Connecting to MySQL server...');
    console.log(`   Host: ${config.host}:${config.port}`);
    console.log(`   User: ${config.user}`);
    
    // Connect to MySQL server (without specifying database)
    const connection = await mysql.createConnection(config);
    console.log(' Connected to MySQL server successfully');

    // Check if database exists
    const [databases] = await connection.execute('SHOW DATABASES LIKE ?', [databaseName]);
    
    if (databases.length > 0) {
      console.log(`  Database '${databaseName}' already exists`);
    } else {
      // Create database
      console.log(` Creating database '${databaseName}'...`);
      await connection.execute(`CREATE DATABASE \`${databaseName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      console.log(` Database '${databaseName}' created successfully`);
    }

    // Test connection to the new database
    await connection.execute(`USE \`${databaseName}\``);
    console.log(` Successfully connected to database '${databaseName}'`);

    await connection.end();
    
    console.log('\n Database setup completed successfully!');
    console.log('\n Next steps:');
    console.log('   1. Run: npm run db:generate');
    console.log('   2. Run: npm run migrate');
    console.log('   3. Run: npm run seed (optional)');
    console.log('   4. Run: npm run dev');

  } catch (error) {
    console.error(' Error:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n Troubleshooting Access Denied Error:');
      console.log('   1. Check your MySQL username and password in .env file');
      console.log('   2. Make sure MySQL server is running');
      console.log('   3. Try connecting with MySQL Workbench or another client first');
      console.log('   4. You might need to reset your MySQL root password');
      console.log('\n To reset MySQL root password:');
      console.log('   - Stop MySQL service');
      console.log('   - Start MySQL with --skip-grant-tables');
      console.log('   - Connect and run: ALTER USER \'root\'@\'localhost\' IDENTIFIED BY \'your_new_password\';');
      console.log('   - Restart MySQL service normally');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n Troubleshooting Connection Refused:');
      console.log('   1. Make sure MySQL server is running');
      console.log('   2. Check if MySQL is listening on port 3306');
      console.log('   3. Check Windows Services for MySQL service');
    }
    
    process.exit(1);
  }
}

// Run the script
createDatabase();
