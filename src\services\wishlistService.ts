import authService from './authService';

const API_BASE = 'http://localhost:3001/api';

export interface WishlistItem {
  id: number;
  UserId: number;
  ProductId: number;
  createdAt: string;
  Product: {
    id: number;
    name: string;
    description: string;
    price: number;
    stock: number;
    Category?: {
      id: number;
      name: string;
    };
    Farmer?: {
      id: number;
      username: string;
      email: string;
    };
  };
}

export interface WishlistResponse {
  success: boolean;
  data: {
    wishlist: WishlistItem[];
    count: number;
  };
  message?: string;
}

class WishlistService {
  // Get user's wishlist
  async getWishlist(): Promise<WishlistResponse> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/wishlist`);
      return await response.json();
    } catch (error) {
      console.error('Get wishlist error:', error);
      throw new Error('Failed to fetch wishlist');
    }
  }

  // Add item to wishlist
  async addToWishlist(productId: string): Promise<any> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/wishlist/add`, {
        method: 'POST',
        body: JSON.stringify({
          productId,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Add to wishlist error:', error);
      throw new Error('Failed to add item to wishlist');
    }
  }

  // Remove item from wishlist
  async removeFromWishlist(productId: string): Promise<any> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/wishlist/remove/${productId}`, {
        method: 'DELETE',
      });

      return await response.json();
    } catch (error) {
      console.error('Remove from wishlist error:', error);
      throw new Error('Failed to remove item from wishlist');
    }
  }

  // Clear entire wishlist
  async clearWishlist(): Promise<any> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/wishlist`, {
        method: 'DELETE',
      });

      return await response.json();
    } catch (error) {
      console.error('Clear wishlist error:', error);
      throw new Error('Failed to clear wishlist');
    }
  }

  // Check if product is in wishlist
  async checkWishlist(productId: number): Promise<boolean> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/wishlist/check/${productId}`);
      const result = await response.json();
      
      return result.success ? result.data.inWishlist : false;
    } catch (error) {
      console.error('Check wishlist error:', error);
      return false;
    }
  }

  // Move wishlist items to cart
  async moveToCart(productIds: string[]): Promise<any> {
    try {
      const response = await authService.authenticatedFetch(`${API_BASE}/wishlist/move-to-cart`, {
        method: 'POST',
        body: JSON.stringify({
          productIds,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Move to cart error:', error);
      throw new Error('Failed to move items to cart');
    }
  }

  // Get wishlist item count (for navbar badge)
  async getWishlistItemCount(): Promise<number> {
    try {
      const wishlistResponse = await this.getWishlist();
      if (wishlistResponse.success) {
        return wishlistResponse.data.count || 0;
      }
      return 0;
    } catch (error) {
      console.error('Get wishlist count error:', error);
      return 0;
    }
  }
}

// Create and export a singleton instance
const wishlistService = new WishlistService();
export default wishlistService;
