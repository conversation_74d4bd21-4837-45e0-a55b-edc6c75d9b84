// Test categories API after fix
const axios = require('axios');

async function testCategoriesAPI() {
  console.log('🧪 Testing Fixed Categories API...\n');
  
  try {
    const response = await axios.get('http://localhost:3001/api/categories');
    
    console.log('✅ Categories API Response:');
    console.log('   Success:', response.data.success);
    console.log('   Message:', response.data.message);
    console.log('   Categories count:', response.data.data?.length || 0);
    
    if (response.data.success && response.data.data) {
      console.log('\n📁 Sample Categories:');
      response.data.data.slice(0, 5).forEach((cat, index) => {
        console.log(`   ${index + 1}. ${cat.name} (ID: ${cat.id})`);
      });
      
      console.log('\n🎉 Categories API is now working correctly!');
      console.log('✅ Frontend should now be able to load categories in dropdown');
    } else {
      console.log('❌ API still not returning correct format');
    }
    
  } catch (error) {
    console.error('❌ Categories API failed:', error.message);
  }
}

testCategoriesAPI();
