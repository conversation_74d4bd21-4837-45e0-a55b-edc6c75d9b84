import { useLocation } from "react-router-dom";
import { useEffect } from "react";

function NotFound() {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-black via-gray-900 to-green-900 text-white">
      <h1 className="text-6xl font-extrabold text-green-400 mb-6">404</h1>
      <p className="text-xl text-green-200 mb-8">Page Not Found</p>
      <a
        className="bg-green-500 hover:bg-green-400 text-black font-bold px-8 py-3 rounded-xl"
        href="/"
      >
        Go Home
      </a>
    </div>
  );
}

export default NotFound;
