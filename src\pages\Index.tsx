import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import Navigation from "@/components/Navigation";
import HeroSection from "@/components/HeroSection";
import ProductCard from "@/components/ProductCard";
import ProductCatalog from "@/components/ProductCatalog";
import FarmerProfile from "@/components/FarmerProfile";
import CustomerProfile from "@/components/CustomerProfile";
import ShoppingCartComponent from "@/components/ShoppingCart";
import OrderCard from "@/components/OrderCard";
import ReviewCard from "@/components/ReviewCard";
import Dashboard from "@/components/Dashboard";
import DeliveryTracking from "@/components/DeliveryTracking";
import FreshnessManager from "@/components/FreshnessManager";
import OfflineAccess from "@/components/OfflineAccess";
import PaymentOptions from "@/components/PaymentOptions";
import ProfessionalServices from "@/components/ProfessionalServices";
import AnalyticsDashboard from "@/components/AnalyticsDashboard";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Search, Filter, TrendingUp, Award, Heart, Users, ShoppingCart, Leaf, BarChart3 } from "lucide-react";
import { productsAPI, categoriesAPI, healthAPI } from "@/services/api";
import { toast } from "sonner";

const Index = () => {
  const [cartItems, setCartItems] = useState([
    {
      id: "1",
      name: "Premium Cocoa Beans",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=100&h=100&fit=crop",
      price: 2500,
      quantity: 2,
      farmer: "Paul Mebe",
      region: "Southwest Region",
      unit: "kg"
    }
  ]);

  // Fetch real products from backend
  const { data: productsData, isLoading: productsLoading, error: productsError } = useQuery({
    queryKey: ['products'],
    queryFn: () => productsAPI.getAll({ limit: 6 }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch categories from backend
  const { data: categoriesData } = useQuery({
    queryKey: ['categories'],
    queryFn: categoriesAPI.getAll,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Check backend health on component mount
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        await healthAPI.check();
        console.log('✅ Backend is connected and running');
      } catch (error) {
        console.error('❌ Backend connection failed:', error);
        toast.error('Backend connection failed. Some features may not work properly.');
      }
    };

    checkBackendHealth();
  }, []);

  // Use real products if available, otherwise fallback to mock data
  const backendProducts = productsData?.data || [];
  const realCategories = categoriesData?.data || [];

  const featuredProducts = [
    {
      name: "Premium Cocoa Beans",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=300&fit=crop",
      price: "2,500 FCFA/kg",
      region: "Southwest Region",
      farmer: "Paul Mbe",
      rating: 4.8,
      category: "Cocoa",
      inStock: true
    },
    {
      name: "Arabica Coffee",
      image: "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop",
      price: "3,200 FCFA/kg",
      region: "Northwest Region",
      farmer: "Marie Fon",
      rating: 4.9,
      category: "Coffee",
      inStock: true
    },
    {
      name: "Fresh Plantains",
      image: "https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=400&h=300&fit=crop",
      price: "800 FCFA/bunch",
      region: "Littoral Region",
      farmer: "Jean Douala",
      rating: 4.6,
      category: "Fruits",
      inStock: true
    },
    {
      name: "Cassava Roots",
      image: "https://images.unsplash.com/photo-1518843875459-f738682238a6?w=400&h=300&fit=crop",
      price: "500 FCFA/kg",
      region: "East Region",
      farmer: "Grace Ngozi",
      rating: 4.5,
      category: "Tubers",
      inStock: false
    },
    {
      name: "Palm Oil",
      image: "https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400&h=300&fit=crop",
      price: "1,800 FCFA/L",
      region: "Southwest Region",
      farmer: "Samuel Tabi",
      rating: 4.7,
      category: "Oil",
      inStock: true
    },
    {
      name: "Ground Nuts",
      image: "https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=300&fit=crop",
      price: "1,200 FCFA/kg",
      region: "Far North Region",
      farmer: "Fatima Hassan",
      rating: 4.4,
      category: "Nuts",
      inStock: true
    }
  ];

  const featuredFarmers = [
    {
      name: "Paul Mbe",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      region: "Southwest Region, Buea",
      specialties: ["Cocoa", "Coffee", "Plantains"],
      experience: "15 years farming experience, certified organic producer",
      phone: "+237 6XX XXX XXX",
      verified: true
    },
    {
      name: "Marie Fon",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
      region: "Northwest Region, Bamenda",
      specialties: ["Coffee", "Maize", "Beans"],
      experience: "20 years in coffee cultivation, cooperative leader",
      phone: "+237 6XX XXX XXX",
      verified: true
    },
    {
      name: "Jean Douala",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      region: "Littoral Region, Douala",
      specialties: ["Plantains", "Cassava", "Yams"],
      experience: "12 years sustainable farming, market vendor",
      phone: "+237 6XX XXX XXX",
      verified: false
    }
  ];

  const featuredCustomers = [
    {
      name: "Alice Tanyi",
      email: "<EMAIL>",
      phone: "+237 6XX XXX XXX",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
      location: "Douala, Littoral Region",
      joinDate: "January 2023",
      totalOrders: 24,
      favoriteCategories: ["Fruits", "Vegetables", "Coffee"],
      loyaltyPoints: 1247,
      verified: true
    },
    {
      name: "David Nguh",
      email: "<EMAIL>",
      phone: "+237 6XX XXX XXX", 
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      location: "Yaoundé, Centre Region",
      joinDate: "March 2023",
      totalOrders: 18,
      favoriteCategories: ["Cocoa", "Coffee", "Grains"],
      loyaltyPoints: 892,
      verified: true
    }
  ];

  const sampleOrders = [
    {
      orderId: "ORD-2024-001",
      date: "January 15, 2024",
      status: "delivered" as const,
      items: [
        { name: "Premium Cocoa Beans", quantity: 2, price: 2500, farmer: "Paul Mbe" },
        { name: "Fresh Plantains", quantity: 1, price: 800, farmer: "Jean Douala" }
      ],
      total: 5800,
      deliveryAddress: "123 Main Street, Douala, Littoral Region",
      estimatedDelivery: "January 16, 2024",
      trackingNumber: "TRK-2024-CM-001"
    },
    {
      orderId: "ORD-2024-002", 
      date: "January 10, 2024",
      status: "shipped" as const,
      items: [
        { name: "Arabica Coffee", quantity: 1, price: 3200, farmer: "Marie Fon" }
      ],
      total: 4700,
      deliveryAddress: "456 Oak Avenue, Yaoundé, Centre Region"
    }
  ];

  const sampleReviews = [
    {
      customerName: "Alice Tanyi",
      customerAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
      rating: 5,
      date: "January 12, 2024",
      reviewText: "Excellent quality cocoa beans! The flavor is rich and the delivery was prompt. Highly recommend Paul's products.",
      productName: "Premium Cocoa Beans",
      verified: true,
      helpful: 12
    },
    {
      customerName: "David Nguh",
      customerAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h-100&fit=crop&crop=face",
      rating: 4,
      date: "January 8, 2024", 
      reviewText: "Good coffee beans with authentic Cameroonian taste. Packaging could be improved but overall satisfied.",
      productName: "Arabica Coffee",
      verified: true,
      helpful: 8
    }
  ];

  const categories = [
    { name: "All Products", count: 245 },
    { name: "Cocoa", count: 45 },
    { name: "Coffee", count: 32 },
    { name: "Fruits", count: 78 },
    { name: "Vegetables", count: 56 },
    { name: "Grains", count: 34 }
  ];

  const sampleDelivery = {
    orderId: "ORD-2024-003",
    status: "in_transit" as const,
    currentLocation: "Douala Port - Cold Storage Facility",
    estimatedArrival: "Today, 4:30 PM",
    temperature: 3,
    preservationMethod: "Refrigerated transport with humidity control",
    driverName: "Samuel Nkem",
    driverPhone: "+237 6XX XXX XXX"
  };

  const freshnessData = [
    {
      productName: "Fresh Tomatoes",
      harvestDate: "January 18, 2024",
      shelfLife: 7,
      currentCondition: "excellent" as const,
      temperature: 4,
      humidity: 85,
      storageMethod: "Refrigerated at 2-4°C with controlled humidity",
      preservationTips: [
        "Keep refrigerated below 4°C",
        "Store in ventilated containers",
        "Separate from ethylene-producing fruits",
        "Check daily for spoilage signs"
      ]
    }
  ];

  const professionals = [
    {
      name: "Dr. Marie Konchu",
      title: "Certified Nutritionist",
      specialization: "Child nutrition and dietary planning",
      rating: 4.9,
      experience: "15+ years experience, PhD in Nutrition Science",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
      availability: "available" as const,
      consultationFee: "5,000 FCFA/session",
      languages: ["French", "English", "Bamoun"]
    },
    {
      name: "Prof. Jean Talla",
      title: "Agricultural Engineer",
      specialization: "Post-harvest storage and preservation",
      rating: 4.8,
      experience: "20+ years in agricultural engineering",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      availability: "busy" as const,
      consultationFee: "8,000 FCFA/session",
      languages: ["French", "English", "Fulfulde"]
    }
  ];

  const handleUpdateQuantity = (id: string, quantity: number) => {
    if (quantity === 0) {
      setCartItems(cartItems.filter(item => item.id !== id));
    } else {
      setCartItems(cartItems.map(item => 
        item.id === id ? { ...item, quantity } : item
      ));
    }
  };

  const handleRemoveItem = (id: string) => {
    setCartItems(cartItems.filter(item => item.id !== id));
  };

  return (
    <div className="min-h-screen">
      <Navigation 
        cartItemCount={cartItems.reduce((sum, item) => sum + item.quantity, 0)}
        isAuthenticated={true}
        userName="Alice Tanyi"
        userAvatar="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"
      />
      
      <HeroSection />
      
      {/* Enhanced Search and Filter Section */}
      <div className="bg-white/80 backdrop-blur-sm py-8 px-4 shadow-lg border-b">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <Input 
                placeholder="Search products, farmers, or regions..." 
                className="pl-10 py-3 text-lg border-2 border-green-200 focus:border-green-400 rounded-xl"
              />
            </div>
            <Button variant="outline" className="px-6 border-2 border-green-200 hover:border-green-400 rounded-xl">
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Section */}
      <div className="py-12 px-4 gradient-fresh">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div className="enhanced-card float-animation">
              <TrendingUp className="w-8 h-8 text-primary mx-auto mb-3" />
              <div className="text-3xl font-bold text-foreground">850+</div>
              <div className="text-muted-foreground">Active Products</div>
            </div>
            <div className="enhanced-card float-animation" style={{animationDelay: '0.5s'}}>
              <Award className="w-8 h-8 text-blue-600 mx-auto mb-3" />
              <div className="text-3xl font-bold text-foreground">248+</div>
              <div className="text-muted-foreground">Verified Farmers</div>
            </div>
            <div className="enhanced-card float-animation" style={{animationDelay: '1s'}}>
              <Users className="w-8 h-8 text-purple-600 mx-auto mb-3" />
              <div className="text-3xl font-bold text-foreground">5,840+</div>
              <div className="text-muted-foreground">Happy Customers</div>
            </div>
            <div className="enhanced-card float-animation" style={{animationDelay: '1.5s'}}>
              <Leaf className="w-8 h-8 text-green-600 mx-auto mb-3" />
              <div className="text-3xl font-bold text-foreground">99%</div>
              <div className="text-muted-foreground">Freshness Guarantee</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <Tabs defaultValue="products" className="space-y-8">
            <TabsList className="grid w-full grid-cols-9 lg:w-auto lg:grid-cols-9 bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg">
              <TabsTrigger value="products" className="rounded-xl">Products</TabsTrigger>
              <TabsTrigger value="catalog" className="rounded-xl">Catalog</TabsTrigger>
              <TabsTrigger value="farmers" className="rounded-xl">Farmers</TabsTrigger>
              <TabsTrigger value="customers" className="rounded-xl">Customers</TabsTrigger>
              <TabsTrigger value="orders" className="rounded-xl">Orders</TabsTrigger>
              <TabsTrigger value="reviews" className="rounded-xl">Reviews</TabsTrigger>
              <TabsTrigger value="delivery" className="rounded-xl">Delivery</TabsTrigger>
              <TabsTrigger value="services" className="rounded-xl">Services</TabsTrigger>
              <TabsTrigger value="analytics" className="rounded-xl">Analytics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="products" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Fresh from Our Farms</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Discover the finest agricultural products from across Cameroon's diverse regions, 
                  sourced directly from dedicated local farmers.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {productsLoading ? (
                  // Loading skeleton
                  Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                      <div className="bg-gray-200 h-4 rounded mb-2"></div>
                      <div className="bg-gray-200 h-4 rounded w-3/4"></div>
                    </div>
                  ))
                ) : backendProducts.length > 0 ? (
                  // Real products from backend
                  backendProducts.slice(0, 6).map((product) => (
                    <ProductCard
                      key={product.id}
                      id={product.id}
                      name={product.name}
                      image={product.images?.[0] || "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=300&fit=crop"}
                      price={`${product.price?.toLocaleString() || 0} FCFA/${product.unit || 'kg'}`}
                      region={product.farmer?.location || product.region || "Cameroon"}
                      farmer={product.farmer ? `${product.farmer.firstName} ${product.farmer.lastName}` : "Local Farmer"}
                      rating={product.rating || 4.5}
                      category={product.category?.name || product.categoryName || "General"}
                      inStock={product.stock > 0}
                      onAddToCart={(productId) => {
                        toast.success("Product added to cart!");
                        // You can add cart logic here
                      }}
                    />
                  ))
                ) : (
                  // Fallback to mock data if backend is not available
                  featuredProducts.slice(0, 6).map((product, index) => (
                    <ProductCard key={index} {...product} />
                  ))
                )}
              </div>
              
              <div className="text-center">
                <Button size="lg" className="px-8 gradient-agricultural text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  View All Products
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="catalog" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Complete Product Catalog</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Browse our extensive collection of agricultural products with advanced filtering and search capabilities.
                </p>
              </div>
              
              <ProductCatalog />
            </TabsContent>
            
            <TabsContent value="farmers" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-green-600 to-amber-600 bg-clip-text text-transparent">Meet Our Farmers</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Connect with experienced farmers from across Cameroon who are passionate 
                  about sustainable agriculture and quality produce.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredFarmers.map((farmer, index) => (
                  <FarmerProfile key={index} {...farmer} />
                ))}
              </div>
              
              <div className="text-center">
                <Button size="lg" className="px-8 gradient-earth text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  View All Farmers
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="customers" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Our Valued Customers</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Meet the amazing customers who support our agricultural community
                  and enjoy fresh, locally-sourced products.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredCustomers.map((customer, index) => (
                  <CustomerProfile key={index} {...customer} />
                ))}
                
                <div className="md:col-span-1">
                  <ShoppingCartComponent 
                    items={cartItems}
                    onUpdateQuantity={handleUpdateQuantity}
                    onRemoveItem={handleRemoveItem}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="orders" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Order Management</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Track and manage orders from placement to delivery with real-time updates.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                {sampleOrders.map((order, index) => (
                  <OrderCard key={index} {...order} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="reviews" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text text-transparent">Customer Reviews</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Read authentic reviews from our customers about their experience
                  with our products and farmers.
                </p>
              </div>
              
              <div className="space-y-6 max-w-4xl mx-auto">
                {sampleReviews.map((review, index) => (
                  <ReviewCard key={index} {...review} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="delivery" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">Smart Delivery & Freshness</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Advanced cold-chain delivery and freshness management for optimal product quality.
                </p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <DeliveryTracking {...sampleDelivery} />
                <FreshnessManager products={freshnessData} />
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <OfflineAccess />
                <PaymentOptions />
              </div>
            </TabsContent>

            <TabsContent value="services" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Professional Advisory Services</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Get expert guidance from certified nutritionists, agronomists, and food preservation specialists.
                </p>
              </div>
              
              <ProfessionalServices professionals={professionals} />
            </TabsContent>

            <TabsContent value="analytics" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Platform Analytics & Insights</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Comprehensive analytics dashboard showing platform performance, sales trends, and farmer insights.
                </p>
              </div>
              
              <AnalyticsDashboard />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Enhanced Call to Action */}
      <div className="gradient-earth text-white py-16 px-4 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="max-w-4xl mx-auto text-center relative z-10">
          <h2 className="text-4xl font-bold mb-6">Join Cameroon's Digital Agricultural Revolution</h2>
          <p className="text-xl mb-8 text-orange-100">
            Whether you're a farmer looking to reach more customers, a buyer seeking 
            fresh local produce, or need professional guidance - our comprehensive platform 
            serves all your agricultural needs across Cameroon.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-orange-600 hover:bg-orange-50 font-semibold px-8 rounded-2xl shadow-lg">
              Start Selling Today
            </Button>
            <Button variant="outline" size="lg" className="border-2 border-white text-white hover:bg-white hover:text-orange-600 font-semibold px-8 rounded-2xl">
              Browse Fresh Products
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Footer */}
      <footer className="gradient-agricultural text-primary-foreground py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="font-bold text-lg mb-4 flex items-center gap-2">
                <Leaf className="w-6 h-6" />
                AgriConnect Cameroon
              </h3>
              <p className="text-primary-foreground/80">
                Revolutionizing agriculture through technology, connecting farmers and buyers 
                across all regions of Cameroon for a sustainable agricultural future.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Products & Services</h4>
              <ul className="space-y-2 text-primary-foreground/80">
                <li>Fresh Produce</li>
                <li>Cold Chain Delivery</li>
                <li>Professional Advisory</li>
                <li>Offline Access Solutions</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Technology Features</h4>
              <ul className="space-y-2 text-primary-foreground/80">
                <li>Smart Freshness Tracking</li>
                <li>Mobile Money Integration</li>
                <li>SMS/USSD Support</li>
                <li>Multi-language Support</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-primary-foreground/80">
                <li>24/7 Help Center</li>
                <li>Rural Area Support</li>
                <li>Professional Network</li>
                <li>Community Forums</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-primary-foreground/80">
            <p>&copy; 2024 AgriConnect Cameroon. Empowering agriculture through innovation across all 10 regions of Cameroon.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
