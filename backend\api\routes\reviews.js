// Import required modules
const express = require('express');
const { body } = require('express-validator');
const reviewController = require('../controllers/reviewController');
const { protect } = require('../../middleware/auth');
const validate = require('../middleware/validate');

// Create a router
const router = express.Router();

// Define review routes
router.post(
  '/',
  protect,
  [
    body('productId').isUUID().withMessage('Product ID must be a valid UUID'),
    body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('comment').isString().trim().isLength({ min: 3 }).withMessage('Comment must be at least 3 characters long')
  ],
  validate,
  reviewController.createReview
);

router.get('/product/:productId', reviewController.getReviewsForProduct);


router.put(
  '/:id',
  protect,
  [
    body('rating').optional().isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('comment').optional().isString().trim().isLength({ min: 3 }).withMessage('Comment must be at least 3 characters long')
  ],
  validate,
  reviewController.updateReview
);

router.delete('/:id', protect, reviewController.deleteReview);

// Export the router
module.exports = router;