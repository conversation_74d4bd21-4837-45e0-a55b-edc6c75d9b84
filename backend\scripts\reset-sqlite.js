const fs = require('fs');
const path = require('path');
const { connectDB, getSequelize } = require('../config/database');

async function resetDatabase() {
  try {
    console.log('🗑️ Resetting SQLite database...');

    // Remove existing database files
    const dbFiles = [
      path.join(__dirname, '../database.sqlite'),
      path.join(__dirname, '../data/database.sqlite'),
      path.join(__dirname, '../simple-database.sqlite'),
      path.join(__dirname, '../database/cameroon_farm_connect.sqlite')
    ];

    dbFiles.forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        console.log(`✅ Removed ${file}`);
      }
    });

    console.log('🔄 Creating fresh database...');

    // Connect to database (this will create a new SQLite file)
    const sequelize = await connectDB();
    console.log('✅ Database connection established');

    // Force sync to create all tables fresh
    await sequelize.sync({ force: true });
    console.log('✅ Database tables created');

    console.log('🎉 Database reset and setup completed');
  } catch (error) {
    console.error('❌ Reset failed:', error);
    process.exit(1);
  }
}

resetDatabase();