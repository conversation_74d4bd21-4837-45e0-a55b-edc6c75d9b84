@echo off
echo ========================================
echo Recreating Cameroon Farm Connect Database
echo ========================================
echo.

echo Step 1: Creating database structure...
mysql -u root -p < recreate-database.sql
if %errorlevel% neq 0 (
    echo ERROR: Failed to create database structure
    pause
    exit /b 1
)
echo ✅ Database structure created successfully!
echo.

echo Step 2: Adding all products...
mysql -u root -p < add-all-products.sql
if %errorlevel% neq 0 (
    echo ERROR: Failed to add products
    pause
    exit /b 1
)
echo ✅ All products added successfully!
echo.

echo ========================================
echo Database Recreation Completed Successfully!
echo ========================================
echo.
echo Your database now contains:
echo - 7 agricultural categories
echo - 56 products (8 per category)
echo - 1 sample farmer
echo - All tables properly structured
echo.
echo You can now start your backend server:
echo   node server.js
echo.
pause
