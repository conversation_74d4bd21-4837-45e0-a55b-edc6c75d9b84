// Import necessary modules
const Order = require('../../data/models/order');
const OrderItem = require('../../data/models/orderItem');
const Product = require('../../data/models/product');
const orderService = require('../../business/services/orderService');

// Create a new order
exports.createOrder = async (req, res, next) => {
  try {
    const order = await orderService.createOrder(req.user.id, req.body);
    res.status(201).json(order);
  } catch (error) {
    next(error);
  }
};

// Get all orders for the authenticated user
exports.getUserOrders = async (req, res, next) => {
  try {
    // Find all orders for the user, including order items and products
    const orders = await Order.findAll({
      where: { userId: req.user.id },
      include: [
        {
          model: OrderItem,
          include: [{ model: Product, attributes: ['id', 'name'] }],
        },
      ],
    });

    // Return the orders
    res.json(orders);
  } catch (error) {
    next(error);
  }
};

// Get a specific order by ID
exports.getOrderById = async (req, res, next) => {
  try {
    // Find the order by id, including order items and products
    const order = await Order.findOne({
      where: { id: req.params.id, userId: req.user.id },
      include: [
        {
          model: OrderItem,
          include: [{ model: Product, attributes: ['id', 'name'] }],
        },
      ],
    });

    // If order not found, return 404
    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    // Return the order
    res.json(order);
  } catch (error) {
    next(error);
  }
};

// Update order status (admin only)
exports.updateOrderStatus = async (req, res, next) => {
  try {
    // Find the order by id
    const order = await Order.findByPk(req.params.id);

    // If order not found, return 404
    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    // Update the order status
    order.status = req.body.status;
    await order.save();

    // Return the updated order
    res.json(order);
  } catch (error) {
    next(error);
  }
};