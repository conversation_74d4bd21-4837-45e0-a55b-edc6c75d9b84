const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const config = require('../config/config');
const { ApiError } = require('./errorHandler');

// Ensure upload directories exist
const uploadDir = path.join(__dirname, '../uploads');
const productImagesDir = path.join(uploadDir, 'products');
const userAvatarsDir = path.join(uploadDir, 'avatars');

[uploadDir, productImagesDir, userAvatarsDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Check file type
  if (config.upload.allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ApiError(`Invalid file type. Allowed types: ${config.upload.allowedTypes.join(', ')}`, 400), false);
  }
};

// Storage configuration for product images
const productStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, productImagesDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `product-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// Storage configuration for user avatars
const avatarStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, userAvatarsDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `avatar-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// Multer configurations
const productUpload = multer({
  storage: productStorage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 5 // Maximum 5 images per product
  }
});

const avatarUpload = multer({
  storage: avatarStorage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 1 // Only one avatar per user
  }
});

// Middleware for handling multiple product images
const uploadProductImages = (req, res, next) => {
  const upload = productUpload.array('images', 5);
  
  upload(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return next(new ApiError('File size too large. Maximum size is 10MB per file.', 400));
      }
      if (err.code === 'LIMIT_FILE_COUNT') {
        return next(new ApiError('Too many files. Maximum 5 images allowed.', 400));
      }
      if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        return next(new ApiError('Unexpected field name. Use "images" for product images.', 400));
      }
      return next(new ApiError(err.message, 400));
    } else if (err) {
      return next(err);
    }
    
    // Process uploaded files
    if (req.files && req.files.length > 0) {
      req.uploadedImages = req.files.map(file => ({
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        path: `/uploads/products/${file.filename}`
      }));
    }
    
    next();
  });
};

// Middleware for handling single avatar upload
const uploadAvatar = (req, res, next) => {
  const upload = avatarUpload.single('avatar');
  
  upload(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return next(new ApiError('File size too large. Maximum size is 10MB.', 400));
      }
      return next(new ApiError(err.message, 400));
    } else if (err) {
      return next(err);
    }
    
    // Process uploaded file
    if (req.file) {
      req.uploadedAvatar = {
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        path: `/uploads/avatars/${req.file.filename}`
      };
    }
    
    next();
  });
};

// Helper function to delete files
const deleteFile = (filePath) => {
  try {
    const fullPath = path.join(__dirname, '../uploads', filePath.replace('/uploads/', ''));
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

// Helper function to delete multiple files
const deleteFiles = (filePaths) => {
  const results = [];
  filePaths.forEach(filePath => {
    results.push(deleteFile(filePath));
  });
  return results;
};

// Cleanup middleware for failed uploads
const cleanupFailedUploads = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // If response is an error and we have uploaded files, clean them up
    if (res.statusCode >= 400) {
      if (req.uploadedImages) {
        req.uploadedImages.forEach(image => {
          deleteFile(image.path);
        });
      }
      if (req.uploadedAvatar) {
        deleteFile(req.uploadedAvatar.path);
      }
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

// Image optimization middleware (basic)
const optimizeImages = (req, res, next) => {
  // This is a placeholder for image optimization
  // In production, you might want to use sharp or similar library
  // to resize and optimize images
  next();
};

module.exports = {
  uploadProductImages,
  uploadAvatar,
  deleteFile,
  deleteFiles,
  cleanupFailedUploads,
  optimizeImages
};
