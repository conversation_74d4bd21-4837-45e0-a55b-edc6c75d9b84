// Final category update - Remove 3 categories
const mysql = require('mysql2/promise');

async function updateCategoriesFinal() {
  console.log('🔄 Final Category Update - Removing 3 Categories...\n');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to database');
    
    // Step 1: Remove products from categories we're deleting
    console.log('1. Removing products from unwanted categories...');
    
    await connection.execute(`
      DELETE FROM products WHERE categoryId IN (
        SELECT id FROM categories WHERE name IN ('Livestock', 'Other Products', 'Other Vegetables', 'Leafy Vegetables')
      )
    `);
    
    console.log('   ✅ Removed products from unwanted categories');
    
    // Step 2: Delete the unwanted categories
    console.log('2. Deleting unwanted categories...');
    
    await connection.execute(`
      DELETE FROM categories WHERE name IN ('Livestock', 'Other Products', 'Other Vegetables', 'Leafy Vegetables')
    `);
    
    console.log('   ✅ Deleted unwanted categories');
    
    // Step 3: Update existing categories if needed
    console.log('3. Updating existing categories...');
    
    // Update Vegetables category to include leafy vegetables
    await connection.execute(`
      UPDATE categories 
      SET description = 'Tomato, pepper, onion, garlic, carrot, okra, cucumber, spinach, lettuce, cabbage'
      WHERE name = 'Vegetables'
    `);
    
    // If Vegetables category doesn't exist, create it
    await connection.execute(`
      INSERT IGNORE INTO categories (id, name, description, isActive, createdAt, updatedAt)
      VALUES ('cat-vegetables', 'Vegetables', 'Tomato, pepper, onion, garlic, carrot, okra, cucumber, spinach, lettuce, cabbage', true, NOW(), NOW())
    `);
    
    console.log('   ✅ Updated/created Vegetables category');
    
    // Step 4: Ensure we have the final 7 categories
    console.log('4. Ensuring final 7 categories exist...');
    
    const finalCategories = [
      {
        id: 'cat-cereals',
        name: 'Cereals & Grains',
        description: 'Maize, rice, millet, sorghum, wheat'
      },
      {
        id: 'cat-roots',
        name: 'Root Crops & Tubers',
        description: 'Cassava, yam, sweet potato, Irish potato, cocoyam, plantain'
      },
      {
        id: 'cat-fruits',
        name: 'Fruits',
        description: 'Banana, pineapple, mango, papaya, avocado, citrus fruits, bush mango'
      },
      {
        id: 'cat-vegetables',
        name: 'Vegetables',
        description: 'Tomato, pepper, onion, garlic, carrot, okra, cucumber, spinach, lettuce, cabbage'
      },
      {
        id: 'cat-legumes',
        name: 'Legumes & Beans',
        description: 'Groundnuts, cowpea, beans, soybeans, bambara groundnut'
      },
      {
        id: 'cat-cash',
        name: 'Cash Crops',
        description: 'Cocoa, coffee, cotton, palm oil'
      },
      {
        id: 'cat-spices',
        name: 'Spices & Herbs',
        description: 'Ginger, pepper, basil, lemongrass, mint, garlic'
      }
    ];
    
    for (const category of finalCategories) {
      await connection.execute(`
        INSERT IGNORE INTO categories (id, name, description, isActive, createdAt, updatedAt)
        VALUES (?, ?, ?, true, NOW(), NOW())
      `, [category.id, category.name, category.description]);
    }
    
    console.log('   ✅ Ensured all 7 final categories exist');
    
    // Step 5: Create sample products for the remaining categories
    console.log('5. Creating sample products...');
    
    // Get farmer ID
    const [farmers] = await connection.execute('SELECT id FROM farmers LIMIT 1');
    let farmerId = 'sample-farmer-123';
    
    if (farmers.length === 0) {
      // Create sample farmer if doesn't exist
      const userId = 'sample-user-123';
      
      await connection.execute(`
        INSERT IGNORE INTO users (id, firstName, lastName, email, password, role, status, emailVerified, createdAt, updatedAt)
        VALUES (?, 'Sample', 'Farmer', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'FARMER', 'ACTIVE', true, NOW(), NOW())
      `, [userId]);
      
      await connection.execute(`
        INSERT IGNORE INTO farmers (id, userId, farmName, farmLocation, verified, rating, totalSales, createdAt, updatedAt)
        VALUES (?, ?, 'Green Valley Farm', 'Douala, Cameroon', true, 4.5, 0, NOW(), NOW())
      `, [farmerId, userId]);
      
      console.log('   ✅ Created sample farmer');
    } else {
      farmerId = farmers[0].id;
    }
    
    // Sample products for each category
    const sampleProducts = [
      // Cereals & Grains
      ['Yellow Maize (Corn)', 'High-quality yellow corn, locally grown', 400, 'kg', 500, 'cat-cereals'],
      ['Premium White Rice', 'High quality lowland white rice', 800, 'kg', 200, 'cat-cereals'],
      
      // Root Crops & Tubers
      ['Fresh Cassava', 'Sweet cassava variety, freshly harvested', 300, 'kg', 300, 'cat-roots'],
      ['Sweet Yam', 'Fresh yam tubers, excellent for cooking', 450, 'kg', 200, 'cat-roots'],
      
      // Fruits
      ['Sweet Bananas', 'Ripe yellow bananas, perfect for eating', 250, 'bunch', 150, 'cat-fruits'],
      ['Fresh Pineapple', 'Sweet tropical pineapples, locally grown', 600, 'piece', 100, 'cat-fruits'],
      
      // Vegetables
      ['Fresh Tomatoes', 'Organic red tomatoes, freshly harvested', 500, 'kg', 200, 'cat-vegetables'],
      ['Fresh Spinach', 'Tender green spinach leaves, organic', 400, 'kg', 80, 'cat-vegetables'],
      ['Red Onions', 'Fresh red bulb onions, locally grown', 700, 'kg', 150, 'cat-vegetables'],
      
      // Legumes & Beans
      ['Groundnuts (Peanuts)', 'Spanish variety groundnuts, fresh', 900, 'kg', 200, 'cat-legumes'],
      ['Black-eyed Peas', 'Fresh cowpeas, high protein content', 600, 'kg', 180, 'cat-legumes'],
      
      // Cash Crops
      ['Cocoa Beans', 'Premium cocoa beans, sun-dried', 2000, 'kg', 50, 'cat-cash'],
      ['Coffee Beans', 'Arabica coffee beans, mountain grown', 3000, 'kg', 30, 'cat-cash'],
      
      // Spices & Herbs
      ['Fresh Ginger', 'Yellow ginger variety, aromatic', 1500, 'kg', 80, 'cat-spices'],
      ['Hot Pepper', 'Fresh hot peppers, locally grown', 800, 'kg', 60, 'cat-spices']
    ];
    
    let createdCount = 0;
    
    for (let i = 0; i < sampleProducts.length; i++) {
      const [name, description, price, unit, stock, categoryId] = sampleProducts[i];
      const productId = 'product-final-' + (i + 1);
      
      try {
        await connection.execute(`
          INSERT IGNORE INTO products (
            id, name, description, price, unit, stock, minOrder, images, status, 
            farmerId, categoryId, isOrganic, isFairTrade, rating, totalReviews, 
            totalSold, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, 1, ?, 'ACTIVE', ?, ?, false, false, 0, 0, 0, NOW(), NOW())
        `, [
          productId, name, description, price, unit, stock,
          JSON.stringify(['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=400&h=300&fit=crop&q=80']),
          farmerId, categoryId
        ]);
        
        createdCount++;
        console.log(`   ✅ Created: ${name}`);
        
      } catch (error) {
        console.log(`   ⚠️ Product may already exist: ${name}`);
      }
    }
    
    // Step 6: Final verification
    const [categoryCount] = await connection.execute('SELECT COUNT(*) as count FROM categories');
    const [productCount] = await connection.execute('SELECT COUNT(*) as count FROM products');
    
    console.log(`\n🎉 Final update completed successfully!`);
    console.log(`   Categories: ${categoryCount[0].count} (should be 7)`);
    console.log(`   Products: ${productCount[0].count} sample products`);
    
    console.log('\n📝 Final 7 categories:');
    const [finalCats] = await connection.execute('SELECT name FROM categories ORDER BY name');
    finalCats.forEach((cat, index) => {
      console.log(`   ${index + 1}. ${cat.name}`);
    });
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Update failed:', error.message);
  }
  
  process.exit(0);
}

updateCategoriesFinal();
