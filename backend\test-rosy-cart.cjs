// Test cart functionality for Rosy's account
const axios = require('axios');

async function testRosyCart() {
  console.log('🧪 Testing Cart for Rosy\'s Account...\n');
  
  try {
    // Login as Rosy
    console.log('🔐 Logging in as Rosy...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'Test123!' // Use your actual password
    });
    
    const token = loginResponse.data.data.accessToken;
    console.log('✅ Login successful\n');
    
    // Get a product to test with
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products;
    const testProduct = products[0];
    
    console.log(`🎯 Testing with: ${testProduct.name} (ID: ${testProduct.id})\n`);
    
    // Test Add to Cart
    console.log('🛒 Testing Add to Cart...');
    try {
      const cartResponse = await axios.post('http://localhost:3001/api/cart/add', {
        productId: testProduct.id,
        quantity: 1
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Add to cart response:', cartResponse.data);
    } catch (cartError) {
      console.log('❌ Add to cart failed:');
      console.log('   Status:', cartError.response?.status);
      console.log('   Message:', cartError.response?.data?.message || cartError.message);
      console.log('   Full error:', cartError.response?.data);
    }
    
    // Test Get Cart
    console.log('\n🛒 Testing Get Cart...');
    try {
      const getCartResponse = await axios.get('http://localhost:3001/api/cart', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ Get cart response:', getCartResponse.data);
    } catch (getCartError) {
      console.log('❌ Get cart failed:', getCartError.response?.data || getCartError.message);
    }
    
    // Test Add to Wishlist
    console.log('\n❤️ Testing Add to Wishlist...');
    try {
      const wishlistResponse = await axios.post('http://localhost:3001/api/wishlist/add', {
        productId: testProduct.id
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Add to wishlist response:', wishlistResponse.data);
    } catch (wishlistError) {
      console.log('❌ Add to wishlist failed:');
      console.log('   Status:', wishlistError.response?.status);
      console.log('   Message:', wishlistError.response?.data?.message || wishlistError.message);
    }
    
    // Test Get Wishlist
    console.log('\n❤️ Testing Get Wishlist...');
    try {
      const getWishlistResponse = await axios.get('http://localhost:3001/api/wishlist', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ Get wishlist response:', getWishlistResponse.data);
    } catch (getWishlistError) {
      console.log('❌ Get wishlist failed:', getWishlistError.response?.data || getWishlistError.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testRosyCart();
