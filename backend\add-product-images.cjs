// Add real images to all products in database
const mysql = require('mysql2/promise');

async function addProductImages() {
  console.log('🖼️ Adding Real Images to All Products...\n');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to database');
    
    // Define high-quality images for each product type
    const productImages = {
      // Cereals & Grains
      'Yellow Maize': ['https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=500&h=400&fit=crop&q=80'],
      'White Rice': ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=500&h=400&fit=crop&q=80'],
      'Red Sorghum': ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80'],
      'Pearl Millet': ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80'],
      'Wheat Flour': ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80'],
      'Brown Rice': ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=500&h=400&fit=crop&q=80'],
      'White Maize': ['https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=500&h=400&fit=crop&q=80'],
      'Fonio Grain': ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80'],
      
      // Root Crops & Tubers
      'Fresh Cassava': ['https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=500&h=400&fit=crop&q=80'],
      'Sweet Yam': ['https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=500&h=400&fit=crop&q=80'],
      'Irish Potatoes': ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80'],
      'Sweet Potatoes': ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80'],
      'Cocoyam': ['https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=500&h=400&fit=crop&q=80'],
      'Ripe Plantains': ['https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=500&h=400&fit=crop&q=80'],
      'Green Plantains': ['https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=500&h=400&fit=crop&q=80'],
      'Water Yam': ['https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=500&h=400&fit=crop&q=80'],
      
      // Fruits
      'Sweet Bananas': ['https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=500&h=400&fit=crop&q=80'],
      'Fresh Pineapple': ['https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=500&h=400&fit=crop&q=80'],
      'Ripe Mangoes': ['https://images.unsplash.com/photo-1553279768-865429fa0078?w=500&h=400&fit=crop&q=80'],
      'Fresh Papaya': ['https://images.unsplash.com/photo-1617112848923-cc2234396a8d?w=500&h=400&fit=crop&q=80'],
      'Avocados': ['https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=500&h=400&fit=crop&q=80'],
      'Bush Mango': ['https://images.unsplash.com/photo-1553279768-865429fa0078?w=500&h=400&fit=crop&q=80'],
      'Oranges': ['https://images.unsplash.com/photo-1547514701-42782101795e?w=500&h=400&fit=crop&q=80'],
      'Lemons': ['https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=500&h=400&fit=crop&q=80'],
      
      // Vegetables
      'Fresh Tomatoes': ['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=500&h=400&fit=crop&q=80'],
      'Fresh Spinach': ['https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=500&h=400&fit=crop&q=80'],
      'Red Onions': ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80'],
      'Green Cabbage': ['https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?w=500&h=400&fit=crop&q=80'],
      'Fresh Carrots': ['https://images.unsplash.com/photo-1445282768818-728615cc910a?w=500&h=400&fit=crop&q=80'],
      'Green Peppers': ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=500&h=400&fit=crop&q=80'],
      'Fresh Okra': ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=500&h=400&fit=crop&q=80'],
      'Cucumber': ['https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=500&h=400&fit=crop&q=80'],
      
      // Legumes & Beans
      'Groundnuts': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'],
      'Black-eyed Peas': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'],
      'Red Kidney Beans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'],
      'Soybeans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'],
      'White Beans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'],
      'Bambara Nuts': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'],
      'Green Beans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'],
      'Lima Beans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'],
      
      // Cash Crops
      'Cocoa Beans': ['https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=500&h=400&fit=crop&q=80'],
      'Coffee Beans': ['https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=500&h=400&fit=crop&q=80'],
      'Raw Cotton': ['https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80'],
      'Palm Oil': ['https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80'],
      'Palm Kernels': ['https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80'],
      'Rubber Latex': ['https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80'],
      'Cocoa Pods': ['https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=500&h=400&fit=crop&q=80'],
      'Coffee Cherries': ['https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=500&h=400&fit=crop&q=80'],
      
      // Spices & Herbs
      'Fresh Ginger': ['https://images.unsplash.com/photo-1599639832862-bd197d5c1b5d?w=500&h=400&fit=crop&q=80'],
      'Hot Pepper': ['https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=500&h=400&fit=crop&q=80'],
      'Fresh Garlic': ['https://images.unsplash.com/photo-1553978297-833d24758ba5?w=500&h=400&fit=crop&q=80'],
      'Fresh Basil': ['https://images.unsplash.com/photo-1618375569909-3c8616cf7733?w=500&h=400&fit=crop&q=80'],
      'Lemongrass': ['https://images.unsplash.com/photo-1618375569909-3c8616cf7733?w=500&h=400&fit=crop&q=80'],
      'Fresh Mint': ['https://images.unsplash.com/photo-1618375569909-3c8616cf7733?w=500&h=400&fit=crop&q=80'],
      'Turmeric Root': ['https://images.unsplash.com/photo-1599639832862-bd197d5c1b5d?w=500&h=400&fit=crop&q=80'],
      'Scotch Bonnet': ['https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=500&h=400&fit=crop&q=80']
    };
    
    // Get all products
    const [products] = await connection.execute('SELECT id, name FROM products');
    console.log(`\n📦 Found ${products.length} products to update`);
    
    let updatedCount = 0;
    
    // Update each product with appropriate image
    for (const product of products) {
      const productName = product.name;
      let imageUrl = null;
      
      // Find matching image by exact name or partial match
      if (productImages[productName]) {
        imageUrl = productImages[productName][0];
      } else {
        // Try partial matching
        for (const [key, value] of Object.entries(productImages)) {
          if (productName.toLowerCase().includes(key.toLowerCase()) || 
              key.toLowerCase().includes(productName.toLowerCase())) {
            imageUrl = value[0];
            break;
          }
        }
      }
      
      // Fallback to a generic agricultural image
      if (!imageUrl) {
        imageUrl = 'https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=500&h=400&fit=crop&q=80';
      }
      
      try {
        // Update product with image
        await connection.execute(`
          UPDATE products 
          SET images = ? 
          WHERE id = ?
        `, [JSON.stringify([imageUrl]), product.id]);
        
        updatedCount++;
        console.log(`   ✅ Updated: ${productName}`);
        
      } catch (error) {
        console.log(`   ❌ Failed to update ${productName}:`, error.message);
      }
    }
    
    console.log(`\n🎉 Image update completed!`);
    console.log(`   Updated: ${updatedCount} products`);
    console.log(`   Total products: ${products.length}`);
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Image update failed:', error.message);
  }
  
  process.exit(0);
}

addProductImages();
