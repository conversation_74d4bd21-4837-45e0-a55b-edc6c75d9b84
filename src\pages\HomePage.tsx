import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import ModernHeroSection from '@/components/ModernHeroSection';
import ProductCard from '@/components/ProductCard';
import CategoryCard from '@/components/CategoryCard';
import DealsSection from '@/components/DealsSection';
import NewsletterSection from '@/components/NewsletterSection';
import wishlistService from '@/services/wishlistService';
import {
  Truck,
  HeadphonesIcon,
  RotateCcw
} from 'lucide-react';
import { toast } from 'sonner';

// API functions (connected to backend)
const API_BASE = 'http://localhost:3001/api';

const fetchProducts = async () => {
  const response = await fetch(`${API_BASE}/products`);
  if (!response.ok) throw new Error('Failed to fetch products');
  const result = await response.json();
  // Backend returns { success: true, data: { products: [...], pagination: {...} } }
  return result.success ? result.data.products : [];
};

const fetchCategories = async () => {
  const response = await fetch(`${API_BASE}/categories`);
  if (!response.ok) throw new Error('Failed to fetch categories');
  const result = await response.json();
  // Backend returns { success: true, data: [...] }
  return result.success ? result.data : [];
};

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  // Fetch real data from backend
  const { data: productsData, isLoading: productsLoading } = useQuery({
    queryKey: ['products'],
    queryFn: fetchProducts,
    staleTime: 5 * 60 * 1000,
  });

  const { data: categoriesData } = useQuery({
    queryKey: ['categories'],
    queryFn: fetchCategories,
    staleTime: 10 * 60 * 1000,
  });

  const handleAddToCart = async (productId: string) => {
    try {
      // The ProductCard already handles the cart service call
      // This is just for additional actions if needed
      console.log(`Product ${productId} added to cart`);
    } catch (error) {
      console.error('Error in cart handler:', error);
    }
  };

  const handleAddToWishlist = async (productId: string) => {
    try {
      // Call wishlist service
      await wishlistService.addToWishlist(productId);
      console.log(`Product ${productId} added to wishlist`);
      // Trigger a refresh of wishlist count if needed
      window.dispatchEvent(new CustomEvent('wishlistUpdated'));
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      toast.error('Failed to add to wishlist');
    }
  };

  const handleCompare = (productId: string) => {
    toast.success('Product added to compare!');
    console.log(`Added product ${productId} to compare`);
  };

  const handleViewProduct = (productId: string) => {
    navigate(`/products/${productId}`);
  };

  const handleCategoryClick = (categoryId: string) => {
    navigate(`/products?category=${categoryId}`);
  };

  const handleSearch = (query: string) => {
    navigate(`/products?search=${encodeURIComponent(query)}`);
  };

  const handleShopNow = () => {
    navigate('/products');
  };

  const handleMeetFarmers = () => {
    navigate('/farmers');
  };

  const handleViewAllDeals = () => {
    navigate('/products?deals=true');
  };

  const handleNewsletterSubscribe = async (email: string) => {
    // Implement newsletter subscription logic
    console.log('Newsletter subscription:', email);
    // You can add API call here
  };

  // Extract products array from API response
  const products = productsData || [];
  const categories = categoriesData || [];
  const featuredProducts = products.slice(0, 8);
  const featuredCategories = categories.slice(0, 8);

  // Mock deals data (you can replace with real API data)
  const mockDeals = products.slice(0, 4).map((product: any, index: number) => ({
    id: product.id?.toString() || `deal-${index}`,
    name: product.name,
    image: product.images && product.images.length > 0 ? product.images[0] : '/placeholder.svg',
    originalPrice: product.price * 1.3,
    salePrice: product.price,
    discount: 25,
    rating: 4.5,
    reviewCount: 32,
    vendor: 'NestFood',
    sold: 90,
    total: 120,
    timeLeft: {
      days: 3,
      hours: 2,
      minutes: 43,
      seconds: 29
    }
  }));

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Modern Hero Section */}
      <ModernHeroSection
        onSearch={handleSearch}
        onShopNow={handleShopNow}
        onMeetFarmers={handleMeetFarmers}
        featuredProduct={products.length > 0 ? products[0] : null}
      />
      {/* Featured Categories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Categories</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">Discover fresh produce from local Cameroon farmers across different categories</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
            {featuredCategories.map((category: any) => (
              <CategoryCard
                key={category.id}
                id={category.id?.toString()}
                name={category.name}
                description={category.description}
                image={category.imageUrl || '/placeholder.svg'}
                itemCount={category.productCount}
                onClick={handleCategoryClick}
                variant="compact"
              />
            ))}
          </div>
        </div>
      </section>
      {/* Popular Products */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Popular Products</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">Fresh, organic produce directly from Cameroon's finest farmers</p>
          </div>

          {/* Product Filter Tabs */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <Button variant="default" className="bg-green-600 text-white">All</Button>
            <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">Fruits</Button>
            <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">Vegetables</Button>
            <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">Grains</Button>
            <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">Dairy</Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product: any) => (
              <ProductCard
                key={product.id}
                id={product.id?.toString()}
                name={product.name}
                image={product.images && product.images.length > 0 ? product.images[0] : '/placeholder.svg'}
                price={`${parseFloat(product.price || 0).toLocaleString()} FCFA`}
                originalPrice={product.originalPrice ? `${parseFloat(product.originalPrice).toLocaleString()} FCFA` : undefined}
                region={product.region || 'Cameroon'}
                farmer={product.farmer || 'Local Farmer'}
                rating={product.rating || 4.5}
                reviewCount={product.reviewCount || 32}
                category={product.category || 'Fresh Produce'}
                inStock={product.inStock !== false}
                isOnSale={product.isOnSale}
                isHot={product.isHot}
                isNew={product.isNew}
                discount={product.discount}
                onAddToCart={handleAddToCart}
                onAddToWishlist={handleAddToWishlist}
                onCompare={handleCompare}
                onViewProduct={handleViewProduct}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Deals Section */}
      <DealsSection
        deals={mockDeals}
        onViewAllDeals={handleViewAllDeals}
        onAddToCart={handleAddToCart}
      />

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Truck className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Free Delivery</h3>
              <p className="text-gray-600">24/7 amazing services</p>
              <p className="text-sm text-gray-500 mt-1">Orders $50 or more</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <HeadphonesIcon className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Great daily deal</h3>
              <p className="text-gray-600">When you sign up</p>
              <p className="text-sm text-gray-500 mt-1">Mega Discounts</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <RotateCcw className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Easy returns</h3>
              <p className="text-gray-600">Within 30 days</p>
              <p className="text-sm text-gray-500 mt-1">Safe delivery</p>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <NewsletterSection
        onSubscribe={handleNewsletterSubscribe}
      />

      <Footer />
    </div>
  );
};

export default HomePage;
