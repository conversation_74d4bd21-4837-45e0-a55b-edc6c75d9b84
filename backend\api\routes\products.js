// Import necessary modules
const express = require('express');
const productController = require('../controllers/productController');
const { protect } = require('../../middleware/auth');
const { validateProduct, validateUUID } = require('../../middleware/validation');
const { uploadProductImages, cleanupFailedUploads } = require('../../middleware/upload');

// Create a new router
const router = express.Router();

// Public routes
router.get('/', productController.getAllProducts);
router.get('/:id', validateUUID('id'), productController.getProductById);
router.get('/farmer/:farmerId', validateUUID('farmerId'), productController.getProductsByFarmer);

// Protected routes (require authentication)
router.use(protect);

// Product CRUD operations
router.post('/', cleanupFailedUploads, uploadProductImages, validateProduct, productController.createProduct);
router.put('/:id', validateUUID('id'), productController.updateProduct);
router.delete('/:id', validateUUID('id'), productController.deleteProduct);

// Image management routes
router.post('/:id/images', validateUUID('id'), cleanupFailedUploads, uploadProductImages, productController.uploadProductImages);
router.delete('/:id/images', validateUUID('id'), productController.deleteProductImage);



// Export the router
module.exports = router;