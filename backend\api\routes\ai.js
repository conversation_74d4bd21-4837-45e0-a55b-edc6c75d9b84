const express = require('express');
const { protect } = require('../../middleware/auth');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// Cameroon-specific agricultural data
const cameroonCrops = {
  'clay': [
    {
      crop: 'Plantain',
      successProbability: 0.85,
      expectedYield: '15-25 tons/hectare',
      plantingTime: 'March-May (start of rainy season)',
      harvestTime: '12-15 months after planting',
      tips: [
        'Plant during the beginning of rainy season for best results',
        'Ensure good drainage to prevent root rot',
        'Space plants 3 meters apart in triangular pattern',
        'Apply organic manure every 3 months',
        'Protect from strong winds with windbreaks'
      ]
    },
    {
      crop: 'Cassava',
      successProbability: 0.90,
      expectedYield: '10-20 tons/hectare',
      plantingTime: 'March-June (rainy season)',
      harvestTime: '8-12 months after planting',
      tips: [
        'Very drought resistant, perfect for Cameroon climate',
        'Can grow in poor soils but yields better with fertilizer',
        'Plant stem cuttings 15-20cm long',
        'Harvest before next rainy season to avoid rot',
        'Store properly after harvest to prevent spoilage'
      ]
    },
    {
      crop: 'Cocoyam (Taro)',
      successProbability: 0.80,
      expectedYield: '8-15 tons/hectare',
      plantingTime: 'April-June (peak rainy season)',
      harvestTime: '6-9 months after planting',
      tips: [
        'Thrives in humid conditions typical of Cameroon',
        'Requires rich, well-drained organic soil',
        'Plant in raised beds for better drainage',
        'Regular watering needed during dry spells',
        'Harvest when leaves start yellowing'
      ]
    }
  ],
  'sandy': [
    {
      crop: 'Groundnuts (Peanuts)',
      successProbability: 0.85,
      expectedYield: '1.5-3 tons/hectare',
      plantingTime: 'April-May (early rainy season)',
      harvestTime: '3-4 months after planting',
      tips: [
        'Perfect for sandy soils in northern Cameroon',
        'Fixes nitrogen in soil, improving fertility',
        'Plant after first good rains',
        'Requires well-drained soil to prevent fungal diseases',
        'Harvest when pods rattle inside shells'
      ]
    },
    {
      crop: 'Sweet Potato',
      successProbability: 0.80,
      expectedYield: '8-15 tons/hectare',
      plantingTime: 'March-May',
      harvestTime: '4-6 months after planting',
      tips: [
        'Grows well in sandy soils with good drainage',
        'Plant vine cuttings 20-30cm long',
        'Ridge planting improves yields',
        'Drought tolerant once established',
        'Harvest before heavy rains to prevent rot'
      ]
    }
  ],
  'loamy': [
    {
      crop: 'Maize (Corn)',
      successProbability: 0.85,
      expectedYield: '3-6 tons/hectare',
      plantingTime: 'March-April and July-August',
      harvestTime: '3-4 months after planting',
      tips: [
        'Two growing seasons possible in Cameroon',
        'Plant at beginning of each rainy season',
        'Apply fertilizer at planting and 6 weeks later',
        'Control weeds early for best yields',
        'Harvest when kernels are hard and dry'
      ]
    },
    {
      crop: 'Beans',
      successProbability: 0.75,
      expectedYield: '1-2 tons/hectare',
      plantingTime: 'March-April and August-September',
      harvestTime: '2-3 months after planting',
      tips: [
        'Can be intercropped with maize',
        'Fixes nitrogen, improving soil fertility',
        'Requires well-drained soil',
        'Harvest when pods are dry and brown',
        'Store in dry conditions to prevent weevils'
      ]
    }
  ]
};

// AI Chatbot responses for common farming questions
const chatbotResponses = {
  'crop diseases': 'Common crop diseases in Cameroon include cassava mosaic virus, plantain black sigatoka, and maize streak virus. Prevention includes using disease-resistant varieties, proper spacing for air circulation, and removing infected plants immediately.',
  'soil improvement': 'To improve soil in Cameroon: 1) Add organic matter like compost or animal manure, 2) Practice crop rotation, 3) Use cover crops during fallow periods, 4) Apply lime to acidic soils, 5) Use green manure crops like legumes.',
  'market prices': 'Current market prices vary by region and season. Check local markets in Douala, Yaoundé, or your regional capital. Generally, prices are higher during off-seasons and lower during harvest periods.',
  'planting time': 'In Cameroon, plant most crops at the beginning of rainy seasons: March-May for main season, August-September for second season in some regions. Timing varies by location and crop type.',
  'fertilizer': 'Use organic fertilizers like compost and animal manure when possible. For chemical fertilizers, NPK 20-10-10 is good for most crops. Apply at planting and again 4-6 weeks later.'
};

// @desc    Get AI crop recommendations
// @route   POST /api/ai/crop-recommendations
// @access  Private
const getCropRecommendations = asyncHandler(async (req, res) => {
  const { location, soilType, farmSize, season, previousCrops } = req.body;

  // Get recommendations based on soil type
  let recommendations = cameroonCrops[soilType] || cameroonCrops['clay'];

  // Filter based on season if specified
  if (season === 'dry') {
    recommendations = recommendations.filter(crop => 
      crop.crop === 'Cassava' || crop.crop === 'Sweet Potato' || crop.crop === 'Groundnuts (Peanuts)'
    );
  }

  // Adjust success probability based on farm size
  recommendations = recommendations.map(crop => ({
    ...crop,
    successProbability: farmSize > 5 ? 
      Math.min(crop.successProbability + 0.05, 0.95) : 
      crop.successProbability
  }));

  // Add location-specific tips
  recommendations = recommendations.map(crop => ({
    ...crop,
    tips: [
      ...crop.tips,
      `Recommended for ${location} region`,
      'Consult local agricultural extension officers for specific advice'
    ]
  }));

  res.json({
    success: true,
    data: recommendations.slice(0, 5) // Return top 5 recommendations
  });
});

// @desc    Get AI chatbot response
// @route   POST /api/ai/chatbot
// @access  Private
const getChatbotResponse = asyncHandler(async (req, res) => {
  const { message, context } = req.body;
  
  const lowerMessage = message.toLowerCase();
  let response = '';

  // Simple keyword matching for farming questions
  if (lowerMessage.includes('disease') || lowerMessage.includes('pest')) {
    response = chatbotResponses['crop diseases'];
  } else if (lowerMessage.includes('soil') || lowerMessage.includes('fertilizer')) {
    response = chatbotResponses['soil improvement'];
  } else if (lowerMessage.includes('price') || lowerMessage.includes('market')) {
    response = chatbotResponses['market prices'];
  } else if (lowerMessage.includes('plant') || lowerMessage.includes('when')) {
    response = chatbotResponses['planting time'];
  } else if (lowerMessage.includes('cassava')) {
    response = 'Cassava is one of the best crops for Cameroon! It\'s drought-resistant, grows in poor soil, and can be harvested 8-12 months after planting. Plant stem cuttings during the rainy season (March-June) and harvest before the next rainy season to avoid rot.';
  } else if (lowerMessage.includes('plantain')) {
    response = 'Plantain is excellent for Cameroon\'s climate! Plant during March-May at the start of rainy season. It takes 12-15 months to harvest but yields 15-25 tons per hectare. Ensure good drainage and space plants 3 meters apart.';
  } else if (lowerMessage.includes('maize') || lowerMessage.includes('corn')) {
    response = 'Maize can be grown twice a year in Cameroon! Plant in March-April and again in July-August. It matures in 3-4 months and yields 3-6 tons per hectare. Apply fertilizer at planting and 6 weeks later for best results.';
  } else if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
    response = 'Hello! I\'m here to help you with farming questions specific to Cameroon. I can advise on crop selection, planting times, soil management, and agricultural practices suitable for our climate. What would you like to know?';
  } else {
    response = 'I can help you with farming questions about crops suitable for Cameroon, planting times, soil management, and agricultural practices. Could you ask me something specific about farming, such as "What crops grow best in clay soil?" or "When should I plant cassava?"';
  }

  res.json({
    success: true,
    response: response
  });
});

// @desc    Get market predictions (simplified)
// @route   POST /api/ai/market-predictions
// @access  Private
const getMarketPredictions = asyncHandler(async (req, res) => {
  const { farmerId } = req.body;

  // Simplified market predictions based on seasonal patterns
  const predictions = [
    {
      product: 'Plantain',
      predictedDemand: 'high',
      suggestedPrice: 150,
      confidence: 0.8,
      factors: ['Rainy season approaching', 'High urban demand', 'Limited supply from other regions']
    },
    {
      product: 'Cassava',
      predictedDemand: 'medium',
      suggestedPrice: 80,
      confidence: 0.7,
      factors: ['Stable year-round demand', 'Processing industry growth', 'Export opportunities']
    },
    {
      product: 'Cocoyam',
      predictedDemand: 'high',
      suggestedPrice: 200,
      confidence: 0.75,
      factors: ['Cultural food preferences', 'Limited seasonal availability', 'Urban market growth']
    }
  ];

  res.json({
    success: true,
    data: predictions
  });
});

// @desc    Analyze product image (placeholder)
// @route   POST /api/ai/image-analysis
// @access  Private
const analyzeProductImage = asyncHandler(async (req, res) => {
  // This would integrate with computer vision APIs
  // For now, return mock analysis
  
  const analysis = {
    quality: 'good',
    freshness: 85,
    defects: [],
    grade: 'A',
    suggestions: [
      'Product appears fresh and well-formed',
      'Good color and texture',
      'Suitable for premium pricing',
      'Consider highlighting freshness in product description'
    ]
  };

  res.json({
    success: true,
    data: analysis
  });
});

// @desc    Get farming advice based on weather
// @route   POST /api/ai/farming-advice
// @access  Private
const getFarmingAdvice = asyncHandler(async (req, res) => {
  const { location } = req.body;

  // Mock weather-based advice
  const advice = {
    currentWeather: {
      temperature: 28,
      humidity: 75,
      rainfall: 150,
      season: 'rainy'
    },
    advice: [
      'Good time for planting cassava and plantain',
      'Ensure proper drainage to prevent waterlogging',
      'Monitor crops for fungal diseases due to high humidity',
      'Apply organic mulch to retain soil moisture'
    ],
    warnings: [
      'Heavy rains expected this week - protect young plants',
      'High humidity may increase disease pressure'
    ],
    recommendations: [
      'Plant cover crops to prevent soil erosion',
      'Harvest mature crops before peak rains',
      'Prepare drainage channels around farm'
    ]
  };

  res.json({
    success: true,
    data: advice
  });
});

// Define routes
router.post('/crop-recommendations', protect, getCropRecommendations);
router.post('/chatbot', protect, getChatbotResponse);
router.post('/market-predictions', protect, getMarketPredictions);
router.post('/image-analysis', protect, analyzeProductImage);
router.post('/farming-advice', protect, getFarmingAdvice);

module.exports = router;
