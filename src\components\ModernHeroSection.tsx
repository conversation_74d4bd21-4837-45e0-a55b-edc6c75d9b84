import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  ArrowRight, 
  Leaf, 
  Users, 
  TrendingUp, 
  Shield,
  Clock,
  MapPin
} from 'lucide-react';

interface HeroSectionProps {
  title?: string;
  subtitle?: string;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void;
  onShopNow?: () => void;
  onMeetFarmers?: () => void;
  backgroundImage?: string;
  featuredProduct?: any;
  className?: string;
}

const ModernHeroSection: React.FC<HeroSectionProps> = ({
  title = "Welcome to Agri Connect Cameroon",
  subtitle = "Discover fresh produce, connect with trusted farmers, and enjoy a seamless farm-to-table experience.",
  searchPlaceholder = "Search for fresh products...",
  onSearch,
  onShopNow,
  onMeetFarmers,
  backgroundImage,
  featuredProduct,
  className = ''
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  const features = [
    {
      icon: <Shield className="w-5 h-5" />,
      text: "100% Secure delivery without contacting the courier"
    },
    {
      icon: <TrendingUp className="w-5 h-5" />,
      text: "Super Value Deals - Save more with coupons"
    },
    {
      icon: <Clock className="w-5 h-5" />,
      text: "Fresh products delivered within 24 hours"
    }
  ];

  return (
    <section className={`relative overflow-hidden ${className}`}>
      {/* Background with gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-900 via-emerald-800 to-green-900">
        {backgroundImage && (
          <img 
            src={backgroundImage} 
            alt="Hero background" 
            className="w-full h-full object-cover opacity-20"
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-r from-green-900/90 to-emerald-800/80" />
      </div>

      {/* Decorative elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-green-400/10 rounded-full blur-xl" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-emerald-400/10 rounded-full blur-xl" />
      <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-yellow-400/10 rounded-full blur-lg" />

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-white">
            {/* Top badge */}
            <Badge className="mb-6 bg-green-500/20 text-green-300 border-green-400/30 px-4 py-2 text-sm font-semibold">
              <Leaf className="w-4 h-4 mr-2" />
              Fresh from Cameroon Farms
            </Badge>

            {/* Main heading */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-black mb-6 leading-tight">
              <span className="text-white">Welcome to</span>
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-emerald-300">
                Agri Connect
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-green-100 mb-8 leading-relaxed max-w-lg">
              {subtitle}
            </p>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="mb-8">
              <div className="flex flex-col sm:flex-row gap-3 max-w-md">
                <div className="relative flex-1">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    type="text"
                    placeholder={searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-12 pr-4 py-3 bg-white/95 border-0 rounded-xl text-gray-900 placeholder-gray-500 focus:bg-white transition-all duration-200"
                  />
                </div>
                <Button 
                  type="submit"
                  className="bg-green-500 hover:bg-green-400 text-green-900 font-bold px-8 py-3 rounded-xl shadow-lg transition-all duration-300 hover:scale-105"
                >
                  Search
                </Button>
              </div>
            </form>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Button 
                size="lg" 
                onClick={onShopNow}
                className="bg-green-500 hover:bg-green-400 text-green-900 font-bold px-10 py-4 rounded-xl shadow-lg text-lg transition-all duration-300 hover:scale-105"
              >
                Shop Now
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={onMeetFarmers}
                className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-green-900 font-bold px-10 py-4 rounded-xl text-lg transition-all duration-300 hover:scale-105 bg-transparent"
              >
                Meet Farmers
                <Users className="w-5 h-5 ml-2" />
              </Button>
            </div>

            {/* Features */}
            <div className="space-y-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center text-green-100">
                  <div className="text-green-400 mr-3">
                    {feature.icon}
                  </div>
                  <span className="text-sm">{feature.text}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content - Deal Card */}
          <div className="lg:flex justify-center hidden">
            <div className="bg-white/95 backdrop-blur-sm rounded-3xl p-8 shadow-2xl max-w-sm w-full">
              {/* Deal Badge */}
              <Badge className="mb-4 bg-red-500 text-white font-bold px-3 py-1 rounded-full">
                Deal of the Day
              </Badge>

              {/* Product Image */}
              <div className="relative mb-6">
                <img
                  src={featuredProduct?.images && featuredProduct.images.length > 0 ? featuredProduct.images[0] : '/placeholder.svg'}
                  alt={featuredProduct?.name || "Featured product"}
                  className="w-full h-48 object-cover rounded-2xl"
                />
                <Badge className="absolute top-3 left-3 bg-orange-500 text-white font-bold px-2 py-1 rounded-full text-xs">
                  26% Off
                </Badge>
              </div>

              {/* Product Info */}
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {featuredProduct?.name || "Organic fruit for your family's health"}
              </h3>

              <div className="flex items-center gap-2 mb-4">
                <span className="text-2xl font-bold text-green-600">
                  {featuredProduct?.price ? `${featuredProduct.price.toLocaleString()} FCFA` : '$38'}
                </span>
                {featuredProduct?.price && (
                  <span className="text-lg text-gray-400 line-through">
                    {Math.round(featuredProduct.price * 1.3).toLocaleString()} FCFA
                  </span>
                )}
              </div>

              {/* Countdown Timer */}
              <div className="bg-gray-100 rounded-xl p-4 mb-6">
                <p className="text-sm text-gray-600 mb-2">Hurry Up! Offer End In:</p>
                <div className="flex gap-2 text-center">
                  <div className="bg-green-600 text-white rounded-lg px-2 py-1 text-xs font-bold">
                    03<br />days
                  </div>
                  <div className="bg-green-600 text-white rounded-lg px-2 py-1 text-xs font-bold">
                    02<br />hours
                  </div>
                  <div className="bg-green-600 text-white rounded-lg px-2 py-1 text-xs font-bold">
                    43<br />mins
                  </div>
                  <div className="bg-green-600 text-white rounded-lg px-2 py-1 text-xs font-bold">
                    29<br />sec
                  </div>
                </div>
              </div>

              {/* Shop Button */}
              <Button className="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 rounded-xl">
                Shop Now
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ModernHeroSection;
