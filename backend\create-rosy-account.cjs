// Create <PERSON><PERSON>'s account in database
const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

async function createRosyAccount() {
  console.log('👤 Creating <PERSON><PERSON>\'s Account...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    // Check if user already exists
    const [existingUsers] = await connection.execute(`
      SELECT id, email FROM users WHERE email = '<EMAIL>'
    `);
    
    if (existingUsers.length > 0) {
      console.log('✅ User already exists!');
      console.log(`   User ID: ${existingUsers[0].id}`);
      console.log(`   Email: ${existingUsers[0].email}`);
    } else {
      // Create new user
      const userId = require('crypto').randomUUID();
      const hashedPassword = await bcrypt.hash('Test123!', 12);
      
      await connection.execute(`
        INSERT INTO users (
          id, firstName, lastName, email, password, role, 
          isEmailVerified, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        userId,
        'Rosy',
        'Wenga', 
        '<EMAIL>',
        hashedPassword,
        'CUSTOMER',
        true
      ]);
      
      console.log('✅ User account created successfully!');
      console.log(`   User ID: ${userId}`);
      console.log(`   Name: Rosy Wenga`);
      console.log(`   Email: <EMAIL>`);
      console.log(`   Password: Test123!`);
      console.log(`   Role: CUSTOMER`);
      
      // Create a cart for the user
      const cartId = require('crypto').randomUUID();
      await connection.execute(`
        INSERT INTO carts (id, userId, total, itemCount, createdAt, updatedAt)
        VALUES (?, ?, 0, 0, NOW(), NOW())
      `, [cartId, userId]);
      
      console.log(`✅ Cart created: ${cartId}`);
    }
    
    await connection.end();
    console.log('\n🎉 Account setup completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Go to: http://localhost:5173/login');
    console.log('2. Login with:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Test123!');
    console.log('3. Try adding products to cart - should work now!');
    
  } catch (error) {
    console.error('❌ Failed to create account:', error.message);
  }
}

createRosyAccount();
