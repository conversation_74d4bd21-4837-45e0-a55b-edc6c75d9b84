const { Vendor, ProductListing, Order } = require('../../data/models');

exports.getDashboard = async () => {
  const vendorCount = await Vendor.count();
  const productCount = await ProductListing.count();
  const orderCount = await Order.count();
  const totalRevenue = await Order.sum('totalAmount');

  return { vendorCount, productCount, orderCount, totalRevenue };
};

exports.updateListingQuality = async (listingId, qualityScore) => {
  const listing = await ProductListing.findByPk(listingId);
  if (!listing) throw new Error('Listing not found');
  await listing.update({ qualityScore });
  return listing;
};

exports.getLowQualityListings = async () => {
  const listings = await ProductListing.findAll({
    where: { qualityScore: { [Op.lt]: 3 } },
    include: [{ model: Vendor, attributes: ['companyName'] }]
  });
  return listings;
};

exports.approveVendor = async (vendorId) => {
  const vendor = await Vendor.findByPk(vendorId);
  if (!vendor) throw new Error('Vendor not found');
  await vendor.update({ isApproved: true });
  return vendor;
};