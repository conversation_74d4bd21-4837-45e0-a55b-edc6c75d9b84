class Review {
    constructor(id, userId, productId, rating, comment, createdAt) {
      this.id = id;
      this.userId = userId;
      this.productId = productId;
      this.rating = rating;
      this.comment = comment;
      this.createdAt = createdAt;
    }
  
    isValid() {
      return this.rating >= 1 && this.rating <= 5;
    }
  
    updateRating(newRating) {
      if (newRating >= 1 && newRating <= 5) {
        this.rating = newRating;
      } else {
        throw new Error('Invalid rating. Must be between 1 and 5.');
      }
    }
  
    updateComment(newComment) {
      this.comment = newComment;
    }
  
    toJSON() {
      return {
        id: this.id,
        userId: this.userId,
        productId: this.productId,
        rating: this.rating,
        comment: this.comment,
        createdAt: this.createdAt
      };
    }
  }
  
  module.exports = Review;