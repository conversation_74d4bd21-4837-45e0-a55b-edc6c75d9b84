const express = require('express');
const bcrypt = require('bcryptjs');
const { User, Farmer } = require('../data/models');
const { generateToken, generateRefreshToken, protect } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
const register = asyncHandler(async (req, res) => {
  const { firstName, lastName, email, phone, password, role = 'CUSTOMER', farmName, farmLocation } = req.body;

  // Validation
  if (!firstName || !lastName || !email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Please provide firstName, lastName, email, and password'
    });
  }

  // Validate role
  const validRoles = ['CUSTOMER', 'FARMER', 'ADMIN', 'DELIVERY_AGENT', 'NUTRITIONIST'];
  if (!validRoles.includes(role)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid role specified'
    });
  }

  // Additional validation for farmer role
  if (role === 'FARMER' && (!farmName || !farmLocation)) {
    return res.status(400).json({
      success: false,
      message: 'Farm name and location are required for farmer registration'
    });
  }

  // Check if user already exists
  const existingUser = await User.findOne({
    where: {
      email: email.toLowerCase()
    }
  });

  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: 'User with this email already exists'
    });
  }

  // Check if phone number already exists (if provided)
  if (phone) {
    const existingPhone = await User.findOne({
      where: { phone }
    });

    if (existingPhone) {
      return res.status(400).json({
        success: false,
        message: 'User with this phone number already exists'
      });
    }
  }

  // Hash password
  const salt = await bcrypt.genSalt(12);
  const hashedPassword = await bcrypt.hash(password, salt);

  // Create user
  const user = await User.create({
    firstName,
    lastName,
    email: email.toLowerCase(),
    phone,
    password: hashedPassword,
    role,
    status: 'ACTIVE', // Set to active for now, can be changed to PENDING_VERIFICATION for email verification
    emailVerified: false,
    phoneVerified: false
  });

  // If user is a farmer, create farmer profile
  let farmerProfile = null;
  if (role === 'FARMER') {
    farmerProfile = await Farmer.create({
      userId: user.id,
      farmName,
      farmLocation,
      verified: false
    });
  }

  // Generate tokens
  const accessToken = generateToken(user.id);
  const refreshToken = generateRefreshToken(user.id);

  // Remove password from response
  const userResponse = {
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    phone: user.phone,
    role: user.role,
    status: user.status,
    emailVerified: user.emailVerified,
    phoneVerified: user.phoneVerified,
    createdAt: user.createdAt,
    farmerProfile: farmerProfile ? {
      id: farmerProfile.id,
      farmName: farmerProfile.farmName,
      farmLocation: farmerProfile.farmLocation,
      verified: farmerProfile.verified
    } : null
  };

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user: userResponse,
      accessToken,
      refreshToken
    }
  });
});

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Validation
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Please provide email and password'
    });
  }

  // Check if user exists and include farmer profile if applicable
  const user = await User.findOne({
    where: {
      email: email.toLowerCase()
    },
    include: [
      {
        model: Farmer,
        required: false
      }
    ]
  });

  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Invalid email or password'
    });
  }

  // Check if user account is active
  if (user.status === 'SUSPENDED') {
    return res.status(403).json({
      success: false,
      message: 'Your account has been suspended. Please contact support.'
    });
  }

  if (user.status === 'INACTIVE') {
    return res.status(403).json({
      success: false,
      message: 'Your account is inactive. Please contact support.'
    });
  }

  // Check password
  const isPasswordValid = await bcrypt.compare(password, user.password);

  if (!isPasswordValid) {
    return res.status(401).json({
      success: false,
      message: 'Invalid email or password'
    });
  }

  // Update last login
  await user.update({ lastLogin: new Date() });

  // Generate tokens
  const accessToken = generateToken(user.id);
  const refreshToken = generateRefreshToken(user.id);

  // Remove password from response
  const userResponse = {
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    phone: user.phone,
    role: user.role,
    status: user.status,
    emailVerified: user.emailVerified,
    phoneVerified: user.phoneVerified,
    lastLogin: user.lastLogin,
    createdAt: user.createdAt,
    farmerProfile: user.Farmer ? {
      id: user.Farmer.id,
      farmName: user.Farmer.farmName,
      farmLocation: user.Farmer.farmLocation,
      farmDescription: user.Farmer.farmDescription,
      verified: user.Farmer.verified,
      rating: user.Farmer.rating
    } : null
  };

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: userResponse,
      accessToken,
      refreshToken
    }
  });
});

// @desc    Get current user profile
// @route   GET /api/auth/profile
// @access  Private
const getProfile = asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    attributes: { exclude: ['password'] },
    include: [
      {
        model: Farmer,
        required: false
      }
    ]
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const userResponse = {
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    phone: user.phone,
    avatar: user.avatar,
    role: user.role,
    status: user.status,
    emailVerified: user.emailVerified,
    phoneVerified: user.phoneVerified,
    lastLogin: user.lastLogin,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
    farmerProfile: user.Farmer ? {
      id: user.Farmer.id,
      farmName: user.Farmer.farmName,
      farmLocation: user.Farmer.farmLocation,
      farmDescription: user.Farmer.farmDescription,
      farmSize: user.Farmer.farmSize,
      verified: user.Farmer.verified,
      rating: user.Farmer.rating,
      totalSales: user.Farmer.totalSales
    } : null
  };

  res.json({
    success: true,
    data: {
      user: userResponse
    }
  });
});

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private
const updateProfile = asyncHandler(async (req, res) => {
  const { firstName, lastName, phone, avatar, farmName, farmLocation, farmDescription } = req.body;

  const user = await User.findByPk(req.user.id, {
    include: [
      {
        model: Farmer,
        required: false
      }
    ]
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Update user fields
  if (firstName) user.firstName = firstName;
  if (lastName) user.lastName = lastName;
  if (phone) {
    // Check if phone number is already taken by another user
    const existingPhone = await User.findOne({
      where: {
        phone,
        id: { [require('sequelize').Op.ne]: user.id }
      }
    });

    if (existingPhone) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is already taken'
      });
    }
    user.phone = phone;
  }
  if (avatar) user.avatar = avatar;

  await user.save();

  // Update farmer profile if user is a farmer
  if (user.role === 'FARMER' && user.Farmer) {
    if (farmName) user.Farmer.farmName = farmName;
    if (farmLocation) user.Farmer.farmLocation = farmLocation;
    if (farmDescription) user.Farmer.farmDescription = farmDescription;

    await user.Farmer.save();
  }

  // Reload user with updated farmer profile
  await user.reload({
    include: [
      {
        model: Farmer,
        required: false
      }
    ]
  });

  const userResponse = {
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    phone: user.phone,
    avatar: user.avatar,
    role: user.role,
    status: user.status,
    emailVerified: user.emailVerified,
    phoneVerified: user.phoneVerified,
    updatedAt: user.updatedAt,
    farmerProfile: user.Farmer ? {
      id: user.Farmer.id,
      farmName: user.Farmer.farmName,
      farmLocation: user.Farmer.farmLocation,
      farmDescription: user.Farmer.farmDescription,
      verified: user.Farmer.verified,
      rating: user.Farmer.rating
    } : null
  };

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: {
      user: userResponse
    }
  });
});

// @desc    Change password
// @route   PUT /api/auth/change-password
// @access  Private
const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      message: 'Please provide current password and new password'
    });
  }

  const user = await User.findByPk(req.user.id);

  // Check current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);

  if (!isCurrentPasswordValid) {
    return res.status(400).json({
      success: false,
      message: 'Current password is incorrect'
    });
  }

  // Hash new password
  const salt = await bcrypt.genSalt(12);
  const hashedNewPassword = await bcrypt.hash(newPassword, salt);

  user.password = hashedNewPassword;
  await user.save();

  res.json({
    success: true,
    message: 'Password changed successfully'
  });
});

// @desc    Logout user (invalidate token)
// @route   POST /api/auth/logout
// @access  Private
const logout = asyncHandler(async (req, res) => {
  // In a real app, you'd add the token to a blacklist
  // For now, we'll just send a success response
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Routes
router.post('/register', register);
router.post('/login', login);
router.get('/profile', protect, getProfile);
router.put('/profile', protect, updateProfile);
router.put('/change-password', protect, changePassword);
router.post('/logout', protect, logout);

module.exports = router;
