// Test authentication and order creation fix
const axios = require('axios');

async function testAuthFix() {
  console.log('🔐 Testing Authentication Fix for Order Creation...\n');
  
  try {
    // Step 1: Register a test user
    console.log('1. Registering test user...');
    const email = `auth.test.${Date.now()}@example.com`;
    
    const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
      firstName: 'Auth',
      lastName: 'Test',
      email: email,
      password: 'Test123!',
      role: 'CUSTOMER'
    });
    
    if (!registerResponse.data.success) {
      console.log('❌ Registration failed:', registerResponse.data.message);
      return;
    }
    
    const token = registerResponse.data.data.accessToken;
    console.log('✅ User registered successfully');
    console.log(`   Token: ${token.substring(0, 20)}...`);
    
    // Step 2: Test token validation
    console.log('\n2. Testing token validation...');
    try {
      const profileResponse = await axios.get('http://localhost:3001/api/auth/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (profileResponse.data.success) {
        console.log('✅ Token is valid');
        console.log(`   User: ${profileResponse.data.data.user.firstName} ${profileResponse.data.data.user.lastName}`);
      } else {
        console.log('❌ Token validation failed');
        return;
      }
    } catch (error) {
      console.log('❌ Token validation error:', error.response?.data?.message || error.message);
      return;
    }
    
    // Step 3: Add products to cart
    console.log('\n3. Adding products to cart...');
    const productsResponse = await axios.get('http://localhost:3001/api/products');
    const products = productsResponse.data.data.products;
    
    if (products.length === 0) {
      console.log('❌ No products available for testing');
      return;
    }
    
    const testProduct = products[0];
    console.log(`   Using product: ${testProduct.name}`);
    
    try {
      const cartResponse = await axios.post('http://localhost:3001/api/cart/add', {
        productId: testProduct.id,
        quantity: 2
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (cartResponse.data.success) {
        console.log('✅ Product added to cart');
      } else {
        console.log('❌ Failed to add to cart:', cartResponse.data.message);
        return;
      }
    } catch (error) {
      console.log('❌ Cart error:', error.response?.data?.message || error.message);
      return;
    }
    
    // Step 4: Test order creation (the main issue)
    console.log('\n4. Testing order creation...');
    const orderData = {
      shippingAddress: {
        firstName: 'Auth',
        lastName: 'Test',
        address: '123 Test Street',
        city: 'Douala',
        region: 'Littoral',
        postalCode: '12345',
        phone: '+237 677 123 456',
        email: email
      },
      paymentMethod: 'cod',
      phoneNumber: '+237 677 123 456',
      notes: 'Test order for auth fix'
    };
    
    try {
      const orderResponse = await axios.post('http://localhost:3001/api/orders/create', orderData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (orderResponse.data.success) {
        console.log('✅ Order created successfully!');
        console.log(`   Order ID: ${orderResponse.data.data.order.id}`);
        console.log(`   Order Number: ${orderResponse.data.data.order.orderNumber}`);
        console.log(`   Total: ${orderResponse.data.data.order.totalAmount} FCFA`);
      } else {
        console.log('❌ Order creation failed:', orderResponse.data.message);
      }
    } catch (error) {
      console.log('❌ Order creation error:', error.response?.status, error.response?.data?.message || error.message);
      
      if (error.response?.status === 401) {
        console.log('🔍 This is the 401 Unauthorized error you were seeing');
        console.log('💡 Possible causes:');
        console.log('   - Token not being sent correctly');
        console.log('   - Token expired or invalid');
        console.log('   - Backend auth middleware issue');
        console.log('   - User not properly authenticated');
      }
      return;
    }
    
    // Step 5: Test getting orders
    console.log('\n5. Testing order retrieval...');
    try {
      const ordersResponse = await axios.get('http://localhost:3001/api/orders', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (ordersResponse.data.success) {
        console.log(`✅ Retrieved ${ordersResponse.data.data.orders.length} orders`);
      } else {
        console.log('❌ Failed to retrieve orders');
      }
    } catch (error) {
      console.log('❌ Orders retrieval error:', error.response?.data?.message || error.message);
    }
    
    // Step 6: Summary
    console.log('\n🎉 Authentication Test Summary:');
    console.log('✅ User registration: Working');
    console.log('✅ Token validation: Working');
    console.log('✅ Cart operations: Working');
    console.log('✅ Order creation: Working');
    console.log('✅ Order retrieval: Working');
    
    console.log('\n💡 Frontend Fix Applied:');
    console.log('   - CheckoutPage now uses authService.authenticatedFetch()');
    console.log('   - Proper token handling and error management');
    console.log('   - Automatic redirect to login on 401 errors');
    
    console.log('\n🧪 Test Your Frontend:');
    console.log('   1. Login to your account');
    console.log('   2. Add products to cart');
    console.log('   3. Go to checkout page');
    console.log('   4. Fill out shipping info');
    console.log('   5. Place order - should work without 401 error');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the backend server is running:');
      console.log('   cd backend && node server.js');
    }
  }
}

testAuthFix();
