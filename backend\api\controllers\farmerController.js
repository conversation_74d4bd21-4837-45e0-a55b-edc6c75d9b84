const farmerService = require('../../business/services/farmerService');

exports.listFarmers = async (req, res, next) => {
  try {
    const farmers = await farmerService.listFarmers(req.query);
    res.json(farmers);
  } catch (error) {
    next(error);
  }
};

exports.getFarmerById = async (req, res, next) => {
  try {
    const farmer = await farmerService.getFarmerById(req.params.id);
    res.json(farmer);
  } catch (error) {
    next(error);
  }
};

exports.getFarmerProducts = async (req, res, next) => {
  try {
    const products = await farmerService.getFarmerProducts(req.params.id);
    res.json(products);
  } catch (error) {
    next(error);
  }
};

exports.getFarmerDashboard = async (req, res, next) => {
  try {
    const dashboard = await farmerService.getFarmerDashboard(req.user.id);
    res.json(dashboard);
  } catch (error) {
    next(error);
  }
};