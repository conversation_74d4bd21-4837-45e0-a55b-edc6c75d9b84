const { User } = require('../models');

class UserRepository {
  async create(userData) {
    return await User.create(userData);
  }

  async findById(id) {
    return await User.findByPk(id);
  }

  async findByEmail(email) {
    return await User.findOne({ where: { email } });
  }

  async update(id, userData) {
    const [updatedRowsCount, updatedUsers] = await User.update(userData, {
      where: { id },
      returning: true,
    });
    return updatedUsers[0];
  }

  async delete(id) {
    return await User.destroy({ where: { id } });
  }

  async findAll(options = {}) {
    return await User.findAll(options);
  }
}

module.exports = new UserRepository();