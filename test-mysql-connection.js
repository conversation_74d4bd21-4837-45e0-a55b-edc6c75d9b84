// Test MySQL connection with Laragon
const mysql = require('mysql2/promise');

async function testConnection() {
  console.log(' Testing MySQL connection with Laragon...\n');
  
  try {
    // Test connection with root user (no password)
    console.log(' Attempting to connect to MySQL...');
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '', // Empty password for Laragon default
      database: 'cameroon_farm_connect'
    });
    
    console.log(' MySQL connection successful!');
    
    // Test database access
    console.log('\n Testing database access...');
    const [rows] = await connection.execute('SELECT DATABASE() as current_db');
    console.log(' Current database:', rows[0].current_db);
    
    // Show tables
    console.log('\n Checking existing tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    if (tables.length > 0) {
      console.log(' Existing tables:');
      tables.forEach(table => {
        console.log(`   - ${Object.values(table)[0]}`);
      });
    } else {
      console.log(' No tables found (will be created by <PERSON><PERSON><PERSON>)');
    }
    
    // Test with dedicated user if exists
    console.log('\n Testing dedicated user connection...');
    try {
      const userConnection = await mysql.createConnection({
        host: 'localhost',
        port: 3306,
        user: 'farm_connect_user',
        password: 'farm_connect_2024',
        database: 'cameroon_farm_connect'
      });
      
      console.log(' Dedicated user connection successful!');
      await userConnection.end();
    } catch (userError) {
      console.log(' Dedicated user connection failed (using root is fine)');
    }
    
    await connection.end();
    console.log('\n MySQL connection test completed successfully!');
    
  } catch (error) {
    console.error(' MySQL connection failed:');
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n Troubleshooting tips:');
      console.log('1. Make sure Laragon is running');
      console.log('2. Check that MySQL service is started (green in Laragon)');
      console.log('3. Verify MySQL is running on port 3306');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n Authentication issue:');
      console.log('1. Check username and password');
      console.log('2. Try with empty password for root user');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n Database not found:');
      console.log('1. Make sure database "cameroon_farm_connect" exists');
      console.log('2. Check database name spelling');
    }
  }
}

testConnection();
