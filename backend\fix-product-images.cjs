// Fix product images with real corresponding photos
const mysql = require('mysql2/promise');

async function fixProductImages() {
  console.log('🖼️ Fixing Product Images with Real Corresponding Photos...\n');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'cameroon_farm_connect'
    });
    
    console.log('✅ Connected to database');
    
    // Define REAL, SPECIFIC images for each product - CAREFULLY SELECTED MATCHING PHOTOS
    const correctProductImages = {
      // Root Crops & Tubers - CAREFULLY SELECTED REAL IMAGES
      'Fresh Cassava': ['https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=500&h=400&fit=crop&q=80'], // Cassava roots
      'Water Yam': ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80'], // Yam tubers
      'Cocoyam': ['https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=500&h=400&fit=crop&q=80'], // Taro/Cocoyam
      'Sweet Potatoes': ['https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=500&h=400&fit=crop&q=80'], // Sweet potatoes
      'Sweet Yam': ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80'], // Yam varieties

      // Vegetables - CAREFULLY SELECTED REAL IMAGES
      'Fresh Okra': ['https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=500&h=400&fit=crop&q=80'], // Okra pods
      'Red Onions': ['https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=500&h=400&fit=crop&q=80'], // Red onions
      'Fresh Tomatoes': ['https://images.unsplash.com/photo-1546470427-e5380e0e8b5a?w=500&h=400&fit=crop&q=80'], // Fresh tomatoes
      'Fresh Garlic': ['https://images.unsplash.com/photo-1553978297-833d24758ba5?w=500&h=400&fit=crop&q=80'], // Garlic bulbs
      'Fresh Basil': ['https://images.unsplash.com/photo-1618375569909-3c8616cf7733?w=500&h=400&fit=crop&q=80'], // Basil leaves

      // Legumes & Beans - CAREFULLY SELECTED REAL IMAGES
      'Green Beans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'], // Green beans
      'Lima Beans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'], // Lima beans
      'White Beans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'], // White beans
      'Soybeans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'], // Soybeans
      'Red Kidney Beans': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'], // Kidney beans
      'Black-eyed Peas': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'], // Black-eyed peas
      'Groundnuts': ['https://images.unsplash.com/photo-1608797178974-15b35a64ede9?w=500&h=400&fit=crop&q=80'], // Peanuts/Groundnuts

      // Fruits - CAREFULLY SELECTED REAL IMAGES
      'Lemons': ['https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=500&h=400&fit=crop&q=80'], // Lemons
      'Bush Mango': ['https://images.unsplash.com/photo-1553279768-865429fa0078?w=500&h=400&fit=crop&q=80'], // Wild mango

      // Cereals & Grains - CAREFULLY SELECTED REAL IMAGES
      'Fonio Grain': ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80'], // Fonio grains
      'Red Sorghum': ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80'], // Red sorghum
      'Pearl Millet': ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=400&fit=crop&q=80'], // Pearl millet

      // Cash Crops - CAREFULLY SELECTED REAL IMAGES
      'Palm Kernels': ['https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80'], // Palm kernels
      'Palm Oil': ['https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80'], // Palm oil
      'Raw Cotton': ['https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=400&fit=crop&q=80'], // Cotton
      'Coffee Cherries': ['https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=500&h=400&fit=crop&q=80'], // Coffee cherries

      // Spices & Herbs - CAREFULLY SELECTED REAL IMAGES
      'Hot Pepper': ['https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=500&h=400&fit=crop&q=80'], // Hot peppers
      'Fresh Ginger': ['https://images.unsplash.com/photo-1599639832862-bd197d5c1b5d?w=500&h=400&fit=crop&q=80'] // Ginger root
    };
    
    // Get all products that need fixing
    const [products] = await connection.execute('SELECT id, name FROM products');
    console.log(`\n📦 Found ${products.length} products to check`);
    
    let updatedCount = 0;
    let notFoundCount = 0;
    
    // Update each product with correct image
    for (const product of products) {
      const productName = product.name;
      
      if (correctProductImages[productName]) {
        const imageUrl = correctProductImages[productName][0];
        
        try {
          // Update product with correct image
          await connection.execute(`
            UPDATE products 
            SET images = ? 
            WHERE id = ?
          `, [JSON.stringify([imageUrl]), product.id]);
          
          updatedCount++;
          console.log(`   ✅ Updated: ${productName}`);
          
        } catch (error) {
          console.log(`   ❌ Failed to update ${productName}:`, error.message);
        }
      } else {
        notFoundCount++;
        console.log(`   ⚠️ No specific image for: ${productName}`);
      }
    }
    
    console.log(`\n🎉 Image fix completed!`);
    console.log(`   Updated with correct images: ${updatedCount} products`);
    console.log(`   No specific images found: ${notFoundCount} products`);
    console.log(`   Total products: ${products.length}`);
    
    // Show which products were updated
    console.log('\n📝 Products updated with correct images:');
    Object.keys(correctProductImages).forEach(productName => {
      console.log(`   ✅ ${productName}`);
    });
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Image fix failed:', error.message);
  }
  
  process.exit(0);
}

fixProductImages();
