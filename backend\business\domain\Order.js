class Order {
    constructor(id, userId, items, status, total, createdAt) {
      this.id = id;
      this.userId = userId;
      this.items = items; // Array of OrderItem objects
      this.status = status;
      this.total = total;
      this.createdAt = createdAt;
    }
  
    calculateTotal() {
      this.total = this.items.reduce((sum, item) => sum + item.subtotal, 0);
    }
  
    addItem(item) {
      this.items.push(item);
      this.calculateTotal();
    }
  
    removeItem(itemId) {
      this.items = this.items.filter(item => item.id !== itemId);
      this.calculateTotal();
    }
  
    updateStatus(newStatus) {
      this.status = newStatus;
    }
  
    canBeCancelled() {
      return ['pending', 'processing'].includes(this.status);
    }
  
    toJSON() {
      return {
        id: this.id,
        userId: this.userId,
        items: this.items.map(item => item.toJSON()),
        status: this.status,
        total: this.total,
        createdAt: this.createdAt,
        canBeCancelled: this.canBeCancelled()
      };
    }
  }
  
  module.exports = Order;