const { Sequelize } = require('sequelize');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

dotenv.config();

// Create database directory for SQLite if it doesn't exist
const dbDir = path.join(__dirname, '..', 'database');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

console.log('Database Configuration:');
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_HOST:', process.env.DB_HOST);

// Try MySQL first, fallback to SQLite if MySQL fails
let sequelize;
const useMySQL = process.env.DB_DIALECT === 'mysql' && process.env.DB_NAME && process.env.DB_USER;

if (useMySQL) {
  console.log(' Attempting to connect to MySQL...');
  sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      dialect: 'mysql',
      logging: process.env.DB_LOGGING === 'true' ? console.log : false,
      pool: {
        max: parseInt(process.env.DB_POOL_MAX) || 5,
        min: parseInt(process.env.DB_POOL_MIN) || 0,
        acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 30000,
        idle: parseInt(process.env.DB_POOL_IDLE) || 10000
      }
    }
  );
} else {
  console.log(' Using SQLite database (fallback)...');
  sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: path.join(dbDir, 'cameroon_farm_connect.sqlite'),
    logging: process.env.DB_LOGGING === 'true' ? console.log : false,
  });
}

let isConnected = false;

const connectDB = async () => {
  if (isConnected) return sequelize;

  try {
    await sequelize.authenticate();
    const dialect = sequelize.getDialect();
    console.log(` ${dialect.toUpperCase()} database connected successfully`);

    if (dialect === 'sqlite') {
      console.log(' Note: Using SQLite for development. Install MySQL for production.');
    }

    isConnected = true;
    return sequelize;
  } catch (error) {
    console.error(' Error connecting to database:', error);

    // If MySQL fails, try SQLite as fallback
    if (sequelize.getDialect() === 'mysql') {
      console.log(' MySQL connection failed, switching to SQLite...');

      try {
        // Create new SQLite instance
        const sqliteSequelize = new Sequelize({
          dialect: 'sqlite',
          storage: path.join(dbDir, 'cameroon_farm_connect.sqlite'),
          logging: process.env.DB_LOGGING === 'true' ? console.log : false,
        });

        await sqliteSequelize.authenticate();
        console.log(' SQLite database connected successfully (fallback)');

        // Replace the global sequelize instance
        sequelize = sqliteSequelize;
        isConnected = true;
        return sequelize;
      } catch (sqliteError) {
        console.error(' SQLite fallback also failed:', sqliteError);
      }
    }

    console.error(' All database connection attempts failed');
    process.exit(1);
  }
};

// Function to get the current sequelize instance
const getSequelize = () => {
  return sequelize;
};

module.exports = {
  connectDB,
  sequelize,
  getSequelize
};

