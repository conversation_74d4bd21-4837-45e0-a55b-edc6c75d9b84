const { body } = require('express-validator');
const validate = require('../middleware/validate');
exports.validateVendor = [
  body('companyName').notEmpty().withMessage('Company name is required'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters')
];

exports.validateProductListing = [
  body('productId').notEmpty().withMessage('Product ID is required'),
  body('price').isNumeric().withMessage('Price must be a number'),
  body('quantity').isInt({ min: 0 }).withMessage('Quantity must be a non-negative integer')
];